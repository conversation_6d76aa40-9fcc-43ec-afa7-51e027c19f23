# Contributing to WeLikeMoney

Thank you for your interest in contributing to WeLikeMoney! This document outlines the guidelines and best practices for contributing to this project.

## Code Quality Guidelines

### Linter Rules

**IMPORTANT: Always check the linter and never commit changes while there are still linter errors.**

Before submitting any code changes:

1. Run `flutter analyze` to check for linter errors
2. Fix ALL linter errors before committing
3. Ensure your code passes all tests with `flutter test`

A pre-commit hook has been set up to prevent commits with linter errors. If your commit is rejected, run `flutter analyze` to see the issues that need to be fixed.

### Code Style

- Follow the [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Follow the naming conventions that are customary for Flutter
- Keep methods small and focused on a single responsibility
- Document public APIs with dartdoc comments

## Git Workflow

### Commit Message Format

Use the following prefixes for commit messages:

- `fix:` for bug fixes
- `feat:` for new features
- `perf:` for performance improvements
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding missing tests
- `chore:` for maintenance tasks

Examples:
```
feat: add account creation screen
fix: resolve null pointer in payment processing
docs: update README with setup instructions
```

### Pull Request Process

1. Create a new branch for your feature or bugfix
2. Make your changes, ensuring all linter checks pass
3. Write or update tests as necessary
4. Update documentation to reflect any changes
5. Submit a pull request with a clear description of the changes

## Error Handling

- Implement proper error boundaries
- Log errors appropriately for debugging
- Provide user-friendly error messages
- Handle network failures gracefully

## Documentation

- Maintain clear documentation with setup instructions
- Document API interactions and data flows
- Keep pubspec.yaml well-documented
- Document permission requirements

## Dependencies

Remember to add new Dart or Flutter packages to pubspec.yaml if you suggest to use them.

---

By following these guidelines, you help maintain the quality and consistency of the WeLikeMoney codebase. Thank you for your contributions! 