-- Check if each column exists and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendor_invoices') THEN
        CREATE TABLE vendor_invoices (
            invoice_id SERIAL PRIMARY KEY,
            vendor_id VARCHAR REFERENCES vendors(vendor_id) ON DELETE CASCADE,
            invoice_number VA<PERSON>HAR NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE NOT NULL,
            amount DECIMAL NOT NULL,
            currency_code VARCHAR REFERENCES currencies(currency_code),
            expense_account_number VARCHAR REFERENCES accounts(account_number),
            tax_amount DECIMAL,
            project_id INTEGER REFERENCES projects(project_id),
            staff_id INTEGER REFERENCES staff_members(staff_id),
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE,
            is_paid BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Enable RLS on vendor_invoices table
        ALTER TABLE vendor_invoices ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for authenticated users to select vendor invoices
        CREATE POLICY vendor_invoices_select ON vendor_invoices
            FOR SELECT
            USING (true);
            
        -- Create policy for authenticated users to insert vendor invoices
        CREATE POLICY vendor_invoices_insert ON vendor_invoices
            FOR INSERT
            WITH CHECK (true);
            
        -- Create policy for authenticated users to update vendor invoices
        CREATE POLICY vendor_invoices_update ON vendor_invoices
            FOR UPDATE
            USING (true)
            WITH CHECK (true);
            
        -- Create policy for authenticated users to delete vendor invoices
        CREATE POLICY vendor_invoices_delete ON vendor_invoices
            FOR DELETE
            USING (true);
            
        -- Add comments to describe the table and fields
        COMMENT ON TABLE vendor_invoices IS 'Vendor invoices for accounts payable operations';
        COMMENT ON COLUMN vendor_invoices.invoice_id IS 'Unique invoice identifier';
        COMMENT ON COLUMN vendor_invoices.vendor_id IS 'Reference to the vendor';
        COMMENT ON COLUMN vendor_invoices.invoice_number IS 'Vendor invoice number';
        COMMENT ON COLUMN vendor_invoices.invoice_date IS 'Date of the invoice';
        COMMENT ON COLUMN vendor_invoices.due_date IS 'Due date for payment';
        COMMENT ON COLUMN vendor_invoices.amount IS 'Invoice amount';
        COMMENT ON COLUMN vendor_invoices.currency_code IS 'Currency code for the amount';
        COMMENT ON COLUMN vendor_invoices.expense_account_number IS 'Account number for expense classification';
        COMMENT ON COLUMN vendor_invoices.tax_amount IS 'Tax amount on the invoice';
        COMMENT ON COLUMN vendor_invoices.project_id IS 'Reference to associated project';
        COMMENT ON COLUMN vendor_invoices.staff_id IS 'Reference to associated staff member';
        COMMENT ON COLUMN vendor_invoices.company_id IS 'Reference to associated company';
        COMMENT ON COLUMN vendor_invoices.is_paid IS 'Whether the invoice has been paid';
        COMMENT ON COLUMN vendor_invoices.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN vendor_invoices.updated_at IS 'Timestamp when the record was last updated';
        
        RAISE NOTICE 'Created vendor_invoices table with all columns and policies';
    END IF;
END $$; 