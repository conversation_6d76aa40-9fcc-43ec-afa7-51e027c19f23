-- Create the general_ledger table if it doesn't exist
CREATE TABLE IF NOT EXISTS general_ledger (
  ledger_id SERIAL PRIMARY KEY,
  transaction_date TIMESTAMP NOT NULL,
  account_number VARCHAR(10) NOT NULL,
  description TEXT,
  debit DECIMAL(15, 2) NOT NULL DEFAULT 0,
  credit DECIMAL(15, 2) NOT NULL DEFAULT 0,
  currency_code VARCHAR(3) NOT NULL,
  project_id INTEGER REFERENCES projects(project_id) ON DELETE SET NULL,
  staff_id INTEGER REFERENCES staff_members(staff_id) ON DELETE SET NULL,
  tax_amount DECIMAL(15, 2),
  company_id INTEGER NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_gl_account_number ON general_ledger(account_number);
CREATE INDEX IF NOT EXISTS idx_gl_transaction_date ON general_ledger(transaction_date);
CREATE INDEX IF NOT EXISTS idx_gl_company_id ON general_ledger(company_id);
CREATE INDEX IF NOT EXISTS idx_gl_project_id ON general_ledger(project_id);

-- Add comment to table for documentation
COMMENT ON TABLE general_ledger IS 'Stores all general ledger entries for accounting purposes';

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_general_ledger_timestamp()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP; 
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_general_ledger_timestamp_trigger
BEFORE UPDATE ON general_ledger
FOR EACH ROW
EXECUTE FUNCTION update_general_ledger_timestamp(); 