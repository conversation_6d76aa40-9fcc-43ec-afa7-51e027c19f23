-- Check if each column exists and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'staff_members') THEN
        CREATE TABLE staff_members (
            staff_id SERIAL PRIMARY KEY,
            staff_name VA<PERSON>HAR NOT NULL,
            email VARCHAR UNIQUE NOT NULL,
            phone VA<PERSON>HAR,
            position VA<PERSON>HA<PERSON>,
            department VARCHAR,
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Enable RLS on staff_members table
        ALTER TABLE staff_members ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for authenticated users to select staff members
        CREATE POLICY staff_members_select ON staff_members
            FOR SELECT
            USING (true);
            
        -- Create policy for authenticated users to insert staff members
        CREATE POLICY staff_members_insert ON staff_members
            FOR INSERT
            WITH CHECK (true);
            
        -- Create policy for authenticated users to update staff members
        CREATE POLICY staff_members_update ON staff_members
            FOR UPDATE
            USING (true)
            WITH CHECK (true);
            
        -- Create policy for authenticated users to delete staff members
        CREATE POLICY staff_members_delete ON staff_members
            FOR DELETE
            USING (true);
            
        -- Add comments to describe the table and fields
        COMMENT ON TABLE staff_members IS 'Staff members of companies';
        COMMENT ON COLUMN staff_members.staff_id IS 'Unique staff member identifier';
        COMMENT ON COLUMN staff_members.staff_name IS 'Full name of the staff member';
        COMMENT ON COLUMN staff_members.email IS 'Email address of the staff member';
        COMMENT ON COLUMN staff_members.phone IS 'Phone number of the staff member';
        COMMENT ON COLUMN staff_members.position IS 'Job position/title';
        COMMENT ON COLUMN staff_members.department IS 'Department name';
        COMMENT ON COLUMN staff_members.company_id IS 'Reference to associated company';
        COMMENT ON COLUMN staff_members.is_active IS 'Whether the staff member is active';
        COMMENT ON COLUMN staff_members.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN staff_members.updated_at IS 'Timestamp when the record was last updated';
        
        RAISE NOTICE 'Created staff_members table with all columns and policies';
    END IF;
END $$; 