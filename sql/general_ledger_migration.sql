-- Add transaction_id to GeneralLedger table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'general_ledger' 
        AND column_name = 'transaction_id'
    ) THEN
        ALTER TABLE general_ledger
        ADD COLUMN transaction_id UUID DEFAULT gen_random_uuid();
        
        -- Add comment
        COMMENT ON COLUMN general_ledger.transaction_id IS 'Unique identifier to group related ledger entries';
    END IF;
END $$;

-- Add transaction_id to Expenses table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'expenses' 
        AND column_name = 'transaction_id'
    ) THEN
        ALTER TABLE expenses
        ADD COLUMN transaction_id UUID DEFAULT gen_random_uuid();
        
        -- Add comment
        COMMENT ON COLUMN expenses.transaction_id IS 'Unique identifier to group related expense and ledger entries';
    END IF;
END $$;

-- Add check constraint to ensure debit and credit are not both non-zero
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE table_name = 'general_ledger' 
        AND constraint_name = 'check_debit_credit'
    ) THEN
        ALTER TABLE general_ledger
        ADD CONSTRAINT check_debit_credit
        CHECK (
            (debit = 0 AND credit > 0) OR 
            (credit = 0 AND debit > 0) OR 
            (debit = 0 AND credit = 0)
        );
    END IF;
END $$;

-- Add check constraint to ensure amount is positive
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE table_name = 'expenses' 
        AND constraint_name = 'check_expense_amount'
    ) THEN
        ALTER TABLE expenses
        ADD CONSTRAINT check_expense_amount
        CHECK (amount >= 0);
    END IF;
END $$; 