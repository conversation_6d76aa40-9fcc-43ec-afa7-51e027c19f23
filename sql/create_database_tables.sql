-- Generate the database tables. Run this from Supabase's SQL editor.

-- Create Companies table
CREATE TABLE Companies (
    company_id SERIAL PRIMARY KEY,
    company_name VARCHAR UNIQUE,
    address TEXT,
    contact_person VARCHAR
);

-- Create Projects table
CREATE TABLE Projects (
    project_id SERIAL PRIMARY KEY,
    project_code VA<PERSON><PERSON>R UNIQUE,
    project_name VARCHAR,
    description TEXT,
    company_id INT REFERENCES Companies(company_id)
);

-- Create StaffMembers table
CREATE TABLE StaffMembers (
    staff_id SERIAL PRIMARY KEY,
    staff_name VARCHAR,
    email VARCHAR UNIQUE,
    company_id INT REFERENCES Companies(company_id)
);

-- Create Currencies table
CREATE TABLE Currencies (
    currency_code VARCHAR PRIMARY KEY,
    currency_name VARCHAR
);

-- Create ExchangeRates table
CREATE TABLE ExchangeRates (
    rate_id SERIAL PRIMARY KEY,
    from_currency VARCHAR REFERENCES Currencies(currency_code),
    to_currency VARCHAR REFERENCES Currencies(currency_code),
    exchange_rate DECIMAL,
    effective_date DATE
);

-- Create Accounts table
CREATE TABLE Accounts (
    account_number VARCHAR PRIMARY KEY,
    account_name VARCHAR,
    company_id INT REFERENCES Companies(company_id)
);

-- Create GeneralLedger table
CREATE TABLE GeneralLedger (
    ledger_id SERIAL PRIMARY KEY,
    transaction_date DATE,
    account_number VARCHAR REFERENCES Accounts(account_number),
    description TEXT,
    debit DECIMAL,
    credit DECIMAL,
    currency_code VARCHAR REFERENCES Currencies(currency_code),
    project_id INT REFERENCES Projects(project_id),
    staff_id INT REFERENCES StaffMembers(staff_id),
    tax_amount DECIMAL,
    company_id INT REFERENCES Companies(company_id)
);

-- Create Customers table
CREATE TABLE Customers (
    customer_id VARCHAR PRIMARY KEY,
    customer_name VARCHAR,
    address TEXT,
    contact_person VARCHAR,
    company_id INT REFERENCES Companies(company_id)
);

-- Create Vendors table
CREATE TABLE Vendors (
    vendor_id VARCHAR PRIMARY KEY,
    vendor_name VARCHAR,
    address TEXT,
    contact_person VARCHAR,
    company_id INT REFERENCES Companies(company_id)
);

-- Create Invoices table
CREATE TABLE Invoices (
    invoice_id SERIAL PRIMARY KEY,
    customer_id VARCHAR REFERENCES Customers(customer_id),
    invoice_date DATE,
    due_date DATE,
    amount DECIMAL,
    currency_code VARCHAR REFERENCES Currencies(currency_code),
    project_id INT REFERENCES Projects(project_id),
    staff_id INT REFERENCES StaffMembers(staff_id),
    tax_amount DECIMAL,
    company_id INT REFERENCES Companies(company_id)
);

-- Create Expenses table
CREATE TABLE Expenses (
    expense_id SERIAL PRIMARY KEY,
    vendor_id VARCHAR REFERENCES Vendors(vendor_id),
    expense_date DATE,
    amount DECIMAL,
    currency_code VARCHAR REFERENCES Currencies(currency_code),
    project_id INT REFERENCES Projects(project_id),
    staff_id INT REFERENCES StaffMembers(staff_id),
    tax_amount DECIMAL,
    company_id INT REFERENCES Companies(company_id)
);

-- Create PaymentsIn table
CREATE TABLE PaymentsIn (
    payment_in_id SERIAL PRIMARY KEY,
    invoice_id INT REFERENCES Invoices(invoice_id),
    payment_date DATE,
    amount DECIMAL,
    currency_code VARCHAR REFERENCES Currencies(currency_code),
    company_id INT REFERENCES Companies(company_id)
);

-- Create PaymentsOut table
CREATE TABLE PaymentsOut (
    payment_out_id SERIAL PRIMARY KEY,
    expense_id INT REFERENCES Expenses(expense_id),
    payment_date DATE,
    amount DECIMAL,
    currency_code VARCHAR REFERENCES Currencies(currency_code),
    company_id INT REFERENCES Companies(company_id)
);