-- Migration script to update the database schema
-- This script is idempotent and can be run multiple times safely

-- Begin transaction
BEGIN;

-- 1. Create GeneralLedger table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'general_ledger') THEN
        CREATE TABLE general_ledger (
            ledger_id SERIAL PRIMARY KEY,
            transaction_id UUID DEFAULT gen_random_uuid(),
            transaction_date TIMESTAMP NOT NULL,
            account_number VARCHAR(10) NOT NULL,
            description TEXT,
            debit DECIMAL(15, 2) NOT NULL DEFAULT 0,
            credit DECIMAL(15, 2) NOT NULL DEFAULT 0,
            currency_code VARCHAR(3) NOT NULL,
            project_id INTEGER REFERENCES projects(project_id) ON DELETE SET NULL,
            staff_id INTEGER REFERENCES staff_members(staff_id) ON DELETE SET NULL,
            tax_amount DECIMAL(15, 2),
            company_id INTEGER NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Add check constraint for debit and credit
        ALTER TABLE general_ledger
        ADD CONSTRAINT check_debit_credit
        CHECK (
            (debit = 0 AND credit > 0) OR 
            (credit = 0 AND debit > 0) OR 
            (debit = 0 AND credit = 0)
        );

        -- Create indexes
        CREATE INDEX idx_gl_account_number ON general_ledger(account_number);
        CREATE INDEX idx_gl_transaction_date ON general_ledger(transaction_date);
        CREATE INDEX idx_gl_company_id ON general_ledger(company_id);
        CREATE INDEX idx_gl_project_id ON general_ledger(project_id);

        -- Add comments
        COMMENT ON TABLE general_ledger IS 'Stores all general ledger entries for accounting purposes';
        COMMENT ON COLUMN general_ledger.transaction_id IS 'Unique identifier to group related ledger entries';
    END IF;
END $$;

-- 2. Create Expenses table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses') THEN
        CREATE TABLE expenses (
            expense_id SERIAL PRIMARY KEY,
            transaction_id UUID DEFAULT gen_random_uuid(),
            vendor_id VARCHAR REFERENCES vendors(vendor_id),
            expense_date DATE NOT NULL,
            amount DECIMAL(15, 2) NOT NULL,
            currency_code VARCHAR(3) REFERENCES currencies(currency_code),
            project_id INTEGER REFERENCES projects(project_id),
            staff_id INTEGER REFERENCES staff_members(staff_id),
            tax_amount DECIMAL(15, 2),
            payment_method VARCHAR,
            credit_card_number VARCHAR,
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Add check constraint for amount
        ALTER TABLE expenses
        ADD CONSTRAINT check_expense_amount
        CHECK (amount >= 0);

        -- Add comments
        COMMENT ON COLUMN expenses.transaction_id IS 'Unique identifier to group related expense and ledger entries';
        COMMENT ON COLUMN expenses.payment_method IS 'Payment method used for the expense (creditCard, cash, bankTransfer, other)';
        COMMENT ON COLUMN expenses.credit_card_number IS 'Credit card number used for payment, if applicable';
    END IF;
END $$;

-- 3. Create StaffMembers table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'staff_members') THEN
        CREATE TABLE staff_members (
            staff_id SERIAL PRIMARY KEY,
            staff_name VARCHAR NOT NULL,
            email VARCHAR UNIQUE NOT NULL,
            phone VARCHAR,
            position VARCHAR,
            department VARCHAR,
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Enable RLS
        ALTER TABLE staff_members ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY staff_members_select ON staff_members
            FOR SELECT
            USING (true);

        CREATE POLICY staff_members_insert ON staff_members
            FOR INSERT
            WITH CHECK (true);

        CREATE POLICY staff_members_update ON staff_members
            FOR UPDATE
            USING (true)
            WITH CHECK (true);

        CREATE POLICY staff_members_delete ON staff_members
            FOR DELETE
            USING (true);

        -- Add comments
        COMMENT ON TABLE staff_members IS 'Staff members of companies';
        COMMENT ON COLUMN staff_members.staff_id IS 'Unique staff member identifier';
        COMMENT ON COLUMN staff_members.staff_name IS 'Full name of the staff member';
        COMMENT ON COLUMN staff_members.email IS 'Email address of the staff member';
        COMMENT ON COLUMN staff_members.phone IS 'Phone number of the staff member';
        COMMENT ON COLUMN staff_members.position IS 'Job position/title';
        COMMENT ON COLUMN staff_members.department IS 'Department name';
        COMMENT ON COLUMN staff_members.company_id IS 'Reference to associated company';
        COMMENT ON COLUMN staff_members.is_active IS 'Whether the staff member is active';
        COMMENT ON COLUMN staff_members.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN staff_members.updated_at IS 'Timestamp when the record was last updated';
    END IF;
END $$;

-- 4. Create vendor_invoices table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendor_invoices') THEN
        CREATE TABLE vendor_invoices (
            invoice_id SERIAL PRIMARY KEY,
            vendor_id VARCHAR REFERENCES vendors(vendor_id) ON DELETE CASCADE,
            invoice_number VARCHAR NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE NOT NULL,
            amount DECIMAL(15, 2) NOT NULL,
            currency_code VARCHAR(3) REFERENCES currencies(currency_code),
            expense_account_number VARCHAR(10) REFERENCES accounts(account_number),
            tax_amount DECIMAL(15, 2),
            project_id INTEGER REFERENCES projects(project_id),
            staff_id INTEGER REFERENCES staff_members(staff_id),
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE,
            is_paid BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Enable RLS
        ALTER TABLE vendor_invoices ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY vendor_invoices_select ON vendor_invoices
            FOR SELECT
            USING (true);
            
        CREATE POLICY vendor_invoices_insert ON vendor_invoices
            FOR INSERT
            WITH CHECK (true);
            
        CREATE POLICY vendor_invoices_update ON vendor_invoices
            FOR UPDATE
            USING (true)
            WITH CHECK (true);
            
        CREATE POLICY vendor_invoices_delete ON vendor_invoices
            FOR DELETE
            USING (true);
            
        -- Add comments
        COMMENT ON TABLE vendor_invoices IS 'Vendor invoices for accounts payable operations';
        COMMENT ON COLUMN vendor_invoices.invoice_id IS 'Unique invoice identifier';
        COMMENT ON COLUMN vendor_invoices.vendor_id IS 'Reference to the vendor';
        COMMENT ON COLUMN vendor_invoices.invoice_number IS 'Vendor invoice number';
        COMMENT ON COLUMN vendor_invoices.invoice_date IS 'Date of the invoice';
        COMMENT ON COLUMN vendor_invoices.due_date IS 'Due date for payment';
        COMMENT ON COLUMN vendor_invoices.amount IS 'Invoice amount';
        COMMENT ON COLUMN vendor_invoices.currency_code IS 'Currency code for the amount';
        COMMENT ON COLUMN vendor_invoices.expense_account_number IS 'Account number for expense classification';
        COMMENT ON COLUMN vendor_invoices.tax_amount IS 'Tax amount on the invoice';
        COMMENT ON COLUMN vendor_invoices.project_id IS 'Reference to associated project';
        COMMENT ON COLUMN vendor_invoices.staff_id IS 'Reference to associated staff member';
        COMMENT ON COLUMN vendor_invoices.company_id IS 'Reference to associated company';
        COMMENT ON COLUMN vendor_invoices.is_paid IS 'Whether the invoice has been paid';
        COMMENT ON COLUMN vendor_invoices.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN vendor_invoices.updated_at IS 'Timestamp when the record was last updated';
    END IF;
END $$;

-- Commit transaction
COMMIT; 