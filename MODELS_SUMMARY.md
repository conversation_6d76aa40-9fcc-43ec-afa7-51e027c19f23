# Models Summary

This document provides an overview of the data models created for the We Like Money accounting package.

## Core Models

### Project
```dart
class Project {
  int projectId;
  String projectCode;
  String projectName;
  String? description;
}
```

### StaffMember
```dart
class StaffMember {
  int staffId;
  String staffName;
  String email;
}
```

### Currency
```dart
class Currency {
  String currencyCode;
  String currencyName;
}
```

### ExchangeRate
```dart
class ExchangeRate {
  int rateId;
  String fromCurrency;
  String toCurrency;
  double exchangeRate;
  DateTime effectiveDate;
}
```

### Account
```dart
class Account {
  String accountNumber;
  String accountName;
}
```

### GeneralLedger
```dart
class GeneralLedger {
  int ledgerId;
  DateTime transactionDate;
  String accountNumber;
  String description;
  double debit;
  double credit;
  String currencyCode;
  int? projectId;
  int? staffId;
  double? taxAmount;
}
```

## Customer and Vendor Models

### Customer
```dart
class Customer {
  String customerId;
  String customerName;
  String? address;
  String? contactPerson;
}
```

### Vendor
```dart
class Vendor {
  String vendorId;
  String vendorName;
  String? address;
  String? contactPerson;
}
```

## Transaction Models

### Invoice
```dart
class Invoice {
  int invoiceId;
  String customerId;
  DateTime invoiceDate;
  DateTime dueDate;
  double amount;
  String currencyCode;
  int? projectId;
  int? staffId;
  double? taxAmount;
}
```

### Expense
```dart
class Expense {
  int expenseId;
  String vendorId;
  DateTime expenseDate;
  double amount;
  String currencyCode;
  int? projectId;
  int? staffId;
  double? taxAmount;
}
```

### PaymentIn
```dart
class PaymentIn {
  int paymentInId;
  int invoiceId;
  DateTime paymentDate;
  double amount;
  String currencyCode;
}
```

### PaymentOut
```dart
class PaymentOut {
  int paymentOutId;
  int expenseId;
  DateTime paymentDate;
  double amount;
  String currencyCode;
}
```

## Model Relationships

- **Projects** can have multiple GeneralLedger entries, Invoices, and Expenses
- **StaffMembers** can be associated with GeneralLedger entries, Invoices, and Expenses
- **Currencies** are referenced by ExchangeRates, GeneralLedger entries, Invoices, Expenses, PaymentsIn, and PaymentsOut
- **Accounts** are referenced by GeneralLedger entries
- **Customers** are referenced by Invoices
- **Vendors** are referenced by Expenses
- **Invoices** are referenced by PaymentsIn
- **Expenses** are referenced by PaymentsOut

## Next Steps

1. Run the build_runner to generate the necessary code:
   ```bash
   ./scripts/generate_models.sh
   ```

2. Implement the remaining methods in the SupabaseDatabaseService class

3. Create UI components for displaying and editing these models

4. Implement controllers for managing state 