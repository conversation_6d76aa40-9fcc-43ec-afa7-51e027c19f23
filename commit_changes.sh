#!/bin/bash

# Generate necessary files
echo "Generating model and mock files..."
dart run build_runner build --delete-conflicting-outputs

# Run the tests
echo "Running tests..."
flutter test

# Check if tests passed
if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Fix the issues before committing."
    exit 1
fi

# Add files to git
echo "Adding files to git..."
git add .

# Get commit message from user or use default
if [ -z "$1" ]; then
    MESSAGE="feat: implement general ledger integration and fix serialization"
else
    MESSAGE="$1"
fi

# Commit the changes
echo "Committing with message: $MESSAGE"
git commit -m "$MESSAGE"

echo "✅ Changes committed successfully!"
echo "You can now push the changes with 'git push'" 