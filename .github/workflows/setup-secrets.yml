name: Setup Secrets

on:
  workflow_dispatch:
    inputs:
      supabase_url:
        description: 'Supabase Project URL (must start with https://)'
        required: true
        type: string
      supabase_anon_key:
        description: 'Supabase Anonymous Key'
        required: true
        type: string
        default: ''

permissions:
  contents: write
  security-events: write

jobs:
  setup-secrets:
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'development' }}
    
    steps:
      - name: Validate Supabase URL
        run: |
          if [[ ! "${{ github.event.inputs.supabase_url }}" =~ ^https:// ]]; then
            echo "Error: Supabase URL must start with https://"
            exit 1
          fi
        shell: bash

      - name: Validate Supabase Anon Key
        run: |
          if [[ -z "${{ github.event.inputs.supabase_anon_key }}" ]]; then
            echo "Error: Supabase Anon Key cannot be empty"
            exit 1
          fi
        shell: bash

      - name: Add Supabase URL
        run: |
          echo "SUPABASE_URL=${{ github.event.inputs.supabase_url }}" >> $GITHUB_ENV
        shell: bash

      - name: Add Supabase Anon Key
        run: |
          echo "SUPABASE_ANON_KEY=${{ github.event.inputs.supabase_anon_key }}" >> $GITHUB_ENV
        shell: bash

      - name: Create .env file
        run: |
          echo "SUPABASE_URL=${{ github.event.inputs.supabase_url }}" > .env
          echo "SUPABASE_ANON_KEY=${{ github.event.inputs.supabase_anon_key }}" >> .env
          chmod 600 .env
        shell: bash

      - name: Verify .env file
        run: |
          if [ ! -f .env ]; then
            echo "Error: .env file was not created"
            exit 1
          fi
          if [ ! -s .env ]; then
            echo "Error: .env file is empty"
            exit 1
          fi
        shell: bash

      - name: Cleanup sensitive data
        if: always()
        run: |
          rm -f .env
          unset SUPABASE_URL
          unset SUPABASE_ANON_KEY
        shell: bash 