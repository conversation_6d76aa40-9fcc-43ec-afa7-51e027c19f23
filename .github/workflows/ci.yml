name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: 'master'
        channel: 'master'
        cache: true
    
    - name: Verify Flutter and Dart versions
      run: |
        flutter --version
        dart --version
        # Verify Dart version is >= 3.7.0
        DART_VERSION=$(dart --version | cut -d' ' -f2)
        if [[ "$(printf '%s\n' "3.7.0" "$DART_VERSION" | sort -V | head -n1)" != "3.7.0" ]]; then
          echo "Error: Dart version must be >= 3.7.0, but got $DART_VERSION"
          exit 1
        fi
    
    - name: Install dependencies
      run: |
        flutter pub get
        sudo apt-get update
        sudo apt-get install -y lcov
    
    - name: Cache pub dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.pub
          build/
        key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-pub-
    
    - name: Generate test mocks
      run: |
        echo "Generating test mocks..."
        flutter pub run build_runner build --delete-conflicting-outputs test
    
    - name: Analyze project source
      run: flutter analyze
    
    - name: Run tests with coverage
      run: |
        flutter test --coverage
        genhtml coverage/lcov.info -o coverage/html
    
    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/html