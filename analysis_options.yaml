# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
    curly_braces_in_flow_control_structures: true
    avoid_returning_null_for_void: true
    avoid_void_async: true
    await_only_futures: true
    prefer_const_constructors: true  # Enabled to improve performance
    # prefer_final_locals: true  # Disabled to avoid too many changes

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options

# TEAM RULES:
# 1. ALWAYS check the linter and NEVER commit changes while there are still linter errors.
# 2. Run 'flutter analyze' before committing to ensure code quality.
# 3. Fix ALL linter errors before creating a pull request or merging code.
