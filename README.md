# We Like Money - Accounting Package

A comprehensive accounting package for software consultancy firms, built with Flutter.

## Features

- Project management
- Staff member management
- General ledger
- Invoice management
- Expense tracking
- Multi-currency support
- Bank reconciliation
- Financial reporting
- Authority reporting
- Internal visualizations

## Security and Credentials

### Environment Variables

This project uses environment variables for configuration. These are stored in different files:

- `.env` - Main environment variables for development/production
- `.env.test` - Environment variables for testing

**IMPORTANT: Never commit actual credentials to version control!**

We use example files (`.env.example` and `.env.test.example`) as templates. When setting up the project:

1. Copy the example files to create your local versions
2. Add your actual credentials to the local files
3. Ensure these files are in `.gitignore`

```bash
# Set up environment files
cp .env.example .env
cp .env.test.example .env.test

# Edit with your credentials
nano .env
nano .env.test
```

### Adding New Environment Variables

When adding new environment variables:

1. Add them to the example files (`.env.example` or `.env.test.example`) with placeholder values
2. Document them in the appropriate READMEs
3. Add them to your local environment files
4. Never commit actual credential values in any file

### Secure Testing

Our integration tests are designed to:
- Look for credentials in `.env.test`
- Fall back to mock mode if credentials aren't available
- Never hardcode sensitive values in test files

See [Integration Testing Guide](integration_test/README.md) for more details.

## Data Models

The application is built around the following core data models:

- **Project**: Represents client projects with unique codes and descriptions
- **StaffMember**: Represents employees with contact information
- **Currency**: Defines supported currencies with codes and names
- **ExchangeRate**: Tracks exchange rates between currencies over time
- **Account**: Defines chart of accounts with account numbers and names
- **GeneralLedger**: Records all financial transactions with debits and credits
- **Customer**: Stores client information for invoicing
- **Vendor**: Stores supplier information for expenses
- **Invoice**: Tracks outgoing invoices to customers
- **Expense**: Records expenses paid to vendors
- **PaymentIn**: Tracks payments received against invoices
- **PaymentOut**: Tracks payments made against expenses

## Development Setup

### Prerequisites

- Flutter SDK (^3.7.0)
- Dart SDK (^3.7.0)
- Supabase account (for development)
- PostgreSQL (for production)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/we_like_money.git
cd we_like_money
```

2. Install dependencies:
```bash
flutter pub get
```

3. Set up Supabase:
   - Create a new Supabase project
   - Copy your project URL and anon key
   - Create a `.env` file in the root directory with:
     ```
     SUPABASE_URL=your_project_url
     SUPABASE_ANON_KEY=your_anon_key
     ```

4. Generate code:
```bash
./scripts/generate_models.sh
# or
flutter pub run build_runner build --delete-conflicting-outputs
```

### Running Tests

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/app_test.dart
```

### Integration Testing Setup

For integration tests, you need to set up a test environment:

1. Copy the example test environment file:
```bash
cp .env.test.example .env.test
```

2. Modify `.env.test` with appropriate test values if needed.

3. Run the integration tests:
```bash
flutter test integration_test/app_test.dart
```

The integration tests are designed to be robust and will handle missing environment files gracefully.

### Development Workflow

1. Create a new branch for your feature:
```bash
git checkout -b feat/your-feature-name
```

2. Make your changes and commit them:
```bash
git add .
git commit -m "feat: your feature description"
```

3. Push your changes:
```bash
git push origin feat/your-feature-name
```

4. Create a pull request

## Architecture

The project follows clean architecture principles with the following structure:

```
lib/
├── models/         # Data models
├── services/       # Business logic and data access
├── controllers/    # State management
└── ui/            # User interface components
```

## Testing

The project includes:
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for end-to-end flows

### Conditional Testing

We employ a conditional testing approach that allows tests to run in multiple modes:

1. **Offline Mode (Default)**: Uses mock implementations without requiring external services. Perfect for CI/CD pipelines.
2. **Online Mode**: Tests against a real Supabase instance to verify actual database operations.
3. **Fallback Online Mode**: When platform limitations prevent true online testing, a special mock client is used.

To configure testing mode:

```bash
# Copy the example test config
cp .env.test.example .env.test

# Edit .env.test to set online mode
# SUPABASE_TEST_ONLINE=true|false
```

For more details, see [Conditional Testing Documentation](doc/conditional_testing.md).

### Running Tests

```bash
# Run all tests (in the mode configured in .env.test)
flutter test

# Run specific tests
flutter test test/services/supabase_database_service_test.dart

# Run with coverage
flutter test --coverage
```

### True Online Testing

To run tests against actual services without fallback mocks, use integration tests:

```bash
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/app_test.dart
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License

This project is proprietary software. All rights reserved.

## Form Validation Utilities

The application includes a comprehensive set of form validation utilities to ensure data integrity:

### Validator Class

The `Validator` class provides a clean, unified API for all validation needs:

```dart
// Basic validation
final nameValidator = Validator.compose([
  Validator.required('Name is required'),
  Validator.maxLength(50, 'Name is too long')
]);

// Email validation
final emailValidator = Validator.compose([
  Validator.required('Email is required'),
  Validator.email()
]);

// Password validation
final passwordValidator = Validator.password(
  minLength: 8,
  requireUppercase: true,
  requireNumbers: true
);

// Date validation
final dateValidator = Validator.compose([
  Validator.required('Date is required'),
  Validator.dateFormat('yyyy-MM-dd'),
  Validator.dateAfter(DateTime.now(), 'yyyy-MM-dd', 'Date must be in the future')
]);

// Money validation
final amountValidator = Validator.compose([
  Validator.required('Amount is required'),
  Validator.money(minValue: 0.01)
]);
```

### Available Validators

- **Basic Validators**: required, minLength, maxLength, pattern matching
- **Format Validators**: email, numeric, money/currency
- **Password Validators**: customizable complexity requirements
- **Date Validators**: format validation, date range validation, business day validation
- **Field Matching**: for password confirmation, etc.

These validators can be combined using `Validator.compose()` to create complex validation rules.

### Domain-Specific Validators

For specialized validation needs, the application also includes domain-specific validators:

#### InvoiceValidators

Specialized validators for invoice-related fields:

```dart
// Invoice number validation
final invoiceNumberValidator = InvoiceValidators.invoiceNumber();

// Due date validation relative to invoice date
final dueDateValidator = InvoiceValidators.dueDateValidator(
  invoiceDate,
  minDays: 14,
  maxDays: 60
);

// Invoice amount validation
final amountValidator = InvoiceValidators.amount(minAmount: 1.0);

// Tax amount validation
final taxValidator = InvoiceValidators.taxAmount(
  () => double.parse(amountController.text),
  maxTaxRate: 0.25
);
```

#### CurrencyValidators

Specialized validators for currency-related fields:

```dart
// Currency code validation
final currencyCodeValidator = CurrencyValidators.currencyCode();

// Currency name validation
final currencyNameValidator = CurrencyValidators.currencyName();

// Exchange rate validation
final exchangeRateValidator = CurrencyValidators.exchangeRate(minRate: 0.001);

// Currency comparison validation
final targetCurrencyValidator = CurrencyValidators.targetCurrencyNotSameAsBase(
  () => baseCurrencyController.text
);
```

All validators are extensively unit-tested and designed to handle edge cases gracefully.

## Getting Started

This project is a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
