{"trustedCommands": [{"command": "flutter analyze", "description": "Run Flutter analyzer without confirmation"}, {"command": "flutter test", "description": "Run Flutter tests without confirmation"}, {"command": "flutter pub get", "description": "Get Flutter dependencies without confirmation"}, {"command": "flutter pub run build_runner build", "description": "Run build_runner without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && flutter analyze", "description": "Run Flutter analyzer in project directory without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && flutter test", "description": "Run Flutter tests in project directory without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && flutter pub get", "description": "Get Flutter dependencies in project directory without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && flutter pub run build_runner build", "description": "Run build_runner in project directory without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && flutter pub run build_runner build --delete-conflicting-outputs", "description": "Run build_runner with delete conflicting outputs in project directory without confirmation"}, {"command": "cd /Users/<USER>/projects/WeLikeMoney/we_like_money && dart run build_runner build --delete-conflicting-outputs", "description": "Run dart build_runner with delete conflicting outputs in project directory without confirmation"}, {"command": "cd", "description": "change directory"}], "settings": {"terminal": {"autoRunCommands": true}}}