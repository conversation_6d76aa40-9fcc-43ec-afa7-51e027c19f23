# .env.test.example
# Template for integration test environment configuration
# Copy this file to .env.test and update with your credentials
# DO NOT COMMIT your actual .env.test file to version control!

# Supabase configuration (required)
SUPABASE_URL=https://your-test-supabase-url.supabase.co
SUPABASE_ANON_KEY=your-test-anon-key

# Optional: dedicated test credentials
SUPABASE_TEST_URL=https://your-dedicated-test-instance.supabase.co
SUPABASE_TEST_ANON_KEY=your-dedicated-test-instance-key
SUPABASE_TEST_ONLINE=true  # Set to true to use online Supabase, false for mock mode

# Test configuration
TEST_MODE=online          # 'online' or 'mock'
ENABLE_MOCK_FALLBACK=true # Whether to fall back to mock mode if connections fail

# Test database configuration (for local testing)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=we_like_money_test
DB_USER=postgres
DB_PASSWORD=postgres

# Test user credentials (for authentication tests)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=test_password

# Environment identifier
ENV=test 