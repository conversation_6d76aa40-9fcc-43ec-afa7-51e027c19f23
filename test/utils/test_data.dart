import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:uuid/uuid.dart';

/// Utility class containing common test data used across tests
class TestData {
  // Company test data
  static const testCompany = Company(
    companyId: 1,
    companyName: 'Test Company',
    organizationNumber: 'ORG-123',
    phone: '+**********',
    email: '<EMAIL>',
    address: 'Test Address',
    zipCode: '12345',
    city: 'Test City',
    country: 'Test Country',
  );

  static const testCompany2 = Company(
    companyId: 2,
    companyName: 'Test Company 2',
    organizationNumber: 'ORG-456',
    phone: '+**********',
    email: '<EMAIL>',
    address: 'Test Address 2',
    zipCode: '67890',
    city: 'Test City',
    country: 'Test Country',
  );

  static List<Company> getTestCompanies() => [testCompany, testCompany2];

  // Account test data
  static const testAccount = Account(
    accountNumber: '1000',
    accountName: 'Cash Account',
    accountType: 'asset',
    companyId: 1,
  );

  static List<Account> getTestAccounts() => [
    testAccount,
    const Account(
      accountNumber: '2000',
      accountName: 'Bank Account',
      accountType: 'asset',
      companyId: 1,
    ),
  ];

  // Project test data
  static const testProject = Project(
    projectId: 1,
    projectCode: 'PROJ-1',
    projectName: 'Test Project 1',
  );

  static List<Project> getTestProjects() => [
    testProject,
    const Project(
      projectId: 2,
      projectCode: 'PROJ-2',
      projectName: 'Test Project 2',
    ),
  ];

  // General Ledger test data
  static GeneralLedger getTestLedgerEntry({
    required int ledgerId,
    required String accountNumber,
    required double debit,
    required double credit,
    required String description,
  }) {
    return GeneralLedger(
      ledgerId: ledgerId,
      transactionId: const Uuid().v4(),
      transactionDate: DateTime(2024, 1, 1),
      accountNumber: accountNumber,
      description: description,
      debit: debit,
      credit: credit,
      currencyCode: 'USD',
      projectId: 1,
      staffId: 1,
      taxAmount: 10.0,
      companyId: 1,
    );
  }

  static List<GeneralLedger> getTestLedgerEntries() => [
    getTestLedgerEntry(
      ledgerId: 1,
      accountNumber: '1000',
      debit: 1000.0,
      credit: 0.0,
      description: 'Test transaction 1',
    ),
    getTestLedgerEntry(
      ledgerId: 2,
      accountNumber: '2000',
      debit: 0.0,
      credit: 500.0,
      description: 'Test transaction 2',
    ),
  ];
}
