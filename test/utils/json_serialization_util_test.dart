import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/json_serialization_util.dart';

void main() {
  group('JsonSerializationUtil', () {
    test('camelToSnake converts simple camelCase strings', () {
      expect(
        JsonSerializationUtil.camelToSnake('companyId'),
        equals('company_id'),
      );
      expect(
        JsonSerializationUtil.camelToSnake('fromCurrency'),
        equals('from_currency'),
      );
      expect(
        JsonSerializationUtil.camelToSnake('taxAmount'),
        equals('tax_amount'),
      );
    });

    test('camelToSnake works with already snake_case strings', () {
      expect(
        JsonSerializationUtil.camelToSnake('company_id'),
        equals('company_id'),
      );
      expect(
        JsonSerializationUtil.camelToSnake('from_currency'),
        equals('from_currency'),
      );
    });

    test('snakeToCamel converts simple snake_case strings', () {
      expect(
        JsonSerializationUtil.snakeToCamel('company_id'),
        equals('companyId'),
      );
      expect(
        JsonSerializationUtil.snakeToCamel('from_currency'),
        equals('fromCurrency'),
      );
      expect(
        JsonSerializationUtil.snakeToCamel('tax_amount'),
        equals('taxAmount'),
      );
    });

    test('snakeToCamel works with already camelCase strings', () {
      expect(
        JsonSerializationUtil.snakeToCamel('companyId'),
        equals('companyId'),
      );
      expect(
        JsonSerializationUtil.snakeToCamel('fromCurrency'),
        equals('fromCurrency'),
      );
    });

    test('convertMapKeysToSnakeCase converts simple maps', () {
      final camelMap = {
        'companyId': 1,
        'companyName': 'Test',
        'contactPerson': 'John',
      };

      final snakeMap = JsonSerializationUtil.convertMapKeysToSnakeCase(
        camelMap,
      );

      expect(
        snakeMap,
        equals({
          'company_id': 1,
          'company_name': 'Test',
          'contact_person': 'John',
        }),
      );
    });

    test('convertMapKeysToSnakeCase handles nested maps', () {
      final camelMap = {
        'companyId': 1,
        'companyData': {
          'companyName': 'Test',
          'contactInfo': {'contactPerson': 'John', 'phoneNumber': '555-1234'},
        },
      };

      final snakeMap = JsonSerializationUtil.convertMapKeysToSnakeCase(
        camelMap,
      );

      expect(
        snakeMap,
        equals({
          'company_id': 1,
          'company_data': {
            'company_name': 'Test',
            'contact_info': {
              'contact_person': 'John',
              'phone_number': '555-1234',
            },
          },
        }),
      );
    });

    test('convertMapKeysToSnakeCase handles lists', () {
      final camelMap = {
        'companyId': 1,
        'contactList': [
          {'contactId': 1, 'contactName': 'John'},
          {'contactId': 2, 'contactName': 'Jane'},
        ],
      };

      final snakeMap = JsonSerializationUtil.convertMapKeysToSnakeCase(
        camelMap,
      );

      expect(
        snakeMap,
        equals({
          'company_id': 1,
          'contact_list': [
            {'contact_id': 1, 'contact_name': 'John'},
            {'contact_id': 2, 'contact_name': 'Jane'},
          ],
        }),
      );
    });

    test('convertMapKeysToSnakeCase handles DateTime objects', () {
      final date = DateTime(2023, 3, 15, 10, 30);
      final camelMap = {'effectiveDate': date};

      final snakeMap = JsonSerializationUtil.convertMapKeysToSnakeCase(
        camelMap,
      );

      expect(snakeMap, equals({'effective_date': '2023-03-15T10:30:00.000'}));
    });

    test('convertMapKeysToCamelCase converts simple maps', () {
      final snakeMap = {
        'company_id': 1,
        'company_name': 'Test',
        'contact_person': 'John',
      };

      final camelMap = JsonSerializationUtil.convertMapKeysToCamelCase(
        snakeMap,
      );

      expect(
        camelMap,
        equals({
          'companyId': 1,
          'companyName': 'Test',
          'contactPerson': 'John',
        }),
      );
    });

    test('convertMapKeysToCamelCase handles nested maps', () {
      final snakeMap = {
        'company_id': 1,
        'company_data': {
          'company_name': 'Test',
          'contact_info': {
            'contact_person': 'John',
            'phone_number': '555-1234',
          },
        },
      };

      final camelMap = JsonSerializationUtil.convertMapKeysToCamelCase(
        snakeMap,
      );

      expect(
        camelMap,
        equals({
          'companyId': 1,
          'companyData': {
            'companyName': 'Test',
            'contactInfo': {'contactPerson': 'John', 'phoneNumber': '555-1234'},
          },
        }),
      );
    });

    test('convertMapKeysToCamelCase handles ISO date strings', () {
      final snakeMap = {'effective_date': '2023-03-15T10:30:00.000'};

      final camelMap = JsonSerializationUtil.convertMapKeysToCamelCase(
        snakeMap,
      );

      expect(camelMap['effectiveDate'], isA<DateTime>());
      expect(camelMap['effectiveDate'], equals(DateTime(2023, 3, 15, 10, 30)));
    });
  });
}
