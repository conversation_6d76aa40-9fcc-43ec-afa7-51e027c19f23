import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/validation/index.dart';

void main() {
  group('Validator', () {
    group('required', () {
      test('returns error message for null value', () {
        final rule = Validator.required('Field is required');
        expect(rule.validate(null), equals('Field is required'));
      });

      test('returns error message for empty string', () {
        final rule = Validator.required('Field is required');
        expect(rule.validate(''), equals('Field is required'));
      });

      test('returns null for non-empty string', () {
        final rule = Validator.required('Field is required');
        expect(rule.validate('test'), isNull);
      });
    });

    group('email', () {
      test('returns null for valid email addresses', () {
        final rule = Validator.email();
        expect(rule.validate('<EMAIL>'), isNull);
        expect(rule.validate('<EMAIL>'), isNull);
        expect(rule.validate('<EMAIL>'), isNull);
      });

      test('returns error message for invalid email addresses', () {
        final rule = Validator.email();
        expect(rule.validate('invalid-email'), isNotNull);
        expect(rule.validate('@example.com'), isNotNull);
        expect(rule.validate('test@'), isNotNull);
        expect(rule.validate('test@.com'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.email();
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('minLength', () {
      test('returns null for strings meeting minimum length', () {
        final rule = Validator.minLength(3, 'Minimum 3 characters');
        expect(rule.validate('abc'), isNull);
        expect(rule.validate('abcd'), isNull);
      });

      test('returns error message for strings below minimum length', () {
        final rule = Validator.minLength(3, 'Minimum 3 characters');
        expect(rule.validate('ab'), equals('Minimum 3 characters'));
        expect(rule.validate('a'), equals('Minimum 3 characters'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.minLength(3, 'Minimum 3 characters');
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('maxLength', () {
      test('returns null for strings within maximum length', () {
        final rule = Validator.maxLength(3, 'Maximum 3 characters');
        expect(rule.validate('abc'), isNull);
        expect(rule.validate('ab'), isNull);
      });

      test('returns error message for strings exceeding maximum length', () {
        final rule = Validator.maxLength(3, 'Maximum 3 characters');
        expect(rule.validate('abcd'), equals('Maximum 3 characters'));
        expect(rule.validate('abcde'), equals('Maximum 3 characters'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.maxLength(3, 'Maximum 3 characters');
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('numeric', () {
      test('returns null for valid numeric values', () {
        final rule = Validator.numeric();
        expect(rule.validate('123'), isNull);
        expect(rule.validate('123.45'), isNull);
        expect(rule.validate('-123'), isNull);
      });

      test('returns error message for non-numeric values', () {
        final rule = Validator.numeric();
        expect(rule.validate('abc'), isNotNull);
        expect(rule.validate('123abc'), isNotNull);
        expect(rule.validate('abc123'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.numeric();
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('pattern', () {
      test('returns null for matching patterns', () {
        final rule = Validator.pattern(
          RegExp(r'^[A-Z]{2}\d{4}$'),
          'Must be 2 uppercase letters followed by 4 digits',
        );
        expect(rule.validate('AB1234'), isNull);
        expect(rule.validate('XY5678'), isNull);
      });

      test('returns error message for non-matching patterns', () {
        final rule = Validator.pattern(
          RegExp(r'^[A-Z]{2}\d{4}$'),
          'Must be 2 uppercase letters followed by 4 digits',
        );
        expect(rule.validate('A1234'), isNotNull);
        expect(rule.validate('AB123'), isNotNull);
        expect(rule.validate('ab1234'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.pattern(
            RegExp(r'^[A-Z]{2}\d{4}$'),
            'Must be 2 uppercase letters followed by 4 digits',
          );
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('numericRange', () {
      test('returns null for values within range', () {
        final rule = Validator.numericRange(min: 10, max: 100);
        expect(rule.validate('10'), isNull);
        expect(rule.validate('50'), isNull);
        expect(rule.validate('100'), isNull);
      });

      test('returns error message for values outside range', () {
        final rule = Validator.numericRange(min: 10, max: 100);
        expect(rule.validate('9.99'), isNotNull);
        expect(rule.validate('100.01'), isNotNull);
      });

      test('respects negative value setting', () {
        final rule = Validator.numericRange(min: -100, max: 100, allowNegative: true);
        expect(rule.validate('-50'), isNull);

        final noNegativeRule = Validator.numericRange(min: 0, max: 100, allowNegative: false);
        expect(noNegativeRule.validate('-50'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.numericRange(min: 10, max: 100);
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('matches', () {
      test('returns null for matching values', () {
        final rule = Validator.matches(
          () => 'password123',
          'Passwords do not match',
        );
        expect(rule.validate('password123'), isNull);
      });

      test('returns error message for non-matching values', () {
        final rule = Validator.matches(
          () => 'password123',
          'Passwords do not match',
        );
        expect(rule.validate('password456'), equals('Passwords do not match'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.matches(
            () => 'password123',
            'Passwords do not match',
          );
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('all', () {
      test('returns first error message from failing validators', () {
        final rule = Validator.all([
          Validator.required('Required'),
          Validator.minLength(5, 'Min 5 chars'),
          Validator.email(),
        ]);

        expect(rule.validate(null), equals('Required'));
        expect(rule.validate(''), equals('Required'));
        expect(rule.validate('a'), equals('Min 5 chars'));
        expect(
          rule.validate('aaaaa'),
          equals('Please enter a valid email address'),
        );
        expect(rule.validate('<EMAIL>'), isNull);
      });

      test('returns null when all validators pass', () {
        final rule = Validator.all([
          Validator.required('Required'),
          Validator.email(),
          Validator.minLength(5, 'Min 5 chars'),
        ]);

        expect(rule.validate('<EMAIL>'), isNull);
      });
    });

    group('dateFormat', () {
      test('returns null for valid date format', () {
        final rule = Validator.dateFormat('yyyy-MM-dd');
        expect(rule.validate('2023-01-01'), isNull);
        expect(rule.validate('2023-12-31'), isNull);
      });

      test('returns error message for invalid date format', () {
        final rule = Validator.dateFormat('yyyy-MM-dd');
        expect(rule.validate('01/01/2023'), isNotNull);
        expect(rule.validate('2023-13-01'), isNotNull);
        expect(rule.validate('2023-01-32'), isNotNull);
      });
    });

    group('dateAfter', () {
      test('returns null for dates after minimum', () {
        final minDate = DateTime(2023, 1, 1);
        final rule = Validator.dateAfter(minDate, 'yyyy-MM-dd');
        expect(rule.validate('2023-01-02'), isNull);
        expect(rule.validate('2023-02-01'), isNull);
      });

      test('returns error message for dates before or equal to minimum', () {
        final minDate = DateTime(2023, 1, 1);
        final rule = Validator.dateAfter(minDate, 'yyyy-MM-dd');
        expect(rule.validate('2022-12-31'), isNotNull);
        expect(rule.validate('2023-01-01'), isNotNull);
      });

      test('respects inclusive setting', () {
        final minDate = DateTime(2023, 1, 1);
        final rule = Validator.dateAfter(minDate, 'yyyy-MM-dd', null, true);
        expect(rule.validate('2023-01-01'), isNull);
        expect(rule.validate('2022-12-31'), isNotNull);
      });
    });

    group('invoiceNumber', () {
      test('returns null for valid invoice numbers', () {
        final rule = Validator.invoiceNumber();
        expect(rule.validate('INV123'), isNull);
        expect(rule.validate('2023-001'), isNull);
      });

      test('returns error message for invalid invoice numbers', () {
        final rule = Validator.invoiceNumber();
        expect(rule.validate(''), isNotNull);
        expect(rule.validate('INV#123'), isNotNull);
      });
    });

    group('invoiceAmount', () {
      test('returns null for valid amounts', () {
        final rule = Validator.invoiceAmount();
        expect(rule.validate('0.01'), isNull);
        expect(rule.validate('100'), isNull);
      });

      test('returns error message for invalid amounts', () {
        final rule = Validator.invoiceAmount();
        expect(rule.validate(''), isNotNull);
        expect(rule.validate('0'), isNotNull);
        expect(rule.validate('-10'), isNotNull);
      });

      test('respects allowZero setting', () {
        final rule = Validator.invoiceAmount(allowZero: true);
        expect(rule.validate('0'), isNull);
        expect(rule.validate('0.00'), isNull);
      });
    });

    group('createValidator', () {
      test('creates a Flutter-compatible validator function', () {
        final validatorFn = Validator.createValidator(
          Validator.required('Required')
        );
        
        expect(validatorFn(''), 'Required');
        expect(validatorFn('value'), isNull);
      });
    });

    group('password', () {
      test('returns null for valid passwords with default requirements', () {
        final rule = Validator.password();
        expect(rule.validate('Password123!'), isNull);
        expect(rule.validate('SecureP@ss1'), isNull);
      });

      test('returns error message for passwords missing requirements', () {
        final rule = Validator.password();
        expect(
          rule.validate('password'),
          isNotNull,
        ); // Missing uppercase, number, special char
        expect(
          rule.validate('PASSWORD'),
          isNotNull,
        ); // Missing lowercase, number, special char
        expect(
          rule.validate('Password'),
          isNotNull,
        ); // Missing number, special char
        expect(rule.validate('Password123'), isNotNull); // Missing special char
      });

      test('respects custom password requirements', () {
        final rule = Validator.password(
          minLength: 10,
          requireUppercase: false,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
        );
        expect(rule.validate('password123'), isNull);
        expect(rule.validate('password'), isNotNull); // Too short
        expect(
          rule.validate('password123!'),
          isNull,
        ); // Special char is allowed even when not required
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.password();
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    group('moneyAmount', () {
      test('returns null for valid money values', () {
        final rule = Validator.moneyAmount();
        expect(rule.validate('123.45'), isNull);
        expect(rule.validate('1,234.56'), isNull);
        expect(rule.validate('\$123.45'), isNull);
      });

      test('respects minimum value constraint', () {
        final rule = Validator.moneyAmount(minValue: 100);
        expect(rule.validate('100'), isNull);
        expect(rule.validate('100.01'), isNull);
        expect(rule.validate('99.99'), isNotNull);
      });

      test('respects maximum value constraint', () {
        final rule = Validator.moneyAmount(maxValue: 1000);
        expect(rule.validate('1000'), isNull);
        expect(rule.validate('999.99'), isNull);
        expect(rule.validate('1000.01'), isNotNull);
      });

      test('respects negative value setting', () {
        final rule = Validator.moneyAmount(allowNegative: true);
        expect(rule.validate('-123.45'), isNull);

        final noNegativeRule = Validator.moneyAmount(allowNegative: false);
        expect(noNegativeRule.validate('-123.45'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final rule = Validator.moneyAmount();
          expect(rule.validate(''), isNull);
          expect(rule.validate(null), isNull);
        },
      );
    });

    // --- Start of Currency Validator Tests (from currency_validators_test.dart) ---
    group('currencyCode', () {
      test('validates correct currency codes', () {
        final rule = Validator.currencyCode();
        expect(rule.validate('USD'), isNull);
        expect(rule.validate('EUR'), isNull);
        expect(rule.validate('GBP'), isNull);
      });

      test('rejects invalid currency codes', () {
        final rule = Validator.currencyCode();
        expect(rule.validate('usd'), isNotNull); // Lowercase
        expect(rule.validate('US'), isNotNull); // Too short
        expect(rule.validate('USDD'), isNotNull); // Too long
        expect(rule.validate('123'), isNotNull); // Numbers
        expect(rule.validate('US\$'), isNotNull); // Special characters
      });

      test('handles null and empty values (due to composed required rule)', () {
        final rule = Validator.currencyCode();
        // Validator.currencyCode is composed of BaseValidator.required and BaseValidator.pattern
        // So, it should return an error for null or empty.
        expect(rule.validate(null), isNotNull); 
        expect(rule.validate(''), isNotNull);
      });

      test('uses custom error message for composed rules', () {
        // The custom message in Validator.currencyCode is passed to both required and pattern.
        // If required fails (e.g. empty string), that message is shown.
        final ruleWithCustomMsg = Validator.currencyCode('Custom error for currency code');
        expect(ruleWithCustomMsg.validate(''), 'Custom error for currency code');
        // If required passes but pattern fails (e.g. "us"), that same message is shown.
        expect(ruleWithCustomMsg.validate('us'), 'Custom error for currency code'); 
      });
    });
    
    group('currencyName', () {
      test('validates correct currency names', () {
        final rule = Validator.currencyName();
        expect(rule.validate('US Dollar'), isNull);
        expect(rule.validate('Euro'), isNull);
        expect(rule.validate('British Pound Sterling'), isNull);
      });

      test('rejects invalid currency names', () {
        final rule = Validator.currencyName();
        expect(rule.validate('A'), isNotNull); // Too short
        expect(rule.validate('A' * 51), isNotNull); // Too long (default max 50)
      });

      test('handles custom max length', () {
        final rule = Validator.currencyName(maxLength: 10);
        expect(rule.validate('US Dollar'), isNull); // 9 chars
        expect(rule.validate('Euro Dollar'), isNotNull); // 11 chars
      });

      test('handles null and empty values', () {
        final rule = Validator.currencyName();
        expect(rule.validate(null), isNotNull); // Required
        expect(rule.validate(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final rule = Validator.currencyName(
          errorMessage: 'Custom error',
        );
        expect(rule.validate(''), 'Custom error');
      });
    });

    group('exchangeRate', () {
      test('validates correct exchange rates', () {
        final rule = Validator.exchangeRate();
        expect(rule.validate('1.00'), isNull);
        expect(rule.validate('0.00001'), isNull); // Minimum default
        expect(rule.validate('1000.00'), isNull);
      });

      test('rejects invalid exchange rates', () {
        final rule = Validator.exchangeRate();
        expect(rule.validate('0.000001'), isNotNull); // Below minimum
        expect(rule.validate('0'), isNotNull); // Zero
        expect(rule.validate('-1.00'), isNotNull); // Negative
        expect(rule.validate('abc'), isNotNull); // Not a number
      });

      test('handles custom min and max rates', () {
        final rule = Validator.exchangeRate(
          minRate: 0.1,
          maxRate: 10.0,
        );
        expect(rule.validate('0.09'), isNotNull); // Below min
        expect(rule.validate('0.10'), isNull); // At min
        expect(rule.validate('5.00'), isNull); // In range
        expect(rule.validate('10.00'), isNull); // At max
        expect(rule.validate('10.01'), isNotNull); // Above max
      });

      test('handles null and empty values', () {
        final rule = Validator.exchangeRate();
        expect(rule.validate(null), isNotNull); // Required
        expect(rule.validate(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final rule = Validator.exchangeRate(
          errorMessage: 'Custom error',
        );
        expect(rule.validate('abc'), 'Custom error');
      });
    });

    group('targetCurrencyNotSameAsBase', () {
      test('validates different currencies', () {
        getBaseCurrency() => 'USD';
        final rule = Validator.targetCurrencyNotSameAsBase(
          getBaseCurrency,
        );
        expect(rule.validate('EUR'), isNull);
        expect(rule.validate('GBP'), isNull);
        expect(rule.validate('USD'), isNotNull); // Same as base
      });

      test('handles null and empty values', () {
        getBaseCurrency() => 'USD';
        final rule = Validator.targetCurrencyNotSameAsBase(
          getBaseCurrency,
        );
        expect(rule.validate(null), isNull); 
        expect(rule.validate(''), isNull); 
      });

      test('uses custom error message', () {
        getBaseCurrency() => 'USD';
        final rule = Validator.targetCurrencyNotSameAsBase(
          getBaseCurrency,
          'Custom error',
        );
        expect(rule.validate('USD'), 'Custom error');
      });
    });

    group('currencySymbol', () {
      test('validates correct currency symbols', () {
        final rule = Validator.currencySymbol();
        expect(rule.validate('\$'), isNull);
        expect(rule.validate('€'), isNull);
        expect(rule.validate('£'), isNull);
        expect(rule.validate('¥'), isNull);
      });

      test('rejects invalid currency symbols', () {
        final rule = Validator.currencySymbol();
        expect(rule.validate(''), isNotNull); // Required
        expect(rule.validate('\$' * 6), isNotNull); // Too long (default max 5)
      });

      test('handles custom max length', () {
        final rule = Validator.currencySymbol(maxLength: 3);
        expect(rule.validate('\$'), isNull); // 1 char
        expect(rule.validate('USD'), isNull); // 3 chars
        expect(rule.validate('USDD'), isNotNull); // 4 chars
      });

      test('handles null and empty values', () {
        final rule = Validator.currencySymbol();
        expect(rule.validate(null), isNotNull); // Required
        expect(rule.validate(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final rule = Validator.currencySymbol(
          errorMessage: 'Custom error',
        );
        expect(rule.validate(''), 'Custom error');
      });
    });

    group('decimalPlaces', () {
      test('validates correct decimal places', () {
        final rule = Validator.decimalPlaces();
        expect(rule.validate('0'), isNull);
        expect(rule.validate('2'), isNull);
        expect(rule.validate('6'), isNull);
      });

      test('rejects invalid decimal places', () {
        final rule = Validator.decimalPlaces();
        expect(rule.validate('-1'), isNotNull); // Negative
        expect(rule.validate('7'), isNotNull); // Too many
        expect(rule.validate('abc'), isNotNull); // Not a number
      });

      test('handles null and empty values', () {
        final rule = Validator.decimalPlaces();
        expect(rule.validate(null), isNotNull); // Required
        expect(rule.validate(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final rule = Validator.decimalPlaces('Custom error');
        expect(rule.validate('7'), 'Custom error');
      });
    });
    // --- End of Currency Validator Tests ---

    // --- Start of Date Validator Tests (from date_validators_test.dart) ---
    // dateFormat and dateAfter are already in validator_test.dart

    group('dateBefore', () {
      test('validates dates before maximum date', () {
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBefore(maxDate, 'yyyy-MM-dd');
        expect(rule.validate('2024-03-15'), isNull);
        expect(rule.validate('2024-03-01'), isNull);
        expect(rule.validate('2024-04-01'), isNotNull);
      });

      test('handles inclusive maximum date', () {
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBefore(
          maxDate,
          'yyyy-MM-dd',
          null,
          true,
        );
        expect(rule.validate('2024-03-31'), isNull);
        expect(rule.validate('2024-04-01'), isNotNull);
      });

      test('handles null and empty values', () {
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBefore(maxDate, 'yyyy-MM-dd');
        expect(rule.validate(null), isNull);
        expect(rule.validate(''), isNull);
      });
    });

    group('dateBetween', () {
      test('validates dates between minimum and maximum dates (inclusive by default)', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBetween(
          minDate,
          maxDate,
          'yyyy-MM-dd',
        );
        expect(rule.validate('2024-03-01'), isNull); 
        expect(rule.validate('2024-03-15'), isNull);
        expect(rule.validate('2024-03-31'), isNull); 
        expect(rule.validate('2024-02-28'), isNotNull);
        expect(rule.validate('2024-04-01'), isNotNull);
      });
      
      test('handles exclusive dates', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBetween(
          minDate,
          maxDate,
          'yyyy-MM-dd',
          null, // errorMessage
          false, // inclusiveMin
          false, // inclusiveMax
        );
        expect(rule.validate('2024-03-01'), isNotNull);
        expect(rule.validate('2024-03-15'), isNull);
        expect(rule.validate('2024-03-31'), isNotNull);
      });

      test('handles null and empty values', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final rule = Validator.dateBetween(
          minDate,
          maxDate,
          'yyyy-MM-dd',
        );
        expect(rule.validate(null), isNull);
        expect(rule.validate(''), isNull);
      });
    });

    group('dateNotWeekend', () { 
      test('validates non-weekend dates', () {
        final rule = Validator.dateNotWeekend('yyyy-MM-dd');
        expect(rule.validate('2024-03-15'), isNull); // Friday
        expect(rule.validate('2024-03-18'), isNull); // Monday
        expect(rule.validate('2024-03-16'), isNotNull); // Saturday
        expect(rule.validate('2024-03-17'), isNotNull); // Sunday
      });

      test('handles null and empty values', () {
        final rule = Validator.dateNotWeekend('yyyy-MM-dd');
        expect(rule.validate(null), isNull);
        expect(rule.validate(''), isNull);
      });
    });

    group('businessDay', () {
      test('validates business days', () {
        final holidays = [
          DateTime(2024, 3, 15), // Friday
          DateTime(2024, 3, 18), // Monday
        ];
        final rule = Validator.businessDay(
          'yyyy-MM-dd',
          holidays: holidays,
        );
        expect(rule.validate('2024-03-14'), isNull); // Thursday
        expect(rule.validate('2024-03-15'), isNotNull); // Holiday
        expect(rule.validate('2024-03-16'), isNotNull); // Saturday
        expect(rule.validate('2024-03-17'), isNotNull); // Sunday
        expect(rule.validate('2024-03-18'), isNotNull); // Holiday
        expect(rule.validate('2024-03-19'), isNull); // Tuesday
      });

      test('handles null and empty values', () {
        final rule = Validator.businessDay('yyyy-MM-dd');
        expect(rule.validate(null), isNull);
        expect(rule.validate(''), isNull);
      });
    });
    // --- End of Date Validator Tests ---

    // --- Start of Invoice Validator Tests (from invoice_validators_test.dart) ---
    // invoiceNumber and invoiceAmount are already in validator_test.dart

    group('invoiceDueDate', () { 
      final invoiceDate = DateTime(2024, 3, 15);

      test('validates correct due dates', () {
        final rule = Validator.invoiceDueDate( 
          DateTime(2024, 3, 15, 0, 0, 0), 
          minDays: 1,
          maxDays: 30,
        );
        expect(rule.validate('2024-03-16'), isNull); 
        expect(rule.validate('2024-03-20'), isNull); 
        expect(rule.validate('2024-04-14'), isNull); 
      });

      test('rejects invalid due dates', () {
        final rule = Validator.invoiceDueDate( 
          DateTime(2024, 3, 15, 0, 0, 0), 
          minDays: 3,
          maxDays: 30,
        );
        expect(rule.validate('2024-03-17'), isNotNull); 
        expect(rule.validate('2024-04-16'), isNotNull); 
      });

      test('respects minimum days', () {
        final rule = Validator.invoiceDueDate( 
          invoiceDate,
          minDays: 7,
        );
        expect(rule.validate('2024-03-22'), isNull); 
        expect(rule.validate('2024-03-21'), isNotNull); 
      });

      test('handles null and empty values', () {
        final rule = Validator.invoiceDueDate(invoiceDate); 
        expect(rule.validate(null), isNull); 
        expect(rule.validate(''), isNull); 
      });

      test('uses custom error message', () {
        final rule = Validator.invoiceDueDate( 
          invoiceDate,
          minDays: 7,
          errorMessage: 'Custom error',
        );
        expect(rule.validate('2024-03-20'), 'Custom error');
      });
    });

    group('taxAmount', () {
      test('validates correct tax amounts', () {
        getInvoiceAmount() => 1000.00;
        final rule25 = Validator.taxAmount(getInvoiceAmount, maxPercentage: 25.0);
        expect(rule25.validate('0.00'), isNull); 
        expect(rule25.validate('250.00'), isNull); // 25% tax
        expect(rule25.validate('100.00'), isNull); // 10% tax
      });

      test('rejects invalid tax amounts', () {
        getInvoiceAmount() => 1000.00;
        final rule = Validator.taxAmount( 
          getInvoiceAmount,
          maxPercentage: 25.0,
        );
        expect(rule.validate(''), isNull); 
        expect(rule.validate('-100.00'), isNotNull); 
        expect(rule.validate('abc'), isNotNull); 
        expect(rule.validate('250.01'), isNotNull); 
      });

      test('handles zero invoice amount', () {
        getInvoiceAmount() => 0.00;
        final rule = Validator.taxAmount(getInvoiceAmount); 
        expect(rule.validate('100.00'), isNull); 
      });

      test('uses custom error message', () {
        getInvoiceAmount() => 1000.00;
        final rule = Validator.taxAmount( 
          getInvoiceAmount,
          maxPercentage: 25.0,
          errorMessage: 'Custom error',
        );
        expect(rule.validate('250.01'), 'Custom error');
      });
    });

    group('vendorSelected', () {
      test('validates vendor selection', () {
        final rule = Validator.vendorSelected(); 
        expect(rule.validate(null), isNotNull);
        expect(rule.validate(''), isNotNull); 
        final ruleForString = Validator.vendorSelected();
        expect(ruleForString.validate('some vendor'), isNull);
      });

      test('uses custom error message', () {
        final rule = Validator.vendorSelected('Custom error'); 
        expect(rule.validate(null), 'Custom error');
      });
    });

    group('accountSelected', () {
      test('validates account selection', () {
        final rule = Validator.accountSelected(); 
        expect(rule.validate(null), isNotNull);
        expect(rule.validate(''), isNotNull); 
        final ruleForString = Validator.accountSelected();
        expect(ruleForString.validate('some account'), isNull);
      });

      test('uses custom error message', () {
        final rule = Validator.accountSelected('Custom error'); 
        expect(rule.validate(null), 'Custom error');
      });
    });
    // --- End of Invoice Validator Tests ---
  });
}
