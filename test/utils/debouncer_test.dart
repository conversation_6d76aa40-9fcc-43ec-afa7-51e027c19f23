import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/debouncer.dart';

void main() {
  group('Debouncer', () {
    late Debouncer debouncer;
    late int callbackCount;

    setUp(() {
      debouncer = Debouncer(milliseconds: 100);
      callbackCount = 0;
    });

    tearDown(() {
      debouncer.cancel();
    });

    test('executes callback after delay', () async {
      // Arrange
      final completer = Completer<void>();

      // Act
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });

      // Assert
      expect(callbackCount, equals(0));
      await completer.future;
      expect(callbackCount, equals(1));
    });

    test('cancels previous callback when run is called again', () async {
      // Arrange
      final completer = Completer<void>();

      // Act
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });

      // Assert
      expect(callbackCount, equals(0));
      await completer.future;
      expect(callbackCount, equals(1));
    });

    test('cancel stops pending callback', () async {
      // Arrange
      final completer = Completer<void>();

      // Act
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });
      debouncer.cancel();

      // Assert
      expect(callbackCount, equals(0));
      await Future.delayed(const Duration(milliseconds: 150));
      expect(callbackCount, equals(0));
    });

    test('multiple runs with cancel between them', () async {
      // Arrange
      final completer = Completer<void>();

      // Act
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });
      debouncer.cancel();
      debouncer.run(() {
        callbackCount++;
        completer.complete();
      });

      // Assert
      expect(callbackCount, equals(0));
      await completer.future;
      expect(callbackCount, equals(1));
    });
  });
}
