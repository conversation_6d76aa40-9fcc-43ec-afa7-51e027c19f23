import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:we_like_money/services/logger.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';

@GenerateMocks([Logger])
import 'error_handler_test.mocks.dart';

void main() {
  late ErrorHandler errorHandler;
  late MockLogger mockLogger;

  setUp(() {
    mockLogger = MockLogger();
    errorHandler = ErrorHandler(mockLogger);
  });

  group('handleError', () {
    test('handles PostgrestException with PGRST116 code', () {
      // Arrange
      const error = PostgrestException(
        message: 'Resource not found',
        code: 'PGRST116',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(result, 'Resource not found.');
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles PostgrestException with PGRST109 code', () {
      // Arrange
      const error = PostgrestException(
        message: 'Database conflict',
        code: 'PGRST109',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(
        result,
        'Database conflict. The operation could not be completed.',
      );
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles PostgrestException with 23505 code', () {
      // Arrange
      const error = PostgrestException(
        message: 'Unique violation',
        code: '23505',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(result, 'A record with this information already exists.');
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles PostgrestException with 23503 code', () {
      // Arrange
      const error = PostgrestException(
        message: 'Foreign key violation',
        code: '23503',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(result, 'This operation would violate database constraints.');
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles PostgrestException with JWT error', () {
      // Arrange
      const error = PostgrestException(
        message: 'JWT token expired',
        code: 'PGRST401',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(result, 'Your session has expired. Please log in again.');
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles NetworkException', () {
      // Arrange
      final error = NetworkException('Connection timeout');

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(
        result,
        'Network error. Please check your connection and try again.',
      );
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles BusinessException', () {
      // Arrange
      final error = BusinessException('Invalid input');

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(result, 'Invalid input');
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles unknown error', () {
      // Arrange
      const error = 'Unknown error';

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(
        result,
        kDebugMode
            ? 'Unknown error'
            : 'An unexpected error occurred. Please try again later.',
      );
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });

    test('handles unknown PostgrestException', () {
      // Arrange
      const error = PostgrestException(
        message: 'Unknown database error',
        code: 'UNKNOWN',
      );

      // Act
      final result = errorHandler.handleError(error);

      // Assert
      expect(
        result,
        kDebugMode
            ? 'Database error: Unknown database error (Code: UNKNOWN)'
            : 'A database error occurred. Please try again later.',
      );
      verify(mockLogger.error('Error occurred', error, any)).called(1);
    });
  });
}
