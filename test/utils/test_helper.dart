import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/company_viewmodel.dart';

import 'test_helper.mocks.dart';

/// A helper class for setting up test mocks and utilities.
///
/// This class follows the Arrange-Act-Assert pattern for test cases:
///
/// 1. Arrange: Set up the test environment, mocks, and test data
///    - Use setupMocks() to initialize mock objects
///    - Use setupCompanyMocks() to configure mock behavior
///    - Use createTestContainer() to set up provider overrides
///
/// 2. Act: Perform the action being tested
///    - Call the method or trigger the event being tested
///    - Use tester.pump() to wait for async operations
///
/// 3. Assert: Verify the expected outcomes
///    - Use expect() to verify results
///    - Use verify() to check mock interactions
///
/// Example:
/// ```dart
/// testWidgets('test case', (tester) async {
///   // Arrange
///   await TestHelper.setupCompanyMocks(companies: testCompanies);
///
///   // Act
///   await tester.pumpWidget(MyWidget());
///   await tester.pump();
///
///   // Assert
///   expect(find.text('Expected Text'), findsOneWidget);
/// });
/// ```
@GenerateMocks(
  [],
  customMocks: [MockSpec<CompanyViewModel>(as: #MockCompanyViewModelForTest)],
)
class TestHelper {
  static late MockCompanyViewModelForTest mockCompanyViewModel;

  /// Sets up mock objects for testing.
  ///
  /// This is typically called in setUp() to initialize mocks before each test.
  /// Part of the Arrange phase.
  static void setupMocks() {
    mockCompanyViewModel = MockCompanyViewModelForTest();
  }

  /// Configures mock behavior for company-related operations.
  ///
  /// This is used in the Arrange phase to set up the expected behavior
  /// of the company view model for the test case.
  ///
  /// Parameters:
  /// - companies: List of companies to return from getCompanies()
  /// - singleCompany: Single company to return from getCompanies()
  /// - shouldThrow: Whether to throw an exception
  /// - errorMessage: Message to include in the exception
  static Future<void> setupCompanyMocks({
    List<Company>? companies,
    Company? singleCompany,
    bool shouldThrow = false,
    String errorMessage = 'Test error',
  }) async {
    if (shouldThrow) {
      when(
        mockCompanyViewModel.getCompanies(),
      ).thenThrow(Exception(errorMessage));
    } else if (companies != null) {
      when(
        mockCompanyViewModel.getCompanies(),
      ).thenAnswer((_) async => companies);
    } else if (singleCompany != null) {
      when(
        mockCompanyViewModel.getCompanies(),
      ).thenAnswer((_) async => [singleCompany]);
    } else {
      when(mockCompanyViewModel.getCompanies()).thenAnswer((_) async => []);
    }
  }

  /// Creates a ProviderContainer with the necessary overrides for testing.
  ///
  /// This is used in the Arrange phase to set up the provider environment
  /// for widget testing.
  ///
  /// Parameters:
  /// - overrides: Additional provider overrides for the test case
  static ProviderContainer createTestContainer({
    List<Override> overrides = const [],
  }) {
    return ProviderContainer(
      overrides: [
        companyViewModelProvider.overrideWithValue(mockCompanyViewModel),
        ...overrides,
      ],
    );
  }
}
