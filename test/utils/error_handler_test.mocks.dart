// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in we_like_money/test/utils/error_handler_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/services/logger.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [<PERSON><PERSON>].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockLogger extends _i1.Mock implements _i2.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void info(String? message) => super.noSuchMethod(
    Invocation.method(#info, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void warning(String? message) => super.noSuchMethod(
    Invocation.method(#warning, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void error(String? message, [dynamic exception, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#error, [message, exception, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void debug(String? message) => super.noSuchMethod(
    Invocation.method(#debug, [message]),
    returnValueForMissingStub: null,
  );
}
