// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in we_like_money/test/screens/account/account_list_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/models/account.dart' as _i2;
import 'package:we_like_money/models/general_ledger.dart' as _i5;
import 'package:we_like_money/viewmodels/account_viewmodel.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAccount_0 extends _i1.SmartFake implements _i2.Account {
  _FakeAccount_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AccountViewModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockAccountViewModel extends _i1.Mock implements _i3.AccountViewModel {
  MockAccountViewModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Account>> getAccounts([int? companyId]) =>
      (super.noSuchMethod(
            Invocation.method(#getAccounts, [companyId]),
            returnValue: _i4.Future<List<_i2.Account>>.value(<_i2.Account>[]),
          )
          as _i4.Future<List<_i2.Account>>);

  @override
  _i4.Future<_i2.Account?> getAccountByNumber(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#getAccountByNumber, [accountNumber]),
            returnValue: _i4.Future<_i2.Account?>.value(),
          )
          as _i4.Future<_i2.Account?>);

  @override
  _i4.Future<_i2.Account> createAccount(_i2.Account? account) =>
      (super.noSuchMethod(
            Invocation.method(#createAccount, [account]),
            returnValue: _i4.Future<_i2.Account>.value(
              _FakeAccount_0(
                this,
                Invocation.method(#createAccount, [account]),
              ),
            ),
          )
          as _i4.Future<_i2.Account>);

  @override
  _i4.Future<_i2.Account> updateAccount(_i2.Account? account) =>
      (super.noSuchMethod(
            Invocation.method(#updateAccount, [account]),
            returnValue: _i4.Future<_i2.Account>.value(
              _FakeAccount_0(
                this,
                Invocation.method(#updateAccount, [account]),
              ),
            ),
          )
          as _i4.Future<_i2.Account>);

  @override
  _i4.Future<void> deleteAccount(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, [accountNumber]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i5.GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String? accountNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntriesByAccount, [
              accountNumber,
            ]),
            returnValue: _i4.Future<List<_i5.GeneralLedger>>.value(
              <_i5.GeneralLedger>[],
            ),
          )
          as _i4.Future<List<_i5.GeneralLedger>>);

  @override
  _i4.Future<double> calculateAccountBalance(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#calculateAccountBalance, [accountNumber]),
            returnValue: _i4.Future<double>.value(0.0),
          )
          as _i4.Future<double>);
}
