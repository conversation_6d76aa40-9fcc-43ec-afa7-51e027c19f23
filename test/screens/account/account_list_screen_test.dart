import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/ui/features/accounts/account_list_screen.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

import 'account_list_screen_test.mocks.dart';

@GenerateMocks([AccountViewModel])
void main() {
  late MockAccountViewModel mockViewModel;
  final getIt = GetIt.instance;

  setUp(() {
    // Arrange: Initialize mock and register with GetIt
    mockViewModel = MockAccountViewModel();
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
    getIt.registerSingleton<AccountViewModel>(mockViewModel);
  });

  tearDown(() {
    // Clean up: Unregister mock after each test
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
  });

  testWidgets('AccountListScreen shows loading indicator initially', (
    tester,
  ) async {
    // Arrange: Configure mock to return empty list
    when(mockViewModel.getAccounts()).thenAnswer((_) async => []);

    // Act: Build the widget
    await tester.pumpWidget(const MaterialApp(home: AccountListScreen()));

    // Assert: Verify loading indicator is shown
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // Clean up: Wait for any pending operations
    await tester.pumpAndSettle();
  });

  testWidgets('AccountListScreen shows accounts when loaded', (tester) async {
    // Arrange: Configure mock with test accounts and balances
    final accounts = [
      const Account(
        accountName: 'Test Account 1',
        accountNumber: '1000',
        accountType: 'asset',
        companyId: 1,
      ),
      const Account(
        accountName: 'Test Account 2',
        accountNumber: '2000',
        accountType: 'liability',
        companyId: 1,
      ),
    ];

    when(mockViewModel.getAccounts()).thenAnswer((_) async => accounts);
    for (final account in accounts) {
      when(
        mockViewModel.calculateAccountBalance(account.accountNumber),
      ).thenAnswer((_) async => 1000.0);
    }

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(const MaterialApp(home: AccountListScreen()));
    await tester.pumpAndSettle();

    // Assert: Verify all account information is displayed
    expect(find.text('Test Account 1'), findsOneWidget);
    expect(find.text('Test Account 2'), findsOneWidget);
    expect(find.text('Account #: 1000'), findsOneWidget);
    expect(find.text('Account #: 2000'), findsOneWidget);
    expect(find.text('\$1000.00'), findsNWidgets(2));
  });

  testWidgets('AccountListScreen shows error when loading fails', (
    tester,
  ) async {
    // Arrange: Configure mock to throw error
    when(
      mockViewModel.getAccounts(),
    ).thenThrow(BusinessException('Unable to fetch accounts'));

    // Act: Build widget and wait for error state
    await tester.pumpWidget(const MaterialApp(home: AccountListScreen()));
    await tester.pumpAndSettle();

    // Assert: Verify error message and retry button are shown
    expect(find.text('Error: Unable to fetch accounts'), findsOneWidget);
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets('AccountListScreen shows empty state when no accounts', (
    tester,
  ) async {
    // Arrange: Configure mock to return empty list
    when(mockViewModel.getAccounts()).thenAnswer((_) async => []);

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(const MaterialApp(home: AccountListScreen()));
    await tester.pumpAndSettle();

    // Assert: Verify empty state message is shown
    expect(
      find.text('No accounts found. Create an account to get started.'),
      findsOneWidget,
    );
  });

  testWidgets('Retry button reloads accounts when pressed', (tester) async {
    // Arrange: Configure mock with failing then succeeding behavior
    reset(mockViewModel);
    int callCount = 0;

    when(mockViewModel.getAccounts()).thenAnswer((_) {
      if (callCount == 0) {
        callCount++;
        throw BusinessException('Unable to fetch accounts');
      } else {
        return Future.value([
          const Account(
            accountName: 'Test Account',
            accountNumber: '1000',
            accountType: 'asset',
            companyId: 1,
          ),
        ]);
      }
    });

    when(
      mockViewModel.calculateAccountBalance('1000'),
    ).thenAnswer((_) async => 1000.0);

    // Act: Build widget and trigger retry
    await tester.pumpWidget(const MaterialApp(home: AccountListScreen()));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Retry'));
    await tester.pumpAndSettle();

    // Assert: Verify success state and mock interactions
    expect(find.text('Test Account'), findsOneWidget);
    expect(find.text('Account #: 1000'), findsOneWidget);
    expect(find.text('\$1000.00'), findsOneWidget);
    verify(mockViewModel.getAccounts()).called(2);
  });
}
