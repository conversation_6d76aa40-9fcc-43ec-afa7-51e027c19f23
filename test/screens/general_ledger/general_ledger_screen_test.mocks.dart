// Mocks generated by Mockito 5.4.5 from annotations
// in we_like_money/test/screens/general_ledger/general_ledger_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/models/general_ledger.dart' as _i4;
import 'package:we_like_money/viewmodels/general_ledger_viewmodel.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [GeneralLedgerViewModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockGeneralLedgerViewModel extends _i1.Mock
    implements _i2.GeneralLedgerViewModel {
  MockGeneralLedgerViewModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.GeneralLedger>> getAllGeneralLedgerEntries() =>
      (super.noSuchMethod(
            Invocation.method(#getAllGeneralLedgerEntries, []),
            returnValue: _i3.Future<List<_i4.GeneralLedger>>.value(
              <_i4.GeneralLedger>[],
            ),
          )
          as _i3.Future<List<_i4.GeneralLedger>>);

  @override
  _i3.Future<List<_i4.GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String? accountNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntriesByAccount, [
              accountNumber,
            ]),
            returnValue: _i3.Future<List<_i4.GeneralLedger>>.value(
              <_i4.GeneralLedger>[],
            ),
          )
          as _i3.Future<List<_i4.GeneralLedger>>);

  @override
  _i3.Future<List<_i4.GeneralLedger>> getGeneralLedgerEntriesByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntriesByDateRange, [
              startDate,
              endDate,
            ]),
            returnValue: _i3.Future<List<_i4.GeneralLedger>>.value(
              <_i4.GeneralLedger>[],
            ),
          )
          as _i3.Future<List<_i4.GeneralLedger>>);

  @override
  double calculateNetBalance(List<_i4.GeneralLedger>? entries) =>
      (super.noSuchMethod(
            Invocation.method(#calculateNetBalance, [entries]),
            returnValue: 0.0,
          )
          as double);
}
