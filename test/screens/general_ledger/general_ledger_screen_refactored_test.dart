import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/ui/features/general_ledger/components/empty_ledger_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/error_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/ledger_table.dart';
import 'package:we_like_money/ui/features/general_ledger/components/loading_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/summary_card.dart';
import 'package:we_like_money/ui/features/general_ledger/general_ledger_screen.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/general_ledger_viewmodel.dart';

import 'general_ledger_screen_test.mocks.dart';

@GenerateMocks([GeneralLedgerViewModel])
void main() {
  final getIt = GetIt.instance;
  late MockGeneralLedgerViewModel mockViewModel;

  // Test data
  final testEntries = [
    GeneralLedger(
      ledgerId: 1,
      transactionId: 'T001',
      transactionDate: DateTime(2024, 1, 1),
      accountNumber: '1000',
      description: 'Test transaction 1',
      debit: 100.0,
      credit: 0.0,
      currencyCode: 'USD',
      companyId: 1,
    ),
    GeneralLedger(
      ledgerId: 2,
      transactionId: 'T002',
      transactionDate: DateTime(2024, 1, 2),
      accountNumber: '2000',
      description: 'Test transaction 2',
      debit: 0.0,
      credit: 50.0,
      currencyCode: 'USD',
      companyId: 1,
    ),
  ];

  setUp(() {
    // Arrange: Initialize mock and register with GetIt
    mockViewModel = MockGeneralLedgerViewModel();
    if (getIt.isRegistered<GeneralLedgerViewModel>()) {
      getIt.unregister<GeneralLedgerViewModel>();
    }
    getIt.registerSingleton<GeneralLedgerViewModel>(mockViewModel);
  });

  tearDown(() {
    // Clean up: Unregister mock after each test
    if (getIt.isRegistered<GeneralLedgerViewModel>()) {
      getIt.unregister<GeneralLedgerViewModel>();
    }
  });

  testWidgets(
    'GeneralLedgerScreen shows loading indicator initially',
    (tester) async {
      // Arrange: Configure mock to delay response
      when(mockViewModel.getAllGeneralLedgerEntries()).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return testEntries;
      });

      // Act: Build the widget
      await tester.pumpWidget(const MaterialApp(home: GeneralLedgerScreen()));

      // Assert: Verify loading indicator is shown
      expect(find.byType(LedgerLoadingView), findsOneWidget);

      // Clean up: Wait for any pending operations
      await tester.pumpAndSettle();
    },
  );

  testWidgets(
    'GeneralLedgerScreen shows ledger entries when loaded',
    (tester) async {
      // Arrange: Configure mock with test entries
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenAnswer((_) async => testEntries);

      // Act: Build widget and wait for data to load
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Assert: Verify entries are displayed
      expect(find.byType(LedgerTable), findsOneWidget);
      expect(find.byType(LedgerSummaryCard), findsOneWidget);
      expect(find.text('Test transaction 1'), findsOneWidget);
      expect(find.text('Test transaction 2'), findsOneWidget);
    },
  );

  testWidgets(
    'GeneralLedgerScreen shows error view when loading fails',
    (tester) async {
      // Arrange: Configure mock to throw error
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenThrow(BusinessException('Failed to load ledger entries'));

      // Act: Build widget and wait for error state
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Assert: Verify error view is shown
      expect(find.byType(LedgerErrorView), findsOneWidget);
      expect(find.text('Failed to load ledger entries'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    },
  );

  testWidgets(
    'GeneralLedgerScreen shows empty view when no entries',
    (tester) async {
      // Arrange: Configure mock to return empty list
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenAnswer((_) async => []);

      // Act: Build widget and wait for data to load
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Assert: Verify empty view is shown
      expect(find.byType(EmptyLedgerView), findsOneWidget);
      expect(find.text('No ledger entries found'), findsOneWidget);
    },
  );

  testWidgets(
    'GeneralLedgerScreen filters entries by account number',
    (tester) async {
      // Arrange: Configure mock with test entries
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenAnswer((_) async => testEntries);

      // Act: Build widget and wait for data to load
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Find the account dropdown and tap it
      await tester.tap(find.byType(DropdownButtonFormField<String?>));
      await tester.pumpAndSettle();

      // Select the first account
      await tester.tap(find.text('1000').last);
      await tester.pumpAndSettle();

      // Assert: Verify only matching entries are shown
      expect(find.text('Test transaction 1'), findsOneWidget);
      expect(find.text('Test transaction 2'), findsNothing);
    },
  );

  testWidgets(
    'GeneralLedgerScreen clears filters when clear button is pressed',
    (tester) async {
      // Arrange: Configure mock with test entries
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenAnswer((_) async => testEntries);

      // Act: Build widget and wait for data to load
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Apply a filter
      await tester.enterText(find.byType(TextField), 'transaction 1');
      await tester.pumpAndSettle();

      // Verify filter is applied
      expect(find.text('Test transaction 1'), findsOneWidget);
      expect(find.text('Test transaction 2'), findsNothing);

      // Clear filters
      await tester.tap(find.text('Clear Filters'));
      await tester.pumpAndSettle();

      // Assert: Verify all entries are shown again
      expect(find.text('Test transaction 1'), findsOneWidget);
      expect(find.text('Test transaction 2'), findsOneWidget);
    },
  );

  testWidgets(
    'GeneralLedgerScreen retries loading when retry button is pressed',
    (tester) async {
      // Arrange: Configure mock to throw error on first call, then succeed
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenThrow(BusinessException('Failed to load ledger entries'));

      // Act: Build widget and wait for error state
      await tester.pumpWidget(
        const MaterialApp(home: GeneralLedgerScreen()),
      );
      await tester.pumpAndSettle();

      // Verify error view is shown
      expect(find.byType(LedgerErrorView), findsOneWidget);
      expect(find.text('Failed to load ledger entries'), findsOneWidget);

      // Reset mock to return success on next call
      reset(mockViewModel);
      when(
        mockViewModel.getAllGeneralLedgerEntries(),
      ).thenAnswer((_) async => testEntries);

      // Tap retry button
      await tester.tap(find.text('Retry'));
      await tester.pump(); // Start the loading state
      await tester.pumpAndSettle(); // Wait for the data to load

      // Assert: Verify success state
      expect(find.byType(LedgerTable), findsOneWidget);
      expect(find.text('Test transaction 1'), findsOneWidget);
      expect(find.text('Test transaction 2'), findsOneWidget);
    },
  );
}
