import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:we_like_money/services/logger.dart';

/// Configuration for running tests
class TestConfig {
  // Singleton pattern
  TestConfig._();
  static final TestConfig instance = TestConfig._();

  /// Whether to run tests against the remote Supabase instance
  bool get isOnlineTest => _isOnlineTest;
  bool _isOnlineTest = false;

  /// Flag for when using a fallback client
  bool get usingFallbackClient => _usingFallbackClient;
  final bool _usingFallbackClient = false;

  /// Map to store environment variables
  final Map<String, String> _envVars = {};

  /// Mock logger for tests
  late Logger mockLogger;

  /// Initialize the test configuration
  Future<void> initialize() async {
    // Try to load environment variables
    try {
      // Try to load from test directory first
      final envFile = File('test/.env');
      if (envFile.existsSync()) {
        await dotenv.dotenv.load(fileName: 'test/.env');
        debugPrint('Loaded environment from test/.env');
      } else {
        // Fallback to root directory
        await dotenv.dotenv.load();
        debugPrint('Loaded environment from .env');
      }

      // Check if we should run online tests
      final testMode = dotenv.dotenv.env['TEST_MODE'] ?? 'offline';
      _isOnlineTest = testMode.toLowerCase() == 'online';
    } catch (e) {
      debugPrint('Error loading environment: $e');
      debugPrint('Running tests in offline mode');
      _isOnlineTest = false;
    }

    // Set the test mode
    if (_isOnlineTest) {
      debugPrint('Running tests in ONLINE mode with real Supabase');
    } else {
      debugPrint('Running tests in OFFLINE mode with mocks');
    }

    // Initialize the mock logger
    mockLogger = Logger(print: debugPrint, debugMode: true);
  }

  /// Get an environment variable value
  String? getEnv(String key) {
    try {
      return dotenv.dotenv.env[key] ?? _envVars[key];
    } catch (e) {
      return _envVars[key];
    }
  }

  /// Set an environment variable value for testing
  void setEnv(String key, String value) {
    _envVars[key] = value;
  }
}
