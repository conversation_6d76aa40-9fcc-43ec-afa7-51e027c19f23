import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:we_like_money/config/app_config.dart';

void main() {
  group('AppConfig', () {
    late AppConfig appConfig;
    late SharedPreferences prefs;

    setUp(() async {
      // Reset SharedPreferences for each test
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
      appConfig = AppConfig();
    });

    test('singleton instance is the same', () {
      // Arrange: Create two instances
      final instance1 = AppConfig();
      final instance2 = AppConfig();

      // Assert: Verify they are the same instance
      expect(identical(instance1, instance2), isTrue);
    });

    test('default database mode is auto', () {
      // Assert: Verify default mode
      expect(appConfig.databaseMode, equals(DatabaseMode.auto));
    });

    test(
      'setDatabaseMode updates mode and persists to SharedPreferences',
      () async {
        // Arrange: Set up test mode
        const testMode = DatabaseMode.mockOnly;

        // Act: Set database mode
        await appConfig.setDatabaseMode(testMode);

        // Assert: Verify mode is updated
        expect(appConfig.databaseMode, equals(testMode));

        // Verify persistence
        final savedMode = prefs.getString('database_mode');
        expect(savedMode, equals('DatabaseMode.mockOnly'));
      },
    );

    test('setDatabaseMode handles errors gracefully', () async {
      // Arrange: Mock SharedPreferences to throw error
      SharedPreferences.setMockInitialValues({});
      SharedPreferences.setMockInitialValues({}); // This will cause an error

      // Act: Set database mode
      await appConfig.setDatabaseMode(DatabaseMode.mockOnly);

      // Assert: Verify mode is still updated despite error
      expect(appConfig.databaseMode, equals(DatabaseMode.mockOnly));
    });

    test('loadSavedMode loads correct mode from SharedPreferences', () async {
      // Arrange: Save a test mode
      await prefs.setString('database_mode', 'DatabaseMode.remoteOnly');

      // Act: Load saved mode
      await appConfig.loadSavedMode();

      // Assert: Verify mode is loaded correctly
      expect(appConfig.databaseMode, equals(DatabaseMode.remoteOnly));
    });

    test('loadSavedMode handles invalid saved mode', () async {
      // Arrange: Save an invalid mode
      await prefs.setString('database_mode', 'InvalidMode');

      // Act: Load saved mode
      await appConfig.loadSavedMode();

      // Assert: Verify default mode is used
      expect(appConfig.databaseMode, equals(DatabaseMode.auto));
    });

    test('loadSavedMode handles missing saved mode', () async {
      // Act: Load saved mode
      await appConfig.loadSavedMode();

      // Assert: Verify default mode is used
      expect(appConfig.databaseMode, equals(DatabaseMode.auto));
    });

    test('loadSavedMode handles errors gracefully', () async {
      // Arrange: Mock SharedPreferences to throw error
      SharedPreferences.setMockInitialValues({});
      SharedPreferences.setMockInitialValues({}); // This will cause an error

      // Act: Load saved mode
      await appConfig.loadSavedMode();

      // Assert: Verify default mode is used
      expect(appConfig.databaseMode, equals(DatabaseMode.auto));
    });

    group('shouldUseMockMode', () {
      test('returns true in auto mode when in debug mode', () {
        // Arrange: Set auto mode
        appConfig = AppConfig();

        // Assert: Verify mock mode is true in debug mode
        expect(appConfig.shouldUseMockMode(), isTrue);
      });

      test('returns true in mockOnly mode regardless of debug mode', () async {
        // Arrange: Set mockOnly mode
        await appConfig.setDatabaseMode(DatabaseMode.mockOnly);

        // Assert: Verify mock mode is true
        expect(appConfig.shouldUseMockMode(), isTrue);
      });

      test(
        'returns false in remoteOnly mode regardless of debug mode',
        () async {
          // Arrange: Set remoteOnly mode
          await appConfig.setDatabaseMode(DatabaseMode.remoteOnly);

          // Assert: Verify mock mode is false
          expect(appConfig.shouldUseMockMode(), isFalse);
        },
      );
    });
  });
}
