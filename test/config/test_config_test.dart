import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:path/path.dart' as path;

void main() {
  setUp(() async {
    // Clear dotenv before each test by loading an empty file
    try {
      final tempDir = Directory.systemTemp.createTempSync('dotenv_init');
      final tempEnvFile = File(path.join(tempDir.path, '.env.empty'));
      tempEnvFile.writeAsStringSync('# Empty env file for testing');

      await dotenv.dotenv.load(fileName: tempEnvFile.path);
      dotenv.dotenv.env.clear();

      tempDir.deleteSync(recursive: true);
    } catch (e) {
      debugPrint('Error setting up test: $e');
    }
  });

  group('Environment Variable Loading', () {
    test('loads values from a .env file', () async {
      // Create a temporary .env.test file
      final tempDir = Directory.systemTemp.createTempSync('env_test');
      final envFile = File(path.join(tempDir.path, '.env.test'));
      envFile.writeAsStringSync('''
SUPABASE_TEST_ONLINE=true
SUPABASE_TEST_URL=https://test-instance.supabase.co
SUPABASE_TEST_ANON_KEY=test_key_123456
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=test_password
      ''');

      // Load the file manually
      await dotenv.dotenv.load(fileName: envFile.path);

      // Verify values are loaded correctly
      expect(dotenv.dotenv.env['SUPABASE_TEST_ONLINE'], equals('true'));
      expect(
        dotenv.dotenv.env['SUPABASE_TEST_URL'],
        equals('https://test-instance.supabase.co'),
      );
      expect(
        dotenv.dotenv.env['SUPABASE_TEST_ANON_KEY'],
        equals('test_key_123456'),
      );
      expect(dotenv.dotenv.env['TEST_USER_EMAIL'], equals('<EMAIL>'));
      expect(dotenv.dotenv.env['TEST_USER_PASSWORD'], equals('test_password'));

      // Clean up
      tempDir.deleteSync(recursive: true);
    });

    test('interprets boolean values correctly', () {
      // Set test values
      dotenv.dotenv.env['TEST_TRUE'] = 'true';
      dotenv.dotenv.env['TEST_FALSE'] = 'false';
      dotenv.dotenv.env['TEST_UPPERCASE_TRUE'] = 'TRUE';

      // Test boolean interpretation
      expect(dotenv.dotenv.env['TEST_TRUE']?.toLowerCase() == 'true', isTrue);
      expect(dotenv.dotenv.env['TEST_FALSE']?.toLowerCase() == 'true', isFalse);
      expect(
        dotenv.dotenv.env['TEST_UPPERCASE_TRUE']?.toLowerCase() == 'true',
        isTrue,
      );

      // Test fallback behavior
      final nonExistentKey =
          dotenv.dotenv.env['NON_EXISTENT_KEY'] ?? 'fallback';
      expect(nonExistentKey, equals('fallback'));
    });

    test('finds .env file in multiple locations', () async {
      // Create a temporary directory structure
      final baseDir = Directory.systemTemp.createTempSync('env_search_test');
      final subDir = Directory(path.join(baseDir.path, 'subdir'));
      final subSubDir = Directory(path.join(subDir.path, 'subsubdir'));

      // Create directories
      subDir.createSync();
      subSubDir.createSync();

      // Create .env.test in the base directory
      final envFile = File(path.join(baseDir.path, '.env.test'));
      envFile.writeAsStringSync('TEST_VALUE=base_directory_value');

      // Test finding the file from subdirectories
      final original = Directory.current;

      try {
        // Set current directory to the deepest subdirectory
        Directory.current = subSubDir;

        // Try to find the file by testing if it exists in parent directories
        File? foundFile;
        Directory? searchDir = Directory.current;

        for (int i = 0; i < 3 && foundFile == null; i++) {
          if (searchDir == null) break;

          final testFile = File(path.join(searchDir.path, '.env.test'));
          if (await testFile.exists()) {
            foundFile = testFile;
            break;
          }

          searchDir = searchDir.parent;
        }

        // Verify file was found
        expect(foundFile, isNotNull);
        expect(await foundFile!.exists(), isTrue);

        // Load and verify content
        await dotenv.dotenv.load(fileName: foundFile.path);
        expect(dotenv.dotenv.env['TEST_VALUE'], equals('base_directory_value'));
      } finally {
        // Restore original directory
        Directory.current = original;

        // Clean up
        baseDir.deleteSync(recursive: true);
      }
    });
  });
}
