import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/logger.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';

import '../config/test_config.dart';
import '../mocks/mock_supabase.dart';
import '../services/database_service_test.mocks.dart';
import 'supabase_test_data_helper.dart';

/// Base class for tests that need to interact with Supabase
abstract class SupabaseTestBase {
  late SupabaseTestDataHelper _testDataHelper;
  late DatabaseService _databaseService;
  late ErrorHandler _errorHandler;
  late Logger _logger;
  bool _isOnlineTest = false;

  /// Whether the test is running against the real Supabase database
  bool get isOnlineTest => _isOnlineTest;

  /// The test data helper for managing test data
  SupabaseTestDataHelper get testDataHelper => _testDataHelper;

  /// The database service instance
  DatabaseService get databaseService => _databaseService;

  /// The error handler instance
  ErrorHandler get errorHandler => _errorHandler;

  /// The logger instance
  Logger get logger => _logger;

  /// Set up the test environment
  Future<void> setUp() async {
    // Initialize test configuration
    await TestConfig.instance.initialize();
    _isOnlineTest = TestConfig.instance.isOnlineTest;

    // Initialize logger and error handler
    _logger = Logger(print: debugPrint, debugMode: true);
    _errorHandler = ErrorHandler(_logger);

    if (_isOnlineTest) {
      debugPrint('Running test in ONLINE mode with real Supabase');
      // Create test data helper with real Supabase client
      _testDataHelper = await SupabaseTestDataHelper.create();
      _databaseService = SupabaseDatabaseService(
        MockSupabaseClientProvider(mockClient: null),
        _errorHandler,
        useMockData: true,
      );
    } else {
      debugPrint('Running test in OFFLINE mode with mocks');
      // Use mock database service
      _databaseService = MockDatabaseService();
      _testDataHelper = SupabaseTestDataHelper(
        _databaseService,
        _errorHandler,
        _logger,
      );
    }
  }

  /// Clean up the test environment
  Future<void> tearDown() async {
    if (_isOnlineTest) {
      // Clean up test data in real database
      await _testDataHelper.cleanupTestData();
    }
  }

  /// Run a test with proper setup and teardown
  Future<void> runTest(Future<void> Function() testFn) async {
    try {
      await setUp();
      await testFn();
    } finally {
      await tearDown();
    }
  }
}
