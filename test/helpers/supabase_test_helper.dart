import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart' as http;
import '../config/test_config.dart';

/// Helper class for Supabase testing
class SupabaseTestHelper {
  /// Returns a mock client if Supabase is not properly initialized
  static SupabaseClient? _mockClient;

  /// Get a Supabase client for testing
  ///
  /// If [TestConfig.instance.isOnlineTest] is true, attempts to return the real Supabase client.
  /// If that fails or [TestConfig.instance.usingFallbackClient] is true, returns a mock client.
  /// Otherwise, returns null to indicate that mocks should be used.
  static SupabaseClient? getSupabaseClient() {
    if (TestConfig.instance.isOnlineTest) {
      // If we're using a fallback client due to initialization issues,
      // return the mock client directly
      if (TestConfig.instance.usingFallbackClient) {
        debugPrint('Using mock Supabase client for online testing');
        _mockClient ??= _createMockClient();
        return _mockClient;
      }

      try {
        // Try to get the real client
        return Supabase.instance.client;
      } catch (e) {
        debugPrint('Error accessing Supabase client: $e');
        debugPrint('Providing a mock client for testing instead');

        // Return a mock client if the real one can't be accessed
        _mockClient ??= _createMockClient();
        return _mockClient;
      }
    } else {
      return null;
    }
  }

  /// Creates a simple mock Supabase client suitable for tests
  static SupabaseClient _createMockClient() {
    // Use the URL and key from the environment variables
    final url = _getEnvValue(
      'SUPABASE_TEST_URL',
      'https://mock-project.supabase.co',
    );
    final anonKey = _getEnvValue('SUPABASE_TEST_ANON_KEY', 'mock-anon-key');

    // Create a direct SupabaseClient (not through Supabase.initialize)
    // This avoids the SharedPreferences dependency
    return SupabaseClient(url, anonKey, httpClient: http.Client());
  }

  /// Helper method to get environment variable with fallback
  static String _getEnvValue(String key, String fallback) {
    try {
      final value = dotenv.dotenv.get(key, fallback: fallback);
      return value;
    } catch (e) {
      debugPrint('Error getting environment variable $key: $e');
      return fallback;
    }
  }
}
