import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/logger.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../mocks/mock_supabase.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
class SupabaseTestDataHelper {
  final DatabaseService _databaseService;
  // ignore: unused_field
  final ErrorHandler _errorHandler;
  final Logger _logger;
  Company? _testCompany;

  SupabaseTestDataHelper(
    this._databaseService,
    this._errorHandler,
    this._logger,
  );

  /// Creates a test company and returns its ID
  Future<int> createTestCompany() async {
    try {
      final company = Company(
        companyId: 0, // New company
        companyName: 'Test Company ${const Uuid().v4()}',
        organizationNumber: 'ORG-${const Uuid().v4()}',
        phone: '+1234567890',
        email: '<EMAIL>',
        address: 'Test Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      _testCompany = await _databaseService.createCompany(company);
      _logger.info('Created test company with ID: ${_testCompany!.companyId}');
      return _testCompany!.companyId;
    } catch (e) {
      _logger.error('Error creating test company: $e');
      rethrow;
    }
  }

  /// Deletes the test company and all associated data
  Future<void> cleanupTestData() async {
    if (_testCompany == null) {
      _logger.warning('No test company to clean up');
      return;
    }

    try {
      // Delete the test company (this will cascade delete all related data)
      await _databaseService.deleteCompany(_testCompany!.companyId);
      _logger.info('Deleted test company with ID: ${_testCompany!.companyId}');
      _testCompany = null;
    } catch (e) {
      _logger.error('Error cleaning up test data: $e');
      rethrow;
    }
  }

  /// Gets the current test company
  Company? get testCompany => _testCompany;

  /// Creates a new instance of the helper with real Supabase client
  static Future<SupabaseTestDataHelper> create() async {
    final logger = Logger(print: debugPrint, debugMode: true);
    final errorHandler = ErrorHandler(logger);

    // Get the real Supabase client
    final client = Supabase.instance.client;

    // Create a test provider that always returns the real client
    final clientProvider = TestSupabaseClientProvider(client);

    final databaseService = SupabaseDatabaseService(
      clientProvider,
      errorHandler,
    );

    return SupabaseTestDataHelper(databaseService, errorHandler, logger);
  }
}
