import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/invoice.dart';

void main() {
  group('Invoice', () {
    final testDate = DateTime(2023, 1, 15);
    final testDueDate = DateTime(2023, 2, 15);

    final invoiceJson = '''
    {
      "invoiceId": 1001,
      "customerId": "CUST001",
      "invoiceDate": "${testDate.toIso8601String()}",
      "dueDate": "${testDueDate.toIso8601String()}",
      "amount": 1250.50,
      "currencyCode": "USD",
      "projectId": 101,
      "staffId": 201,
      "taxAmount": 125.05
    }
    ''';

    group('JSON serialization', () {
      test(
        'should create Invoice from JSON and convert back with all fields',
        () {
          // Parse JSON
          final parsedJson = json.decode(invoiceJson) as Map<String, dynamic>;

          // Create model from JSON
          final invoice = Invoice.fromJson(parsedJson);

          // Verify properties
          expect(invoice.invoiceId, 1001);
          expect(invoice.customerId, 'CUST001');
          expect(invoice.invoiceDate, testDate);
          expect(invoice.dueDate, testDueDate);
          expect(invoice.amount, 1250.50);
          expect(invoice.currencyCode, 'USD');
          expect(invoice.projectId, 101);
          expect(invoice.staffId, 201);
          expect(invoice.taxAmount, 125.05);

          // Convert back to JSON
          final serializedJson = invoice.toJson();

          // Verify serialization
          expect(serializedJson['invoiceId'], 1001);
          expect(serializedJson['customerId'], 'CUST001');
          expect(serializedJson['invoiceDate'], testDate.toIso8601String());
          expect(serializedJson['dueDate'], testDueDate.toIso8601String());
          expect(serializedJson['amount'], 1250.50);
          expect(serializedJson['currencyCode'], 'USD');
          expect(serializedJson['projectId'], 101);
          expect(serializedJson['staffId'], 201);
          expect(serializedJson['taxAmount'], 125.05);
        },
      );

      test('should handle JSON with optional fields null', () {
        final minimalInvoiceJson = '''
        {
          "invoiceId": 1002,
          "customerId": "CUST002",
          "invoiceDate": "${testDate.toIso8601String()}",
          "dueDate": "${testDueDate.toIso8601String()}",
          "amount": 2000.00,
          "currencyCode": "EUR"
        }
        ''';

        // Parse JSON
        final parsedJson =
            json.decode(minimalInvoiceJson) as Map<String, dynamic>;

        // Create model from JSON
        final invoice = Invoice.fromJson(parsedJson);

        // Verify properties
        expect(invoice.invoiceId, 1002);
        expect(invoice.customerId, 'CUST002');
        expect(invoice.invoiceDate, testDate);
        expect(invoice.dueDate, testDueDate);
        expect(invoice.amount, 2000.00);
        expect(invoice.currencyCode, 'EUR');
        expect(invoice.projectId, null);
        expect(invoice.staffId, null);
        expect(invoice.taxAmount, null);

        // Convert back to JSON
        final serializedJson = invoice.toJson();

        // Verify serialization
        expect(serializedJson['invoiceId'], 1002);
        expect(serializedJson['customerId'], 'CUST002');
        expect(serializedJson['projectId'], null);
        expect(serializedJson['staffId'], null);
        expect(serializedJson['taxAmount'], null);
      });
    });

    test('should create Invoice with constructor', () {
      final invoice = Invoice(
        invoiceId: 1001,
        customerId: 'CUST001',
        invoiceDate: testDate,
        dueDate: testDueDate,
        amount: 1250.50,
        currencyCode: 'USD',
        projectId: 101,
        staffId: 201,
        taxAmount: 125.05,
      );

      expect(invoice.invoiceId, 1001);
      expect(invoice.customerId, 'CUST001');
      expect(invoice.invoiceDate, testDate);
      expect(invoice.dueDate, testDueDate);
      expect(invoice.amount, 1250.50);
      expect(invoice.currencyCode, 'USD');
      expect(invoice.projectId, 101);
      expect(invoice.staffId, 201);
      expect(invoice.taxAmount, 125.05);
    });

    test('should copy Invoice with copyWith', () {
      final invoice = Invoice(
        invoiceId: 1001,
        customerId: 'CUST001',
        invoiceDate: testDate,
        dueDate: testDueDate,
        amount: 1250.50,
        currencyCode: 'USD',
        projectId: 101,
        staffId: 201,
        taxAmount: 125.05,
      );

      final updatedInvoice = invoice.copyWith(
        amount: 1500.00,
        taxAmount: 150.00,
      );

      // Original should be unchanged
      expect(invoice.amount, 1250.50);
      expect(invoice.taxAmount, 125.05);

      // New instance should have updated values
      expect(updatedInvoice.invoiceId, 1001); // Unchanged
      expect(updatedInvoice.customerId, 'CUST001'); // Unchanged
      expect(updatedInvoice.amount, 1500.00); // Changed
      expect(updatedInvoice.taxAmount, 150.00); // Changed
    });

    test('should handle equality and hashCode', () {
      final invoice1 = Invoice(
        invoiceId: 1001,
        customerId: 'CUST001',
        invoiceDate: testDate,
        dueDate: testDueDate,
        amount: 1250.50,
        currencyCode: 'USD',
      );

      final invoice2 = Invoice(
        invoiceId: 1001,
        customerId: 'CUST001',
        invoiceDate: testDate,
        dueDate: testDueDate,
        amount: 1250.50,
        currencyCode: 'USD',
      );

      final invoice3 = Invoice(
        invoiceId: 1002,
        customerId: 'CUST002',
        invoiceDate: testDate,
        dueDate: testDueDate,
        amount: 2000.00,
        currencyCode: 'EUR',
      );

      // Same values should be equal
      expect(invoice1, equals(invoice2));
      expect(invoice1.hashCode, equals(invoice2.hashCode));

      // Different values should not be equal
      expect(invoice1, isNot(invoice3));
      expect(invoice1.hashCode, isNot(invoice3.hashCode));
    });
  });
}
