import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/account.dart';

void main() {
  group('Account', () {
    const testAccount = Account(
      accountNumber: '1001',
      accountName: 'Cash Account',
      accountType: 'asset',
      companyId: 1,
    );

    group('Constructor', () {
      test('should create Account with valid values', () {
        const account = Account(
          accountNumber: '1000',
          accountName: 'Test Account',
          accountType: 'asset',
          companyId: 1,
        );

        expect(account.accountNumber, '1000');
        expect(account.accountName, 'Test Account');
        expect(account.accountType, 'asset');
        expect(account.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should create Account from JSON with all fields', () {
        const json = {
          'account_number': '1001',
          'account_name': 'Cash Account',
          'account_type': 'asset',
          'company_id': 1,
        };

        final account = Account.fromJson(json);

        expect(account.accountNumber, '1001');
        expect(account.accountName, 'Cash Account');
        expect(account.accountType, 'asset');
        expect(account.companyId, 1);
      });

      test('should convert Account to JSON with all fields', () {
        final json = testAccount.toJson();

        expect(json['account_number'], '1001');
        expect(json['account_name'], 'Cash Account');
        expect(json['account_type'], 'asset');
        expect(json['company_id'], 1);
      });

      test('should maintain data integrity through JSON conversion', () {
        const json = {
          'account_number': '2000',
          'account_name': 'Bank Account',
          'account_type': 'liability',
          'company_id': 2,
        };

        final account = Account.fromJson(json);
        final serializedJson = account.toJson();

        expect(serializedJson['account_number'], json['account_number']);
        expect(serializedJson['account_name'], json['account_name']);
        expect(serializedJson['account_type'], json['account_type']);
        expect(serializedJson['company_id'], json['company_id']);
      });
    });
  });
}
