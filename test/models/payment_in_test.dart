import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/payment_in.dart';

void main() {
  group('PaymentIn', () {
    final testPaymentDate = DateTime(2024, 3, 25);
    final testPayment = PaymentIn(
      paymentInId: 1,
      invoiceId: 1,
      paymentDate: testPaymentDate,
      amount: 1000.00,
      currencyCode: 'USD',
      companyId: 1,
    );

    group('Constructor', () {
      test('should create payment with required fields only', () {
        final payment = PaymentIn(
          paymentInId: 1,
          invoiceId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
        );

        expect(payment.paymentInId, 1);
        expect(payment.invoiceId, 1);
        expect(payment.paymentDate, testPaymentDate);
        expect(payment.amount, 1000.00);
        expect(payment.currencyCode, 'USD');
        expect(payment.companyId, null);
      });

      test('should create payment with all fields', () {
        expect(testPayment.paymentInId, 1);
        expect(testPayment.invoiceId, 1);
        expect(testPayment.paymentDate, testPaymentDate);
        expect(testPayment.amount, 1000.00);
        expect(testPayment.currencyCode, 'USD');
        expect(testPayment.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should convert payment to JSON with all fields', () {
        final json = testPayment.toJson();

        expect(json['paymentInId'], 1);
        expect(json['invoiceId'], 1);
        expect(json['paymentDate'], '2024-03-25T00:00:00.000');
        expect(json['amount'], 1000.00);
        expect(json['currencyCode'], 'USD');
        expect(json['companyId'], 1);
      });

      test('should create payment from JSON with all fields', () {
        final json = {
          'paymentInId': 1,
          'invoiceId': 1,
          'paymentDate': '2024-03-25T00:00:00.000',
          'amount': 1000.00,
          'currencyCode': 'USD',
          'companyId': 1,
        };

        final payment = PaymentIn.fromJson(json);
        expect(payment, testPayment);
      });

      test('should handle JSON with minimal fields', () {
        final json = {
          'paymentInId': 1,
          'invoiceId': 1,
          'paymentDate': '2024-03-25T00:00:00.000',
          'amount': 1000.00,
          'currencyCode': 'USD',
        };

        final payment = PaymentIn.fromJson(json);
        expect(payment.paymentInId, 1);
        expect(payment.invoiceId, 1);
        expect(payment.paymentDate, testPaymentDate);
        expect(payment.amount, 1000.00);
        expect(payment.currencyCode, 'USD');
        expect(payment.companyId, null);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedPayment = testPayment.copyWith(
          amount: 1500.00,
          companyId: 2,
        );

        // Original should be unchanged
        expect(testPayment.amount, 1000.00);
        expect(testPayment.companyId, 1);

        // New instance should have updated values
        expect(updatedPayment.paymentInId, 1);
        expect(updatedPayment.invoiceId, 1);
        expect(updatedPayment.paymentDate, testPaymentDate);
        expect(updatedPayment.amount, 1500.00);
        expect(updatedPayment.currencyCode, 'USD');
        expect(updatedPayment.companyId, 2);
      });

      test('should handle setting nullable fields to null', () {
        final updatedPayment = testPayment.copyWith(companyId: null);

        expect(updatedPayment.companyId, null);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final payment1 = PaymentIn(
          paymentInId: 1,
          invoiceId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
          companyId: 1,
        );

        final payment2 = PaymentIn(
          paymentInId: 1,
          invoiceId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
          companyId: 1,
        );

        expect(payment1, equals(payment2));
        expect(payment1.hashCode, equals(payment2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final payment1 = testPayment;
        final payment2 = PaymentIn(
          paymentInId: 2,
          invoiceId: 2,
          paymentDate: DateTime(2024, 3, 26),
          amount: 2000.00,
          currencyCode: 'EUR',
          companyId: 2,
        );

        expect(payment1, isNot(payment2));
        expect(payment1.hashCode, isNot(payment2.hashCode));
      });
    });
  });
}
