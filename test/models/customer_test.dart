import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/customer.dart';

void main() {
  group('Customer', () {
    const customerJson = '''
    {
      "customerId": "C001",
      "customerName": "Acme Corporation",
      "address": "123 Main St, Anytown, USA",
      "contactPerson": "John Doe"
    }
    ''';

    group('JSON serialization', () {
      test('should create Customer from JSON and convert back', () {
        // Parse JSON
        final parsedJson = json.decode(customerJson) as Map<String, dynamic>;

        // Create model from JSON
        final customer = Customer.fromJson(parsedJson);

        // Verify properties
        expect(customer.customerId, 'C001');
        expect(customer.customerName, 'Acme Corporation');
        expect(customer.address, '123 Main St, Anytown, USA');
        expect(customer.contactPerson, '<PERSON>');

        // Convert back to JSON
        final serializedJson = customer.toJson();

        // Verify serialization
        expect(serializedJson['customerId'], 'C001');
        expect(serializedJson['customerName'], 'Acme Corporation');
        expect(serializedJson['address'], '123 Main St, Anytown, USA');
        expect(serializedJson['contactPerson'], 'John Doe');
      });

      test('should handle minimal JSON with required fields only', () {
        final minimalJson = {
          'customerId': 'C002',
          'customerName': 'Minimal Corp',
        };

        final customer = Customer.fromJson(minimalJson);

        expect(customer.customerId, 'C002');
        expect(customer.customerName, 'Minimal Corp');
        expect(customer.address, null);
        expect(customer.contactPerson, null);
      });
    });

    test('should create Customer with constructor', () {
      // Arrange: Create test data
      const customer = Customer(
        customerId: 'C001',
        customerName: 'Acme Corporation',
        address: '123 Main St, Anytown, USA',
        contactPerson: 'John Doe',
      );

      // Act: No action needed as we're testing constructor

      // Assert: Verify all fields are set correctly
      expect(customer.customerId, 'C001');
      expect(customer.customerName, 'Acme Corporation');
      expect(customer.address, '123 Main St, Anytown, USA');
      expect(customer.contactPerson, 'John Doe');
    });

    test('should copy Customer with copyWith', () {
      // Arrange: Create test data
      const customer = Customer(
        customerId: 'C001',
        customerName: 'Acme Corporation',
        address: '123 Main St, Anytown, USA',
        contactPerson: 'John Doe',
      );

      // Act: Create a copy with modified fields
      final updatedCustomer = customer.copyWith(
        customerName: 'Updated Acme Corp',
        contactPerson: 'Jane Smith',
      );

      // Assert: Verify original is unchanged
      expect(customer.customerId, 'C001');
      expect(customer.customerName, 'Acme Corporation');
      expect(customer.address, '123 Main St, Anytown, USA');
      expect(customer.contactPerson, 'John Doe');

      // Assert: Verify copy has updated values
      expect(updatedCustomer.customerId, 'C001');
      expect(updatedCustomer.customerName, 'Updated Acme Corp');
      expect(updatedCustomer.address, '123 Main St, Anytown, USA');
      expect(updatedCustomer.contactPerson, 'Jane Smith');
    });

    test('should handle equality and hashCode', () {
      const customer1 = Customer(
        customerId: 'C001',
        customerName: 'Acme Corporation',
        address: '123 Main St, Anytown, USA',
        contactPerson: 'John Doe',
      );

      const customer2 = Customer(
        customerId: 'C001',
        customerName: 'Acme Corporation',
        address: '123 Main St, Anytown, USA',
        contactPerson: 'John Doe',
      );

      const customer3 = Customer(
        customerId: 'C002',
        customerName: 'Different Corp',
        address: '456 Other St, Othertown, USA',
        contactPerson: 'Jane Smith',
      );

      // Same values should be equal
      expect(customer1, equals(customer2));
      expect(customer1.hashCode, equals(customer2.hashCode));

      // Different values should not be equal
      expect(customer1, isNot(customer3));
      expect(customer1.hashCode, isNot(customer3.hashCode));
    });
  });
}
