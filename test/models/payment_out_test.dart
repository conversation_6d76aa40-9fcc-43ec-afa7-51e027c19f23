import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/payment_out.dart';

void main() {
  group('PaymentOut', () {
    final testPaymentDate = DateTime(2024, 3, 25);
    final testPayment = PaymentOut(
      paymentOutId: 1,
      expenseId: 1,
      paymentDate: testPaymentDate,
      amount: 1000.00,
      currencyCode: 'USD',
      companyId: 1,
    );

    group('Constructor', () {
      test('should create payment with required fields only', () {
        final payment = PaymentOut(
          paymentOutId: 1,
          expenseId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
        );

        expect(payment.paymentOutId, 1);
        expect(payment.expenseId, 1);
        expect(payment.paymentDate, testPaymentDate);
        expect(payment.amount, 1000.00);
        expect(payment.currencyCode, 'USD');
        expect(payment.companyId, null);
      });

      test('should create payment with all fields', () {
        expect(testPayment.paymentOutId, 1);
        expect(testPayment.expenseId, 1);
        expect(testPayment.paymentDate, testPaymentDate);
        expect(testPayment.amount, 1000.00);
        expect(testPayment.currencyCode, 'USD');
        expect(testPayment.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should convert payment to JSON with all fields', () {
        final json = testPayment.toJson();

        expect(json['paymentOutId'], 1);
        expect(json['expenseId'], 1);
        expect(json['paymentDate'], '2024-03-25T00:00:00.000');
        expect(json['amount'], 1000.00);
        expect(json['currencyCode'], 'USD');
        expect(json['companyId'], 1);
      });

      test('should create payment from JSON with all fields', () {
        final json = {
          'paymentOutId': 1,
          'expenseId': 1,
          'paymentDate': '2024-03-25T00:00:00.000',
          'amount': 1000.00,
          'currencyCode': 'USD',
          'companyId': 1,
        };

        final payment = PaymentOut.fromJson(json);
        expect(payment, testPayment);
      });

      test('should handle JSON with minimal fields', () {
        final json = {
          'paymentOutId': 1,
          'expenseId': 1,
          'paymentDate': '2024-03-25T00:00:00.000',
          'amount': 1000.00,
          'currencyCode': 'USD',
        };

        final payment = PaymentOut.fromJson(json);
        expect(payment.paymentOutId, 1);
        expect(payment.expenseId, 1);
        expect(payment.paymentDate, testPaymentDate);
        expect(payment.amount, 1000.00);
        expect(payment.currencyCode, 'USD');
        expect(payment.companyId, null);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedPayment = testPayment.copyWith(
          amount: 1500.00,
          companyId: 2,
        );

        // Original should be unchanged
        expect(testPayment.amount, 1000.00);
        expect(testPayment.companyId, 1);

        // New instance should have updated values
        expect(updatedPayment.paymentOutId, 1);
        expect(updatedPayment.expenseId, 1);
        expect(updatedPayment.paymentDate, testPaymentDate);
        expect(updatedPayment.amount, 1500.00);
        expect(updatedPayment.currencyCode, 'USD');
        expect(updatedPayment.companyId, 2);
      });

      test('should handle setting nullable fields to null', () {
        final updatedPayment = testPayment.copyWith(companyId: null);

        expect(updatedPayment.companyId, null);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final payment1 = PaymentOut(
          paymentOutId: 1,
          expenseId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
          companyId: 1,
        );

        final payment2 = PaymentOut(
          paymentOutId: 1,
          expenseId: 1,
          paymentDate: testPaymentDate,
          amount: 1000.00,
          currencyCode: 'USD',
          companyId: 1,
        );

        expect(payment1, equals(payment2));
        expect(payment1.hashCode, equals(payment2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final payment1 = testPayment;
        final payment2 = PaymentOut(
          paymentOutId: 2,
          expenseId: 2,
          paymentDate: DateTime(2024, 3, 26),
          amount: 2000.00,
          currencyCode: 'EUR',
          companyId: 2,
        );

        expect(payment1, isNot(payment2));
        expect(payment1.hashCode, isNot(payment2.hashCode));
      });
    });
  });
}
