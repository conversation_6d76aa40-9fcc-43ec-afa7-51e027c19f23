import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/staff_member.dart';

void main() {
  group('StaffMember', () {
    const testStaff = StaffMember(
      staffId: 1,
      staffName: '<PERSON>',
      email: '<EMAIL>',
      companyId: 1,
    );

    group('Constructor', () {
      test('should create staff member with required fields only', () {
        const staff = StaffMember(
          staffId: 1,
          staffName: '<PERSON>',
          email: '<EMAIL>',
        );

        expect(staff.staffId, 1);
        expect(staff.staffName, '<PERSON>');
        expect(staff.email, '<EMAIL>');
        expect(staff.companyId, isNull);
      });

      test('should create staff member with all fields', () {
        const staff = StaffMember(
          staffId: 1,
          staffName: '<PERSON>',
          email: '<EMAIL>',
          companyId: 1,
        );

        expect(staff.staffId, 1);
        expect(staff.staffName, '<PERSON>');
        expect(staff.email, '<EMAIL>');
        expect(staff.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should convert staff member to JSON with all fields', () {
        final json = testStaff.toJson();
        expect(json, {
          'staffId': 1,
          'staffName': 'John Doe',
          'email': '<EMAIL>',
          'companyId': 1,
        });
      });

      test('should create staff member from JSON with all fields', () {
        final json = {
          'staffId': 1,
          'staffName': 'John Doe',
          'email': '<EMAIL>',
          'companyId': 1,
        };

        final staff = StaffMember.fromJson(json);
        expect(staff, testStaff);
      });

      test('should handle JSON without companyId', () {
        const staff = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
        );

        final json = staff.toJson();
        expect(json, {
          'staffId': 1,
          'staffName': 'John Doe',
          'email': '<EMAIL>',
          'companyId': null,
        });

        final fromJson = StaffMember.fromJson(json);
        expect(fromJson, staff);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedStaff = testStaff.copyWith(
          staffName: 'John Smith',
          email: '<EMAIL>',
        );

        // Original should be unchanged
        expect(testStaff.staffId, 1);
        expect(testStaff.staffName, 'John Doe');
        expect(testStaff.email, '<EMAIL>');
        expect(testStaff.companyId, 1);

        // New instance should have updated values
        expect(updatedStaff.staffId, 1);
        expect(updatedStaff.staffName, 'John Smith');
        expect(updatedStaff.email, '<EMAIL>');
        expect(updatedStaff.companyId, 1);
      });

      test('should handle null companyId in copyWith', () {
        final updatedStaff = testStaff.copyWith(companyId: null);
        expect(updatedStaff.companyId, isNull);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        const staff1 = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
          companyId: 1,
        );

        const staff2 = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
          companyId: 1,
        );

        expect(staff1, equals(staff2));
        expect(staff1.hashCode, equals(staff2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        const staff1 = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
          companyId: 1,
        );

        const staff2 = StaffMember(
          staffId: 2,
          staffName: 'Jane Smith',
          email: '<EMAIL>',
          companyId: 1,
        );

        expect(staff1, isNot(staff2));
        expect(staff1.hashCode, isNot(staff2.hashCode));
      });

      test('should consider instances with different companyIds unequal', () {
        const staff1 = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
          companyId: 1,
        );

        const staff2 = StaffMember(
          staffId: 1,
          staffName: 'John Doe',
          email: '<EMAIL>',
          companyId: 2,
        );

        expect(staff1, isNot(staff2));
        expect(staff1.hashCode, isNot(staff2.hashCode));
      });
    });
  });
}
