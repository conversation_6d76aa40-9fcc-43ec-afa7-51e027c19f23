import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/payment_method.dart';

void main() {
  group('PaymentMethod', () {
    group('JSON serialization', () {
      test('should convert enum values to snake_case JSON strings', () {
        // Test each enum value individually for better error reporting
        expect(
          PaymentMethod.creditCard.toJson(),
          'credit_card',
          reason: 'creditCard should convert to credit_card',
        );
        expect(
          PaymentMethod.cash.toJson(),
          'cash',
          reason: 'cash should convert to cash',
        );
        expect(
          PaymentMethod.bankTransfer.toJson(),
          'bank_transfer',
          reason: 'bankTransfer should convert to bank_transfer',
        );
        expect(
          PaymentMethod.check.toJson(),
          'check',
          reason: 'check should convert to check',
        );
        expect(
          PaymentMethod.other.toJson(),
          'other',
          reason: 'other should convert to other',
        );
      });

      test('should parse snake_case JSON strings to enum values', () {
        expect(
          PaymentMethodExtension.from<PERSON><PERSON>('credit_card'),
          PaymentMethod.creditCard,
          reason: 'credit_card should parse to creditCard',
        );
        expect(
          PaymentMethodExtension.fromJson('cash'),
          PaymentMethod.cash,
          reason: 'cash should parse to cash',
        );
        expect(
          PaymentMethodExtension.fromJson('bank_transfer'),
          PaymentMethod.bankTransfer,
          reason: 'bank_transfer should parse to bankTransfer',
        );
        expect(
          PaymentMethodExtension.fromJson('check'),
          PaymentMethod.check,
          reason: 'check should parse to check',
        );
        expect(
          PaymentMethodExtension.fromJson('other'),
          PaymentMethod.other,
          reason: 'other should parse to other',
        );
      });

      group('error handling', () {
        test(
          'should handle unknown values by returning PaymentMethod.other',
          () {
            expect(
              PaymentMethodExtension.fromJson('unknown'),
              PaymentMethod.other,
              reason: 'Unknown value should default to other',
            );
            expect(
              PaymentMethodExtension.fromJson('invalid_method'),
              PaymentMethod.other,
              reason: 'Invalid method should default to other',
            );
          },
        );

        test('should handle empty string by returning PaymentMethod.other', () {
          expect(
            PaymentMethodExtension.fromJson(''),
            PaymentMethod.other,
            reason: 'Empty string should default to other',
          );
        });

        test('should handle case sensitivity', () {
          expect(
            PaymentMethodExtension.fromJson('CASH'),
            PaymentMethod.other,
            reason: 'Uppercase value should default to other',
          );
          expect(
            PaymentMethodExtension.fromJson('Cash'),
            PaymentMethod.other,
            reason: 'Title case value should default to other',
          );
        });
      });

      test('should maintain consistency between toJson and fromJson', () {
        for (final method in PaymentMethod.values) {
          final json = method.toJson();
          final parsed = PaymentMethodExtension.fromJson(json);
          expect(
            parsed,
            method,
            reason:
                'Converting $method to JSON and back should return the same value',
          );
        }
      });
    });

    group('Display names', () {
      test('should return correct display names for each payment method', () {
        expect(
          PaymentMethod.creditCard.displayName,
          'Credit Card',
          reason: 'creditCard should display as Credit Card',
        );
        expect(
          PaymentMethod.cash.displayName,
          'Cash',
          reason: 'cash should display as Cash',
        );
        expect(
          PaymentMethod.bankTransfer.displayName,
          'Bank Transfer',
          reason: 'bankTransfer should display as Bank Transfer',
        );
        expect(
          PaymentMethod.check.displayName,
          'Check',
          reason: 'check should display as Check',
        );
        expect(
          PaymentMethod.other.displayName,
          'Other',
          reason: 'other should display as Other',
        );
      });

      test('should ensure all enum values have valid display names', () {
        for (final method in PaymentMethod.values) {
          expect(
            method.displayName,
            isNotNull,
            reason: 'Display name should not be null for $method',
          );
          expect(
            method.displayName,
            isNotEmpty,
            reason: 'Display name should not be empty for $method',
          );
          expect(
            method.displayName,
            isNot(contains('_')),
            reason: 'Display name should not contain underscores for $method',
          );
          expect(
            method.displayName[0],
            isUpperCase(),
            reason:
                'Display name should start with uppercase letter for $method',
          );
        }
      });

      test('should have unique display names', () {
        final displayNames =
            PaymentMethod.values.map((m) => m.displayName).toList();
        final uniqueDisplayNames = displayNames.toSet().toList();
        expect(
          displayNames.length,
          uniqueDisplayNames.length,
          reason: 'Each payment method should have a unique display name',
        );
      });
    });

    group('Enum completeness', () {
      test('should have all expected payment methods', () {
        final expectedMethods = {
          'creditCard',
          'cash',
          'bankTransfer',
          'check',
          'other',
        };

        final actualMethods =
            PaymentMethod.values
                .map((m) => m.toString().split('.').last)
                .toSet();

        expect(
          actualMethods,
          equals(expectedMethods),
          reason: 'PaymentMethod enum should contain all expected values',
        );
      });

      test('should have consistent number of methods across properties', () {
        final enumCount = PaymentMethod.values.length;
        final toJsonCaseCount =
            PaymentMethod.values.map((m) => m.toJson()).toSet().length;
        final displayNameCount =
            PaymentMethod.values.map((m) => m.displayName).toSet().length;

        expect(
          toJsonCaseCount,
          equals(enumCount),
          reason: 'Each enum value should have a unique JSON representation',
        );
        expect(
          displayNameCount,
          equals(enumCount),
          reason: 'Each enum value should have a unique display name',
        );
      });
    });
  });
}

Matcher isUpperCase() =>
    predicate<String>((value) => value.toUpperCase() == value, 'is uppercase');
