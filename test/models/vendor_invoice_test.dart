import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/vendor_invoice.dart';

void main() {
  group('VendorInvoice', () {
    final testInvoiceDate = DateTime(2024, 1, 1);
    final testDueDate = DateTime(2024, 2, 1);
    final testInvoice = VendorInvoice(
      invoiceId: 1,
      vendorId: 'VENDOR001',
      invoiceNumber: 'INV-001',
      invoiceDate: testInvoiceDate,
      dueDate: testDueDate,
      amount: 1000.00,
      currencyCode: 'USD',
      expenseAccountNumber: '5000',
      taxAmount: 100.00,
      projectId: 1,
      staffId: 1,
      companyId: 1,
      isPaid: false,
    );

    group('Constructor', () {
      test('should create vendor invoice with required fields only', () {
        final invoice = VendorInvoice(
          invoiceId: 1,
          vendorId: 'VENDOR001',
          invoiceNumber: 'INV-001',
          invoiceDate: testInvoiceDate,
          dueDate: testDueDate,
          amount: 1000.00,
          currencyCode: 'USD',
          expenseAccountNumber: '5000',
        );

        expect(invoice.invoiceId, 1);
        expect(invoice.vendorId, 'VENDOR001');
        expect(invoice.invoiceNumber, 'INV-001');
        expect(invoice.invoiceDate, testInvoiceDate);
        expect(invoice.dueDate, testDueDate);
        expect(invoice.amount, 1000.00);
        expect(invoice.currencyCode, 'USD');
        expect(invoice.expenseAccountNumber, '5000');
        expect(invoice.taxAmount, isNull);
        expect(invoice.projectId, isNull);
        expect(invoice.staffId, isNull);
        expect(invoice.companyId, isNull);
        expect(invoice.isPaid, isNull);
      });

      test('should create vendor invoice with all fields', () {
        expect(testInvoice.invoiceId, 1);
        expect(testInvoice.vendorId, 'VENDOR001');
        expect(testInvoice.invoiceNumber, 'INV-001');
        expect(testInvoice.invoiceDate, testInvoiceDate);
        expect(testInvoice.dueDate, testDueDate);
        expect(testInvoice.amount, 1000.00);
        expect(testInvoice.currencyCode, 'USD');
        expect(testInvoice.expenseAccountNumber, '5000');
        expect(testInvoice.taxAmount, 100.00);
        expect(testInvoice.projectId, 1);
        expect(testInvoice.staffId, 1);
        expect(testInvoice.companyId, 1);
        expect(testInvoice.isPaid, false);
      });
    });

    group('JSON serialization', () {
      test('should convert vendor invoice to JSON with all fields', () {
        final json = testInvoice.toJson();
        expect(json, {
          'invoice_id': 1,
          'vendor_id': 'VENDOR001',
          'invoice_number': 'INV-001',
          'invoice_date': testInvoiceDate.toIso8601String(),
          'due_date': testDueDate.toIso8601String(),
          'amount': 1000.00,
          'currency_code': 'USD',
          'expense_account_number': '5000',
          'tax_amount': 100.00,
          'project_id': 1,
          'staff_id': 1,
          'company_id': 1,
          'is_paid': false,
        });
      });

      test('should create vendor invoice from JSON with all fields', () {
        final json = {
          'invoice_id': 1,
          'vendor_id': 'VENDOR001',
          'invoice_number': 'INV-001',
          'invoice_date': testInvoiceDate.toIso8601String(),
          'due_date': testDueDate.toIso8601String(),
          'amount': 1000.00,
          'currency_code': 'USD',
          'expense_account_number': '5000',
          'tax_amount': 100.00,
          'project_id': 1,
          'staff_id': 1,
          'company_id': 1,
          'is_paid': false,
        };

        final invoice = VendorInvoice.fromJson(json);
        expect(invoice, testInvoice);
      });

      test('should handle JSON with only required fields', () {
        final json = {
          'invoice_id': 1,
          'vendor_id': 'VENDOR001',
          'invoice_number': 'INV-001',
          'invoice_date': testInvoiceDate.toIso8601String(),
          'due_date': testDueDate.toIso8601String(),
          'amount': 1000.00,
          'currency_code': 'USD',
          'expense_account_number': '5000',
        };

        final invoice = VendorInvoice.fromJson(json);
        expect(invoice.invoiceId, 1);
        expect(invoice.vendorId, 'VENDOR001');
        expect(invoice.invoiceNumber, 'INV-001');
        expect(invoice.invoiceDate, testInvoiceDate);
        expect(invoice.dueDate, testDueDate);
        expect(invoice.amount, 1000.00);
        expect(invoice.currencyCode, 'USD');
        expect(invoice.expenseAccountNumber, '5000');
        expect(invoice.taxAmount, isNull);
        expect(invoice.projectId, isNull);
        expect(invoice.staffId, isNull);
        expect(invoice.companyId, isNull);
        expect(invoice.isPaid, isNull);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedInvoice = testInvoice.copyWith(
          amount: 1500.00,
          taxAmount: 150.00,
          isPaid: true,
        );

        // Original should be unchanged
        expect(testInvoice.amount, 1000.00);
        expect(testInvoice.taxAmount, 100.00);
        expect(testInvoice.isPaid, false);

        // New instance should have updated values
        expect(updatedInvoice.invoiceId, 1);
        expect(updatedInvoice.vendorId, 'VENDOR001');
        expect(updatedInvoice.invoiceNumber, 'INV-001');
        expect(updatedInvoice.amount, 1500.00);
        expect(updatedInvoice.taxAmount, 150.00);
        expect(updatedInvoice.isPaid, true);
      });

      test('should handle setting nullable fields to null', () {
        final updatedInvoice = testInvoice.copyWith(
          taxAmount: null,
          projectId: null,
          staffId: null,
          companyId: null,
          isPaid: null,
        );

        expect(updatedInvoice.taxAmount, isNull);
        expect(updatedInvoice.projectId, isNull);
        expect(updatedInvoice.staffId, isNull);
        expect(updatedInvoice.companyId, isNull);
        expect(updatedInvoice.isPaid, isNull);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final invoice1 = VendorInvoice(
          invoiceId: 1,
          vendorId: 'VENDOR001',
          invoiceNumber: 'INV-001',
          invoiceDate: testInvoiceDate,
          dueDate: testDueDate,
          amount: 1000.00,
          currencyCode: 'USD',
          expenseAccountNumber: '5000',
          taxAmount: 100.00,
          projectId: 1,
          staffId: 1,
          companyId: 1,
          isPaid: false,
        );

        final invoice2 = VendorInvoice(
          invoiceId: 1,
          vendorId: 'VENDOR001',
          invoiceNumber: 'INV-001',
          invoiceDate: testInvoiceDate,
          dueDate: testDueDate,
          amount: 1000.00,
          currencyCode: 'USD',
          expenseAccountNumber: '5000',
          taxAmount: 100.00,
          projectId: 1,
          staffId: 1,
          companyId: 1,
          isPaid: false,
        );

        expect(invoice1, equals(invoice2));
        expect(invoice1.hashCode, equals(invoice2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final invoice1 = testInvoice;
        final invoice2 = VendorInvoice(
          invoiceId: 2,
          vendorId: 'VENDOR002',
          invoiceNumber: 'INV-002',
          invoiceDate: DateTime(2024, 1, 2),
          dueDate: DateTime(2024, 2, 2),
          amount: 2000.00,
          currencyCode: 'EUR',
          expenseAccountNumber: '5001',
          taxAmount: 200.00,
          projectId: 2,
          staffId: 2,
          companyId: 2,
          isPaid: true,
        );

        expect(invoice1, isNot(invoice2));
        expect(invoice1.hashCode, isNot(invoice2.hashCode));
      });
    });
  });
}
