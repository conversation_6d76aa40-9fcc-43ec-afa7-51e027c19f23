import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';

void main() {
  group('VendorInvoiceLineItem', () {
    final testCreatedAt = DateTime(2024, 1, 1);
    final testUpdatedAt = DateTime(2024, 1, 2);
    final testLineItem = VendorInvoiceLineItem(
      lineItemId: 1,
      invoiceId: 1,
      accountNumber: '5000',
      description: 'Test line item',
      amount: 1000.00,
      taxAmount: 100.00,
      companyId: 1,
      createdAt: testCreatedAt,
      updatedAt: testUpdatedAt,
    );

    group('Constructor', () {
      test('should create line item with required fields only', () {
        const lineItem = VendorInvoiceLineItem(
          lineItemId: 1,
          invoiceId: 1,
          accountNumber: '5000',
          description: 'Test line item',
          amount: 1000.00,
          taxAmount: 100.00,
          companyId: 1,
        );

        expect(lineItem.lineItemId, 1);
        expect(lineItem.invoiceId, 1);
        expect(lineItem.accountNumber, '5000');
        expect(lineItem.description, 'Test line item');
        expect(lineItem.amount, 1000.00);
        expect(lineItem.taxAmount, 100.00);
        expect(lineItem.companyId, 1);
        expect(lineItem.createdAt, isNull);
        expect(lineItem.updatedAt, isNull);
      });

      test('should create line item with all fields', () {
        expect(testLineItem.lineItemId, 1);
        expect(testLineItem.invoiceId, 1);
        expect(testLineItem.accountNumber, '5000');
        expect(testLineItem.description, 'Test line item');
        expect(testLineItem.amount, 1000.00);
        expect(testLineItem.taxAmount, 100.00);
        expect(testLineItem.companyId, 1);
        expect(testLineItem.createdAt, testCreatedAt);
        expect(testLineItem.updatedAt, testUpdatedAt);
      });
    });

    group('JSON serialization', () {
      test('should convert line item to JSON with all fields', () {
        final json = testLineItem.toJson();
        expect(json, {
          'line_item_id': 1,
          'invoice_id': 1,
          'account_number': '5000',
          'description': 'Test line item',
          'amount': 1000.00,
          'tax_amount': 100.00,
          'company_id': 1,
          'created_at': testCreatedAt.toIso8601String(),
          'updated_at': testUpdatedAt.toIso8601String(),
        });
      });

      test('should create line item from JSON with all fields', () {
        final json = {
          'line_item_id': 1,
          'invoice_id': 1,
          'account_number': '5000',
          'description': 'Test line item',
          'amount': 1000.00,
          'tax_amount': 100.00,
          'company_id': 1,
          'created_at': testCreatedAt.toIso8601String(),
          'updated_at': testUpdatedAt.toIso8601String(),
        };

        final lineItem = VendorInvoiceLineItem.fromJson(json);
        expect(lineItem, testLineItem);
      });

      test('should handle JSON with only required fields', () {
        final json = {
          'line_item_id': 1,
          'invoice_id': 1,
          'account_number': '5000',
          'description': 'Test line item',
          'amount': 1000.00,
          'tax_amount': 100.00,
          'company_id': 1,
        };

        final lineItem = VendorInvoiceLineItem.fromJson(json);
        expect(lineItem.lineItemId, 1);
        expect(lineItem.invoiceId, 1);
        expect(lineItem.accountNumber, '5000');
        expect(lineItem.description, 'Test line item');
        expect(lineItem.amount, 1000.00);
        expect(lineItem.taxAmount, 100.00);
        expect(lineItem.companyId, 1);
        expect(lineItem.createdAt, isNull);
        expect(lineItem.updatedAt, isNull);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedLineItem = testLineItem.copyWith(
          description: 'Updated description',
          amount: 1500.00,
          taxAmount: 150.00,
        );

        // Original should be unchanged
        expect(testLineItem.description, 'Test line item');
        expect(testLineItem.amount, 1000.00);
        expect(testLineItem.taxAmount, 100.00);

        // New instance should have updated values
        expect(updatedLineItem.lineItemId, 1);
        expect(updatedLineItem.invoiceId, 1);
        expect(updatedLineItem.accountNumber, '5000');
        expect(updatedLineItem.description, 'Updated description');
        expect(updatedLineItem.amount, 1500.00);
        expect(updatedLineItem.taxAmount, 150.00);
        expect(updatedLineItem.companyId, 1);
        expect(updatedLineItem.createdAt, testCreatedAt);
        expect(updatedLineItem.updatedAt, testUpdatedAt);
      });

      test('should handle setting nullable fields to null', () {
        final updatedLineItem = testLineItem.copyWith(
          createdAt: null,
          updatedAt: null,
        );

        expect(updatedLineItem.createdAt, isNull);
        expect(updatedLineItem.updatedAt, isNull);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final lineItem1 = VendorInvoiceLineItem(
          lineItemId: 1,
          invoiceId: 1,
          accountNumber: '5000',
          description: 'Test line item',
          amount: 1000.00,
          taxAmount: 100.00,
          companyId: 1,
          createdAt: testCreatedAt,
          updatedAt: testUpdatedAt,
        );

        final lineItem2 = VendorInvoiceLineItem(
          lineItemId: 1,
          invoiceId: 1,
          accountNumber: '5000',
          description: 'Test line item',
          amount: 1000.00,
          taxAmount: 100.00,
          companyId: 1,
          createdAt: testCreatedAt,
          updatedAt: testUpdatedAt,
        );

        expect(lineItem1, equals(lineItem2));
        expect(lineItem1.hashCode, equals(lineItem2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final lineItem1 = testLineItem;
        final lineItem2 = VendorInvoiceLineItem(
          lineItemId: 2,
          invoiceId: 2,
          accountNumber: '5001',
          description: 'Different line item',
          amount: 2000.00,
          taxAmount: 200.00,
          companyId: 2,
          createdAt: DateTime(2024, 1, 3),
          updatedAt: DateTime(2024, 1, 4),
        );

        expect(lineItem1, isNot(lineItem2));
        expect(lineItem1.hashCode, isNot(lineItem2.hashCode));
      });
    });
  });
}
