import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/utils/test_data.dart';

void main() {
  group('Company Model', () {
    test('can be instantiated', () {
      expect(TestData.testCompany.companyId, 1);
      expect(TestData.testCompany.companyName, 'Test Company');
      expect(TestData.testCompany.organizationNumber, 'ORG-123');
      expect(TestData.testCompany.phone, '+1234567890');
      expect(TestData.testCompany.email, '<EMAIL>');
      expect(TestData.testCompany.address, 'Test Address');
      expect(TestData.testCompany.zipCode, '12345');
      expect(TestData.testCompany.city, 'Test City');
      expect(TestData.testCompany.country, 'Test Country');
    });

    test('can be serialized to JSON', () {
      final json = TestData.testCompany.toJson();

      expect(json['company_id'], 1);
      expect(json['company_name'], 'Test Company');
      expect(json['organization_number'], 'ORG-123');
      expect(json['phone'], '+1234567890');
      expect(json['email'], '<EMAIL>');
      expect(json['address'], 'Test Address');
      expect(json['zip_code'], '12345');
      expect(json['city'], 'Test City');
      expect(json['country'], 'Test Country');
    });

    test('can be deserialized from JSON with snake_case', () {
      final json = {
        'company_id': 1,
        'company_name': 'Test Company',
        'organization_number': 'ORG-123',
        'phone': '+1234567890',
        'email': '<EMAIL>',
        'address': 'Test Address',
        'zip_code': '12345',
        'city': 'Test City',
        'country': 'Test Country',
      };

      final company = Company.fromJson(json);

      expect(company.companyId, 1);
      expect(company.companyName, 'Test Company');
      expect(company.organizationNumber, 'ORG-123');
      expect(company.phone, '+1234567890');
      expect(company.email, '<EMAIL>');
      expect(company.address, 'Test Address');
      expect(company.zipCode, '12345');
      expect(company.city, 'Test City');
      expect(company.country, 'Test Country');
    });

    test('copyWith works correctly', () {
      final updatedCompany = TestData.testCompany.copyWith(
        companyName: 'Updated Company',
        address: 'Updated Address',
      );

      // Original should be unchanged
      expect(TestData.testCompany.companyId, 1);
      expect(TestData.testCompany.companyName, 'Test Company');
      expect(TestData.testCompany.address, 'Test Address');

      // New instance should have updated values
      expect(updatedCompany.companyId, 1);
      expect(updatedCompany.companyName, 'Updated Company');
      expect(updatedCompany.address, 'Updated Address');
      expect(updatedCompany.organizationNumber, 'ORG-123');
      expect(updatedCompany.phone, '+1234567890');
      expect(updatedCompany.email, '<EMAIL>');
      expect(updatedCompany.zipCode, '12345');
      expect(updatedCompany.city, 'Test City');
      expect(updatedCompany.country, 'Test Country');
    });

    test('equality works correctly', () {
      // Same values should be equal
      expect(TestData.testCompany, TestData.testCompany);
      expect(TestData.testCompany.hashCode, TestData.testCompany.hashCode);

      // Different values should not be equal
      expect(TestData.testCompany, isNot(TestData.testCompany2));
      expect(
        TestData.testCompany.hashCode,
        isNot(TestData.testCompany2.hashCode),
      );
    });
  });
}
