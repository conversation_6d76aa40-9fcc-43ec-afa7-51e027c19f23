import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/exchange_rate.dart';

void main() {
  group('ExchangeRate', () {
    final testDate = DateTime(2024, 1, 1);
    final testRate = ExchangeRate(
      rateId: 1,
      fromCurrency: 'USD',
      toCurrency: 'EUR',
      rate: 0.92,
      effectiveDate: testDate,
      companyId: 1,
    );

    group('Constructor', () {
      test('should create exchange rate with all required fields', () {
        final rate = ExchangeRate(
          rateId: 1,
          fromCurrency: 'USD',
          toCurrency: 'EUR',
          rate: 0.92,
          effectiveDate: testDate,
          companyId: 1,
        );

        expect(rate.rateId, 1);
        expect(rate.fromCurrency, 'USD');
        expect(rate.toCurrency, 'EUR');
        expect(rate.rate, 0.92);
        expect(rate.effectiveDate, testDate);
        expect(rate.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should convert exchange rate to JSON', () {
        final json = testRate.toJson();
        expect(json, {
          'rate_id': 1,
          'from_currency': 'USD',
          'to_currency': 'EUR',
          'rate': 0.92,
          'effective_date': testDate.toIso8601String(),
          'company_id': 1,
        });
      });

      test('should create exchange rate from JSON', () {
        final json = {
          'rate_id': 1,
          'from_currency': 'USD',
          'to_currency': 'EUR',
          'rate': 0.92,
          'effective_date': testDate.toIso8601String(),
          'company_id': 1,
        };

        final rate = ExchangeRate.fromJson(json);
        expect(rate, testRate);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedRate = testRate.copyWith(
          rate: 0.93,
          effectiveDate: DateTime(2024, 1, 2),
        );

        // Original should be unchanged
        expect(testRate.rate, 0.92);
        expect(testRate.effectiveDate, testDate);

        // New instance should have updated values
        expect(updatedRate.rateId, 1);
        expect(updatedRate.fromCurrency, 'USD');
        expect(updatedRate.toCurrency, 'EUR');
        expect(updatedRate.rate, 0.93);
        expect(updatedRate.effectiveDate, DateTime(2024, 1, 2));
        expect(updatedRate.companyId, 1);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final rate1 = ExchangeRate(
          rateId: 1,
          fromCurrency: 'USD',
          toCurrency: 'EUR',
          rate: 0.92,
          effectiveDate: testDate,
          companyId: 1,
        );

        final rate2 = ExchangeRate(
          rateId: 1,
          fromCurrency: 'USD',
          toCurrency: 'EUR',
          rate: 0.92,
          effectiveDate: testDate,
          companyId: 1,
        );

        expect(rate1, equals(rate2));
        expect(rate1.hashCode, equals(rate2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final rate1 = ExchangeRate(
          rateId: 1,
          fromCurrency: 'USD',
          toCurrency: 'EUR',
          rate: 0.92,
          effectiveDate: testDate,
          companyId: 1,
        );

        final rate2 = ExchangeRate(
          rateId: 2,
          fromCurrency: 'EUR',
          toCurrency: 'USD',
          rate: 1.09,
          effectiveDate: DateTime(2024, 1, 2),
          companyId: 1,
        );

        expect(rate1, isNot(rate2));
        expect(rate1.hashCode, isNot(rate2.hashCode));
      });
    });
  });
}
