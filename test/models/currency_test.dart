import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/currency.dart';

void main() {
  group('Currency', () {
    const testCurrency = Currency(
      currencyCode: 'USD',
      currencyName: 'US Dollar',
    );

    group('Constructor', () {
      test('should create Currency with valid values', () {
        const currency = Currency(currencyCode: 'EUR', currencyName: 'Euro');

        expect(currency.currencyCode, 'EUR');
        expect(currency.currencyName, 'Euro');
      });

      test('should create Currency with common currency codes', () {
        const currencies = [
          Currency(currencyCode: 'USD', currencyName: 'US Dollar'),
          Currency(currencyCode: 'EUR', currencyName: 'Euro'),
          Currency(currencyCode: 'GBP', currencyName: 'British Pound'),
          Currency(currencyCode: 'JPY', currencyName: 'Japanese Yen'),
          Currency(currencyCode: 'CNY', currencyName: 'Chinese Yuan'),
        ];

        for (final currency in currencies) {
          expect(
            currency.currencyCode.length,
            3,
            reason:
                'Currency code should be 3 characters for ${currency.currencyCode}',
          );
          expect(
            currency.currencyName.isNotEmpty,
            true,
            reason:
                'Currency name should not be empty for ${currency.currencyCode}',
          );
        }
      });

      test('should handle special characters in currency names', () {
        const currency = Currency(
          currencyCode: 'NOK',
          currencyName: 'Norwegian Krone (kr)',
        );

        expect(currency.currencyCode, 'NOK');
        expect(currency.currencyName, 'Norwegian Krone (kr)');
      });
    });

    group('JSON serialization', () {
      test('should create Currency from JSON with all fields', () {
        const json = {'currencyCode': 'USD', 'currencyName': 'US Dollar'};

        final currency = Currency.fromJson(json);

        expect(
          currency.currencyCode,
          'USD',
          reason: 'Currency code should match JSON value',
        );
        expect(
          currency.currencyName,
          'US Dollar',
          reason: 'Currency name should match JSON value',
        );
      });

      test('should convert Currency to JSON with all fields', () {
        final json = testCurrency.toJson();

        expect(
          json['currencyCode'],
          'USD',
          reason: 'JSON should contain correct currency code',
        );
        expect(
          json['currencyName'],
          'US Dollar',
          reason: 'JSON should contain correct currency name',
        );
      });

      test('should maintain data integrity through JSON conversion', () {
        const jsonString = '''
        {
          "currencyCode": "EUR",
          "currencyName": "Euro"
        }
        ''';

        final parsedJson = json.decode(jsonString) as Map<String, dynamic>;
        final currency = Currency.fromJson(parsedJson);
        final serializedJson = currency.toJson();

        expect(
          serializedJson['currencyCode'],
          parsedJson['currencyCode'],
          reason:
              'Currency code should remain unchanged through JSON conversion',
        );
        expect(
          serializedJson['currencyName'],
          parsedJson['currencyName'],
          reason:
              'Currency name should remain unchanged through JSON conversion',
        );
      });

      test('should handle missing fields in JSON', () {
        const invalidJson = {
          'currencyCode': 'USD',
          // Missing currencyName
        };

        expect(
          () => Currency.fromJson(invalidJson),
          throwsA(isA<TypeError>()),
          reason: 'Should throw TypeError for missing required fields',
        );
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedCurrency = testCurrency.copyWith(
          currencyCode: 'EUR',
          currencyName: 'Euro',
        );

        // Original should be unchanged
        expect(
          testCurrency.currencyCode,
          'USD',
          reason: 'Original currency code should remain unchanged',
        );
        expect(
          testCurrency.currencyName,
          'US Dollar',
          reason: 'Original currency name should remain unchanged',
        );

        // New instance should have updated values
        expect(
          updatedCurrency.currencyCode,
          'EUR',
          reason: 'New currency code should be updated',
        );
        expect(
          updatedCurrency.currencyName,
          'Euro',
          reason: 'New currency name should be updated',
        );
      });

      test('should handle partial updates', () {
        final updatedCode = testCurrency.copyWith(currencyCode: 'EUR');
        final updatedName = testCurrency.copyWith(currencyName: 'Euro');

        expect(updatedCode.currencyCode, 'EUR');
        expect(updatedCode.currencyName, testCurrency.currencyName);

        expect(updatedName.currencyCode, testCurrency.currencyCode);
        expect(updatedName.currencyName, 'Euro');
      });

      test('should create identical copy when no parameters provided', () {
        final copy = testCurrency.copyWith();
        expect(copy, equals(testCurrency));
        expect(copy.hashCode, equals(testCurrency.hashCode));
      });
    });

    group('Equality and hashCode', () {
      test('should consider identical currencies equal', () {
        const currency1 = Currency(
          currencyCode: 'USD',
          currencyName: 'US Dollar',
        );

        const currency2 = Currency(
          currencyCode: 'USD',
          currencyName: 'US Dollar',
        );

        expect(
          currency1,
          equals(currency2),
          reason: 'Identical currencies should be equal',
        );
        expect(
          currency1.hashCode,
          equals(currency2.hashCode),
          reason: 'Identical currencies should have same hash code',
        );
      });

      test('should consider currencies with different codes unequal', () {
        const currency1 = Currency(
          currencyCode: 'USD',
          currencyName: 'US Dollar',
        );

        const currency2 = Currency(
          currencyCode: 'EUR',
          currencyName: 'US Dollar', // Same name, different code
        );

        expect(
          currency1,
          isNot(currency2),
          reason: 'Currencies with different codes should not be equal',
        );
        expect(
          currency1.hashCode,
          isNot(currency2.hashCode),
          reason:
              'Currencies with different codes should have different hash codes',
        );
      });

      test('should consider currencies with different names unequal', () {
        const currency1 = Currency(
          currencyCode: 'USD',
          currencyName: 'US Dollar',
        );

        const currency2 = Currency(
          currencyCode: 'USD',
          currencyName: 'United States Dollar', // Different name, same code
        );

        expect(
          currency1,
          isNot(currency2),
          reason: 'Currencies with different names should not be equal',
        );
        expect(
          currency1.hashCode,
          isNot(currency2.hashCode),
          reason:
              'Currencies with different names should have different hash codes',
        );
      });
    });

    test('should provide meaningful string representation', () {
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');

      expect(
        currency.toString(),
        'Currency(currencyCode: USD, currencyName: US Dollar)',
        reason: 'toString should include all currency properties',
      );
    });
  });
}
