import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/general_ledger.dart';

void main() {
  group('GeneralLedger', () {
    final testDate = DateTime(2024, 3, 25);
    final testLedger = GeneralLedger(
      ledgerId: 1,
      transactionId: 'T001',
      transactionDate: testDate,
      accountNumber: '1000',
      description: 'Test transaction',
      debit: 100.0,
      credit: 0.0,
      currencyCode: 'USD',
      projectId: 1,
      staffId: 2,
      taxAmount: 10.0,
      companyId: 3,
    );

    group('Constructor', () {
      test('should create ledger with required fields only', () {
        final ledger = GeneralLedger(
          ledgerId: 1,
          transactionId: 'T001',
          transactionDate: testDate,
          accountNumber: '1000',
          description: 'Test transaction',
          debit: 100.0,
          credit: 0.0,
          currencyCode: 'USD',
          companyId: 3,
        );

        expect(ledger.ledgerId, 1);
        expect(ledger.transactionId, 'T001');
        expect(ledger.transactionDate, testDate);
        expect(ledger.accountNumber, '1000');
        expect(ledger.description, 'Test transaction');
        expect(ledger.debit, 100.0);
        expect(ledger.credit, 0.0);
        expect(ledger.currencyCode, 'USD');
        expect(ledger.companyId, 3);
        expect(ledger.projectId, null);
        expect(ledger.staffId, null);
        expect(ledger.taxAmount, null);
      });

      test('should create ledger with all fields', () {
        expect(testLedger.ledgerId, 1);
        expect(testLedger.transactionId, 'T001');
        expect(testLedger.transactionDate, testDate);
        expect(testLedger.accountNumber, '1000');
        expect(testLedger.description, 'Test transaction');
        expect(testLedger.debit, 100.0);
        expect(testLedger.credit, 0.0);
        expect(testLedger.currencyCode, 'USD');
        expect(testLedger.projectId, 1);
        expect(testLedger.staffId, 2);
        expect(testLedger.taxAmount, 10.0);
        expect(testLedger.companyId, 3);
      });
    });

    group('JSON serialization', () {
      test('should convert ledger to JSON with all fields', () {
        final json = testLedger.toJson();

        expect(json['ledgerId'], 1);
        expect(json['transactionId'], 'T001');
        expect(json['transactionDate'], '2024-03-25T00:00:00.000');
        expect(json['accountNumber'], '1000');
        expect(json['description'], 'Test transaction');
        expect(json['debit'], 100.0);
        expect(json['credit'], 0.0);
        expect(json['currencyCode'], 'USD');
        expect(json['projectId'], 1);
        expect(json['staffId'], 2);
        expect(json['taxAmount'], 10.0);
        expect(json['companyId'], 3);
      });

      test('should create ledger from JSON with all fields', () {
        final json = {
          'ledgerId': 1,
          'transactionId': 'T001',
          'transactionDate': '2024-03-25T00:00:00.000',
          'accountNumber': '1000',
          'description': 'Test transaction',
          'debit': 100.0,
          'credit': 0.0,
          'currencyCode': 'USD',
          'projectId': 1,
          'staffId': 2,
          'taxAmount': 10.0,
          'companyId': 3,
        };

        final ledger = GeneralLedger.fromJson(json);
        expect(ledger, testLedger);
      });

      test('should handle JSON with minimal fields', () {
        final json = {
          'ledgerId': 1,
          'transactionId': 'T001',
          'transactionDate': '2024-03-25T00:00:00.000',
          'accountNumber': '1000',
          'description': 'Test transaction',
          'debit': 100.0,
          'credit': 0.0,
          'currencyCode': 'USD',
          'companyId': 3,
        };

        final ledger = GeneralLedger.fromJson(json);
        expect(ledger.ledgerId, 1);
        expect(ledger.transactionId, 'T001');
        expect(ledger.transactionDate, testDate);
        expect(ledger.accountNumber, '1000');
        expect(ledger.description, 'Test transaction');
        expect(ledger.debit, 100.0);
        expect(ledger.credit, 0.0);
        expect(ledger.currencyCode, 'USD');
        expect(ledger.companyId, 3);
        expect(ledger.projectId, null);
        expect(ledger.staffId, null);
        expect(ledger.taxAmount, null);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedLedger = testLedger.copyWith(
          debit: 200.0,
          credit: 50.0,
          taxAmount: 20.0,
        );

        // Original should be unchanged
        expect(testLedger.debit, 100.0);
        expect(testLedger.credit, 0.0);
        expect(testLedger.taxAmount, 10.0);

        // New instance should have updated values
        expect(updatedLedger.ledgerId, 1);
        expect(updatedLedger.transactionId, 'T001');
        expect(updatedLedger.transactionDate, testDate);
        expect(updatedLedger.accountNumber, '1000');
        expect(updatedLedger.description, 'Test transaction');
        expect(updatedLedger.debit, 200.0);
        expect(updatedLedger.credit, 50.0);
        expect(updatedLedger.currencyCode, 'USD');
        expect(updatedLedger.projectId, 1);
        expect(updatedLedger.staffId, 2);
        expect(updatedLedger.taxAmount, 20.0);
        expect(updatedLedger.companyId, 3);
      });

      test('should handle setting nullable fields to null', () {
        final updatedLedger = testLedger.copyWith(
          projectId: null,
          staffId: null,
          taxAmount: null,
        );

        expect(updatedLedger.projectId, null);
        expect(updatedLedger.staffId, null);
        expect(updatedLedger.taxAmount, null);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final ledger1 = GeneralLedger(
          ledgerId: 1,
          transactionId: 'T001',
          transactionDate: testDate,
          accountNumber: '1000',
          description: 'Test transaction',
          debit: 100.0,
          credit: 0.0,
          currencyCode: 'USD',
          projectId: 1,
          staffId: 2,
          taxAmount: 10.0,
          companyId: 3,
        );

        final ledger2 = GeneralLedger(
          ledgerId: 1,
          transactionId: 'T001',
          transactionDate: testDate,
          accountNumber: '1000',
          description: 'Test transaction',
          debit: 100.0,
          credit: 0.0,
          currencyCode: 'USD',
          projectId: 1,
          staffId: 2,
          taxAmount: 10.0,
          companyId: 3,
        );

        expect(ledger1, equals(ledger2));
        expect(ledger1.hashCode, equals(ledger2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final ledger1 = testLedger;
        final ledger2 = GeneralLedger(
          ledgerId: 2,
          transactionId: 'T002',
          transactionDate: DateTime(2024, 3, 26),
          accountNumber: '2000',
          description: 'Different transaction',
          debit: 200.0,
          credit: 100.0,
          currencyCode: 'EUR',
          projectId: 2,
          staffId: 3,
          taxAmount: 20.0,
          companyId: 4,
        );

        expect(ledger1, isNot(ledger2));
        expect(ledger1.hashCode, isNot(ledger2.hashCode));
      });
    });
  });
}
