import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/project.dart';

void main() {
  group('Project', () {
    const projectJson = '''
    {
      "project_id": 101,
      "project_code": "PROJ-101",
      "project_name": "Website Redesign",
      "description": "Complete redesign of company website",
      "company_id": 1
    }
    ''';

    group('JSON serialization', () {
      test(
        'should create Project from JSON and convert back with all fields',
        () {
          // Parse JSON
          final parsedJson = json.decode(projectJson) as Map<String, dynamic>;

          // Create model from JSON
          final project = Project.fromJson(parsedJson);

          // Verify properties
          expect(project.projectId, 101);
          expect(project.projectCode, 'PROJ-101');
          expect(project.projectName, 'Website Redesign');
          expect(project.description, 'Complete redesign of company website');
          expect(project.companyId, 1);

          // Convert back to JSON
          final serializedJson = project.toJson();

          // Verify serialization
          expect(serializedJson['project_id'], 101);
          expect(serializedJson['project_code'], 'PROJ-101');
          expect(serializedJson['project_name'], 'Website Redesign');
          expect(
            serializedJson['description'],
            'Complete redesign of company website',
          );
          expect(serializedJson['company_id'], 1);
        },
      );

      test('should handle JSON with optional fields null', () {
        final minimalJson = {
          'project_id': 102,
          'project_code': 'PROJ-102',
          'project_name': 'Mobile App Development',
        };

        final project = Project.fromJson(minimalJson);

        expect(project.projectId, 102);
        expect(project.projectCode, 'PROJ-102');
        expect(project.projectName, 'Mobile App Development');
        expect(project.description, null);
        expect(project.companyId, null);
      });
    });

    test('should create Project with constructor', () {
      const project = Project(
        projectId: 101,
        projectCode: 'PROJ-101',
        projectName: 'Website Redesign',
        description: 'Complete redesign of company website',
        companyId: 1,
      );

      expect(project.projectId, 101);
      expect(project.projectCode, 'PROJ-101');
      expect(project.projectName, 'Website Redesign');
      expect(project.description, 'Complete redesign of company website');
      expect(project.companyId, 1);
    });

    test('should copy Project with copyWith', () {
      const project = Project(
        projectId: 101,
        projectCode: 'PROJ-101',
        projectName: 'Website Redesign',
        description: 'Complete redesign of company website',
      );

      final updatedProject = project.copyWith(
        projectName: 'Updated Website Redesign',
        description: 'Complete redesign with new branding',
        companyId: 1,
      );

      // Original should be unchanged
      expect(project.projectId, 101);
      expect(project.projectCode, 'PROJ-101');
      expect(project.projectName, 'Website Redesign');
      expect(project.description, 'Complete redesign of company website');
      expect(project.companyId, null);

      // New instance should have updated values
      expect(updatedProject.projectId, 101);
      expect(updatedProject.projectCode, 'PROJ-101');
      expect(updatedProject.projectName, 'Updated Website Redesign');
      expect(updatedProject.description, 'Complete redesign with new branding');
      expect(updatedProject.companyId, 1);
    });

    test('should handle equality and hashCode', () {
      const project1 = Project(
        projectId: 101,
        projectCode: 'PROJ-101',
        projectName: 'Website Redesign',
        description: 'Complete redesign of company website',
        companyId: 1,
      );

      const project2 = Project(
        projectId: 101,
        projectCode: 'PROJ-101',
        projectName: 'Website Redesign',
        description: 'Complete redesign of company website',
        companyId: 1,
      );

      const project3 = Project(
        projectId: 102,
        projectCode: 'PROJ-102',
        projectName: 'Mobile App Development',
        companyId: 2,
      );

      const project4 = Project(
        projectId: 101,
        projectCode: 'PROJ-101',
        projectName: 'Website Redesign',
        description: 'Complete redesign of company website',
        companyId: 2, // Different company ID
      );

      // Same values should be equal
      expect(project1, equals(project2));
      expect(project1.hashCode, equals(project2.hashCode));

      // Different values should not be equal
      expect(project1, isNot(project3));
      expect(project1.hashCode, isNot(project3.hashCode));

      // Same project but different company should not be equal
      expect(project1, isNot(project4));
      expect(project1.hashCode, isNot(project4.hashCode));
    });
  });
}
