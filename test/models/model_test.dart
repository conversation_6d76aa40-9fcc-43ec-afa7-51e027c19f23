import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/models/vendor.dart';

void main() {
  group('Company Model Tests', () {
    test('should create Company from JSON', () {
      final json = {
        'company_id': 1,
        'company_name': 'Test Company',
        'organization_number': 'ORG-123',
        'phone': '+*********0',
        'email': '<EMAIL>',
        'address': '123 Test St',
        'zip_code': '12345',
        'city': 'Test City',
        'country': 'Test Country',
      };

      final company = Company.fromJson(json);

      expect(company.companyId, 1);
      expect(company.companyName, 'Test Company');
      expect(company.organizationNumber, 'ORG-123');
      expect(company.phone, '+*********0');
      expect(company.email, '<EMAIL>');
      expect(company.address, '123 Test St');
      expect(company.zipCode, '12345');
      expect(company.city, 'Test City');
      expect(company.country, 'Test Country');
    });

    test('should convert Company to JSON', () {
      const company = Company(
        companyId: 1,
        companyName: 'Test Company',
        organizationNumber: 'ORG-123',
        phone: '+*********0',
        email: '<EMAIL>',
        address: 'Test Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      final json = company.toJson();

      expect(json['company_id'], 1);
      expect(json['company_name'], 'Test Company');
      expect(json['organization_number'], 'ORG-123');
      expect(json['phone'], '+*********0');
      expect(json['email'], '<EMAIL>');
      expect(json['address'], 'Test Address');
      expect(json['zip_code'], '12345');
      expect(json['city'], 'Test City');
      expect(json['country'], 'Test Country');
    });

    test('should convert JSON to Company', () {
      final json = {
        'company_id': 1,
        'company_name': 'Test Company',
        'organization_number': 'ORG-123',
        'phone': '+*********0',
        'email': '<EMAIL>',
        'address': 'Test Address',
        'zip_code': '12345',
        'city': 'Test City',
        'country': 'Test Country',
      };

      final company = Company.fromJson(json);

      expect(company.companyId, equals(1));
      expect(company.companyName, equals('Test Company'));
      expect(company.organizationNumber, equals('ORG-123'));
      expect(company.phone, equals('+*********0'));
      expect(company.email, equals('<EMAIL>'));
      expect(company.address, equals('Test Address'));
      expect(company.zipCode, equals('12345'));
      expect(company.city, equals('Test City'));
      expect(company.country, equals('Test Country'));
    });

    test('should handle null optional fields', () {
      final json = {
        'company_id': 1,
        'company_name': 'Test Company',
        'organization_number': 'ORG-123',
        'phone': '+*********0',
        'email': '<EMAIL>',
        'zip_code': '12345',
        'city': 'Test City',
        'country': 'Test Country',
        'address': null,
      };

      final company = Company.fromJson(json);

      expect(company.companyId, equals(1));
      expect(company.companyName, equals('Test Company'));
      expect(company.organizationNumber, equals('ORG-123'));
      expect(company.phone, equals('+*********0'));
      expect(company.email, equals('<EMAIL>'));
      expect(company.zipCode, equals('12345'));
      expect(company.city, equals('Test City'));
      expect(company.country, equals('Test Country'));
      expect(company.address, isNull);
    });

    test('should copy Company', () {
      const company = Company(
        companyId: 1,
        companyName: 'Test Company',
        organizationNumber: 'ORG-123',
        phone: '+*********0',
        email: '<EMAIL>',
        address: 'Test Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      final updatedCompany = company.copyWith(
        companyName: 'Updated Company',
        organizationNumber: 'ORG-456',
        phone: '+0987654321',
        email: '<EMAIL>',
        address: 'Updated Address',
        zipCode: '67890',
        city: 'Updated City',
        country: 'Updated Country',
      );

      expect(company.companyId, equals(1));
      expect(company.companyName, equals('Test Company'));
      expect(company.organizationNumber, equals('ORG-123'));
      expect(company.phone, equals('+*********0'));
      expect(company.email, equals('<EMAIL>'));
      expect(company.address, equals('Test Address'));
      expect(company.zipCode, equals('12345'));
      expect(company.city, equals('Test City'));
      expect(company.country, equals('Test Country'));

      expect(updatedCompany.companyId, equals(1));
      expect(updatedCompany.companyName, equals('Updated Company'));
      expect(updatedCompany.organizationNumber, equals('ORG-456'));
      expect(updatedCompany.phone, equals('+0987654321'));
      expect(updatedCompany.email, equals('<EMAIL>'));
      expect(updatedCompany.address, equals('Updated Address'));
      expect(updatedCompany.zipCode, equals('67890'));
      expect(updatedCompany.city, equals('Updated City'));
      expect(updatedCompany.country, equals('Updated Country'));
    });
  });

  group('VendorInvoiceLineItem Model Tests', () {
    test('should create VendorInvoiceLineItem from JSON', () {
      final json = {
        'line_item_id': 1,
        'invoice_id': 100,
        'account_number': '5000',
        'description': 'Test Item',
        'amount': 100.0,
        'tax_amount': 25.0,
        'company_id': 1,
        'created_at': '2024-03-26T10:00:00Z',
        'updated_at': '2024-03-26T10:00:00Z',
      };

      final lineItem = VendorInvoiceLineItem.fromJson(json);

      expect(lineItem.lineItemId, 1);
      expect(lineItem.invoiceId, 100);
      expect(lineItem.accountNumber, '5000');
      expect(lineItem.description, 'Test Item');
      expect(lineItem.amount, 100.0);
      expect(lineItem.taxAmount, 25.0);
      expect(lineItem.companyId, 1);
      expect(lineItem.createdAt, DateTime.parse('2024-03-26T10:00:00Z'));
      expect(lineItem.updatedAt, DateTime.parse('2024-03-26T10:00:00Z'));
    });

    test('should convert VendorInvoiceLineItem to JSON', () {
      final lineItem = VendorInvoiceLineItem(
        lineItemId: 1,
        invoiceId: 100,
        accountNumber: '5000',
        description: 'Test Item',
        amount: 100.0,
        taxAmount: 25.0,
        companyId: 1,
        createdAt: DateTime.parse('2024-03-26T10:00:00Z'),
        updatedAt: DateTime.parse('2024-03-26T10:00:00Z'),
      );

      final json = lineItem.toJson();

      expect(json['line_item_id'], 1);
      expect(json['invoice_id'], 100);
      expect(json['account_number'], '5000');
      expect(json['description'], 'Test Item');
      expect(json['amount'], 100.0);
      expect(json['tax_amount'], 25.0);
      expect(json['company_id'], 1);
      expect(json['created_at'], '2024-03-26T10:00:00.000Z');
      expect(json['updated_at'], '2024-03-26T10:00:00.000Z');
    });
  });

  group('VendorInvoice Model Tests', () {
    test('should create VendorInvoice from JSON', () {
      final json = {
        'invoice_id': 1,
        'vendor_id': 'V001',
        'invoice_number': 'INV-001',
        'invoice_date': '2024-03-26',
        'due_date': '2024-04-26',
        'amount': 1000.0,
        'currency_code': 'USD',
        'expense_account_number': '5000',
        'tax_amount': 250.0,
        'project_id': 1,
        'staff_id': 1,
        'company_id': 1,
        'is_paid': false,
      };

      final invoice = VendorInvoice.fromJson(json);

      expect(invoice.invoiceId, 1);
      expect(invoice.vendorId, 'V001');
      expect(invoice.invoiceNumber, 'INV-001');
      expect(invoice.invoiceDate, DateTime.parse('2024-03-26T00:00:00.000'));
      expect(invoice.dueDate, DateTime.parse('2024-04-26T00:00:00.000'));
      expect(invoice.amount, 1000.0);
      expect(invoice.currencyCode, 'USD');
      expect(invoice.expenseAccountNumber, '5000');
      expect(invoice.taxAmount, 250.0);
      expect(invoice.projectId, 1);
      expect(invoice.staffId, 1);
      expect(invoice.companyId, 1);
      expect(invoice.isPaid, false);
    });

    test('should convert VendorInvoice to JSON', () {
      final invoice = VendorInvoice(
        invoiceId: 1,
        vendorId: 'V001',
        invoiceNumber: 'INV-001',
        invoiceDate: DateTime.parse('2024-03-26T00:00:00.000'),
        dueDate: DateTime.parse('2024-04-26T00:00:00.000'),
        amount: 1000.0,
        currencyCode: 'USD',
        expenseAccountNumber: '5000',
        taxAmount: 250.0,
        projectId: 1,
        staffId: 1,
        companyId: 1,
        isPaid: false,
      );

      final json = invoice.toJson();

      expect(json['invoice_id'], 1);
      expect(json['vendor_id'], 'V001');
      expect(json['invoice_number'], 'INV-001');
      expect(json['invoice_date'], '2024-03-26T00:00:00.000');
      expect(json['due_date'], '2024-04-26T00:00:00.000');
      expect(json['amount'], 1000.0);
      expect(json['currency_code'], 'USD');
      expect(json['expense_account_number'], '5000');
      expect(json['tax_amount'], 250.0);
      expect(json['project_id'], 1);
      expect(json['staff_id'], 1);
      expect(json['company_id'], 1);
      expect(json['is_paid'], false);
    });

    test('should handle null optional fields', () {
      final json = {
        'invoice_id': 1,
        'vendor_id': 'V001',
        'invoice_number': 'INV-001',
        'invoice_date': '2024-03-26',
        'due_date': '2024-04-26',
        'amount': 1000.0,
        'currency_code': 'USD',
        'expense_account_number': '5000',
      };

      final invoice = VendorInvoice.fromJson(json);

      expect(invoice.invoiceId, 1);
      expect(invoice.vendorId, 'V001');
      expect(invoice.invoiceNumber, 'INV-001');
      expect(invoice.invoiceDate, DateTime.parse('2024-03-26T00:00:00.000'));
      expect(invoice.dueDate, DateTime.parse('2024-04-26T00:00:00.000'));
      expect(invoice.amount, 1000.0);
      expect(invoice.currencyCode, 'USD');
      expect(invoice.expenseAccountNumber, '5000');
      expect(invoice.taxAmount, null);
      expect(invoice.projectId, null);
      expect(invoice.staffId, null);
      expect(invoice.companyId, null);
      expect(invoice.isPaid, null);
    });
  });

  group('Vendor Model Tests', () {
    test('should create Vendor from JSON', () {
      final json = {
        'vendor_id': 'V001',
        'vendor_name': 'Test Vendor',
        'vendor_number': 'VN001',
        'organization_number': 'ORG001',
        'is_active': true,
        'phone': '*********0',
        'email': '<EMAIL>',
        'address': '123 Vendor St',
        'zip_code': '12345',
        'city': 'Test City',
        'country': 'Test Country',
        'bank_account_type': 'checking',
        'bank_account_number': '*********',
        'contact_person': 'John Doe',
        'company_id': 1,
      };

      final vendor = Vendor.fromJson(json);

      expect(vendor.vendorId, 'V001');
      expect(vendor.vendorName, 'Test Vendor');
      expect(vendor.vendorNumber, 'VN001');
      expect(vendor.organizationNumber, 'ORG001');
      expect(vendor.isActive, true);
      expect(vendor.phone, '*********0');
      expect(vendor.email, '<EMAIL>');
      expect(vendor.address, '123 Vendor St');
      expect(vendor.zipCode, '12345');
      expect(vendor.city, 'Test City');
      expect(vendor.country, 'Test Country');
      expect(vendor.bankAccountType, 'checking');
      expect(vendor.bankAccountNumber, '*********');
      expect(vendor.contactPerson, 'John Doe');
      expect(vendor.companyId, 1);
    });

    test('should convert Vendor to JSON', () {
      const vendor = Vendor(
        vendorId: 'V001',
        vendorName: 'Test Vendor',
        vendorNumber: 'VN001',
        organizationNumber: 'ORG001',
        isActive: true,
        phone: '*********0',
        email: '<EMAIL>',
        address: '123 Vendor St',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
        bankAccountType: 'checking',
        bankAccountNumber: '*********',
        contactPerson: 'John Doe',
        companyId: 1,
      );

      final json = vendor.toJson();

      expect(json['vendor_id'], 'V001');
      expect(json['vendor_name'], 'Test Vendor');
      expect(json['vendor_number'], 'VN001');
      expect(json['organization_number'], 'ORG001');
      expect(json['is_active'], true);
      expect(json['phone'], '*********0');
      expect(json['email'], '<EMAIL>');
      expect(json['address'], '123 Vendor St');
      expect(json['zip_code'], '12345');
      expect(json['city'], 'Test City');
      expect(json['country'], 'Test Country');
      expect(json['bank_account_type'], 'checking');
      expect(json['bank_account_number'], '*********');
      expect(json['contact_person'], 'John Doe');
      expect(json['company_id'], 1);
    });

    test('should handle null optional fields and default isActive', () {
      final json = {'vendor_id': 'V001', 'vendor_name': 'Test Vendor'};

      final vendor = Vendor.fromJson(json);

      expect(vendor.vendorId, 'V001');
      expect(vendor.vendorName, 'Test Vendor');
      expect(vendor.vendorNumber, null);
      expect(vendor.organizationNumber, null);
      expect(vendor.isActive, true); // Default value
      expect(vendor.phone, null);
      expect(vendor.email, null);
      expect(vendor.address, null);
      expect(vendor.zipCode, null);
      expect(vendor.city, null);
      expect(vendor.country, null);
      expect(vendor.bankAccountType, null);
      expect(vendor.bankAccountNumber, null);
      expect(vendor.contactPerson, null);
      expect(vendor.companyId, null);
    });
  });
}
