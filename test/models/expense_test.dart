import 'package:flutter_test/flutter_test.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/payment_method.dart';

void main() {
  group('Expense', () {
    final transactionId = const Uuid().v4();
    final testExpenseDate = DateTime(2023, 3, 15);
    final testExpense = Expense(
      expenseId: 1001,
      transactionId: transactionId,
      vendorId: 'V001',
      expenseDate: testExpenseDate,
      amount: 1250.50,
      currencyCode: 'USD',
      projectId: 42,
      staffId: 123,
      taxAmount: 125.05,
      companyId: 1,
      paymentMethod: PaymentMethod.creditCard,
      creditCardNumber: 'XXXX-XXXX-XXXX-1234',
    );

    group('Constructor', () {
      test('should create expense with required fields only', () {
        final expense = Expense(
          expenseId: 1002,
          transactionId: const Uuid().v4(),
          vendorId: 'V002',
          expenseDate: DateTime(2023, 4, 20),
          amount: 500.00,
          currencyCode: 'EUR',
          companyId: 1,
          paymentMethod: PaymentMethod.cash,
        );

        expect(expense.expenseId, 1002);
        expect(expense.vendorId, 'V002');
        expect(expense.expenseDate, DateTime(2023, 4, 20));
        expect(expense.amount, 500.00);
        expect(expense.currencyCode, 'EUR');
        expect(expense.projectId, null);
        expect(expense.staffId, null);
        expect(expense.taxAmount, null);
        expect(expense.paymentMethod, PaymentMethod.cash);
        expect(expense.creditCardNumber, null);
        expect(expense.companyId, 1);
      });

      test('should create expense with all fields', () {
        expect(testExpense.expenseId, 1001);
        expect(testExpense.transactionId, transactionId);
        expect(testExpense.vendorId, 'V001');
        expect(testExpense.expenseDate, testExpenseDate);
        expect(testExpense.amount, 1250.50);
        expect(testExpense.currencyCode, 'USD');
        expect(testExpense.projectId, 42);
        expect(testExpense.staffId, 123);
        expect(testExpense.taxAmount, 125.05);
        expect(testExpense.paymentMethod, PaymentMethod.creditCard);
        expect(testExpense.creditCardNumber, 'XXXX-XXXX-XXXX-1234');
        expect(testExpense.companyId, 1);
      });
    });

    group('JSON serialization', () {
      test('should convert expense to JSON with all fields', () {
        final json = testExpense.toJson();
        expect(json['expenseId'], 1001);
        expect(json['transactionId'], transactionId);
        expect(json['vendorId'], 'V001');
        expect(json['expenseDate'], '2023-03-15T00:00:00.000');
        expect(json['amount'], 1250.50);
        expect(json['currencyCode'], 'USD');
        expect(json['projectId'], 42);
        expect(json['staffId'], 123);
        expect(json['taxAmount'], 125.05);
        expect(json['paymentMethod'], 'credit_card');
        expect(json['creditCardNumber'], 'XXXX-XXXX-XXXX-1234');
        expect(json['companyId'], 1);
      });

      test('should create expense from JSON with all fields', () {
        final json = {
          'expenseId': 1001,
          'transactionId': transactionId,
          'vendorId': 'V001',
          'expenseDate': '2023-03-15',
          'amount': 1250.50,
          'currencyCode': 'USD',
          'projectId': 42,
          'staffId': 123,
          'taxAmount': 125.05,
          'companyId': 1,
          'paymentMethod': 'credit_card',
          'creditCardNumber': 'XXXX-XXXX-XXXX-1234',
        };

        final expense = Expense.fromJson(json);
        expect(expense, testExpense);
      });

      test('should handle JSON with minimal fields', () {
        final json = {
          'expenseId': 1002,
          'transactionId': const Uuid().v4(),
          'vendorId': 'V002',
          'expenseDate': '2023-04-20',
          'amount': 500.00,
          'currencyCode': 'EUR',
          'companyId': 1,
          'paymentMethod': 'cash',
        };

        final expense = Expense.fromJson(json);
        expect(expense.expenseId, 1002);
        expect(expense.vendorId, 'V002');
        expect(expense.expenseDate, DateTime(2023, 4, 20));
        expect(expense.amount, 500.00);
        expect(expense.currencyCode, 'EUR');
        expect(expense.projectId, null);
        expect(expense.staffId, null);
        expect(expense.taxAmount, null);
        expect(expense.paymentMethod, PaymentMethod.cash);
        expect(expense.creditCardNumber, null);
        expect(expense.companyId, 1);
      });
    });

    group('Payment method handling', () {
      test('should handle different payment methods correctly', () {
        final cashExpense = Expense(
          expenseId: 1003,
          transactionId: const Uuid().v4(),
          vendorId: 'V003',
          expenseDate: DateTime(2023, 5, 10),
          amount: 75.50,
          currencyCode: 'USD',
          companyId: 1,
          paymentMethod: PaymentMethod.cash,
        );

        final bankTransferExpense = Expense(
          expenseId: 1004,
          transactionId: const Uuid().v4(),
          vendorId: 'V004',
          expenseDate: DateTime(2023, 6, 15),
          amount: 1000.00,
          currencyCode: 'USD',
          companyId: 1,
          paymentMethod: PaymentMethod.bankTransfer,
        );

        expect(cashExpense.paymentMethod, PaymentMethod.cash);
        expect(bankTransferExpense.paymentMethod, PaymentMethod.bankTransfer);

        // Credit card number should be null for non-credit card expenses
        expect(cashExpense.creditCardNumber, null);
        expect(bankTransferExpense.creditCardNumber, null);
      });

      test('should throw error for invalid payment method', () {
        final invalidJson = {
          'expenseId': 1005,
          'transactionId': const Uuid().v4(),
          'vendorId': 'V005',
          'expenseDate': '2023-07-01',
          'amount': 200.00,
          'currencyCode': 'USD',
          'companyId': 1,
          'paymentMethod': 'invalid_method',
        };

        expect(
          () => Expense.fromJson(invalidJson),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should allow credit card expense without credit card number', () {
        final creditCardExpense = Expense(
          expenseId: 1006,
          transactionId: const Uuid().v4(),
          vendorId: 'V006',
          expenseDate: DateTime(2023, 7, 2),
          amount: 300.00,
          currencyCode: 'USD',
          companyId: 1,
          paymentMethod: PaymentMethod.creditCard,
        );

        expect(creditCardExpense.paymentMethod, PaymentMethod.creditCard);
        expect(creditCardExpense.creditCardNumber, null);
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        final updatedExpense = testExpense.copyWith(
          amount: 1500.00,
          taxAmount: 150.00,
          creditCardNumber: 'XXXX-XXXX-XXXX-5678',
        );

        // Original should be unchanged
        expect(testExpense.amount, 1250.50);
        expect(testExpense.taxAmount, 125.05);
        expect(testExpense.creditCardNumber, 'XXXX-XXXX-XXXX-1234');

        // New instance should have updated values
        expect(updatedExpense.expenseId, 1001);
        expect(updatedExpense.transactionId, transactionId);
        expect(updatedExpense.vendorId, 'V001');
        expect(updatedExpense.expenseDate, testExpenseDate);
        expect(updatedExpense.amount, 1500.00);
        expect(updatedExpense.currencyCode, 'USD');
        expect(updatedExpense.projectId, 42);
        expect(updatedExpense.staffId, 123);
        expect(updatedExpense.taxAmount, 150.00);
        expect(updatedExpense.paymentMethod, PaymentMethod.creditCard);
        expect(updatedExpense.creditCardNumber, 'XXXX-XXXX-XXXX-5678');
        expect(updatedExpense.companyId, 1);
      });

      test('should handle setting nullable fields to null', () {
        final updatedExpense = testExpense.copyWith(
          projectId: null,
          staffId: null,
          taxAmount: null,
          creditCardNumber: null,
        );

        expect(updatedExpense.projectId, null);
        expect(updatedExpense.staffId, null);
        expect(updatedExpense.taxAmount, null);
        expect(updatedExpense.creditCardNumber, null);
      });
    });

    group('Equality and hashCode', () {
      test('should consider instances with same values equal', () {
        final expense1 = Expense(
          expenseId: 1001,
          transactionId: transactionId,
          vendorId: 'V001',
          expenseDate: testExpenseDate,
          amount: 1250.50,
          currencyCode: 'USD',
          projectId: 42,
          staffId: 123,
          taxAmount: 125.05,
          companyId: 1,
          paymentMethod: PaymentMethod.creditCard,
          creditCardNumber: 'XXXX-XXXX-XXXX-1234',
        );

        final expense2 = Expense(
          expenseId: 1001,
          transactionId: transactionId,
          vendorId: 'V001',
          expenseDate: testExpenseDate,
          amount: 1250.50,
          currencyCode: 'USD',
          projectId: 42,
          staffId: 123,
          taxAmount: 125.05,
          companyId: 1,
          paymentMethod: PaymentMethod.creditCard,
          creditCardNumber: 'XXXX-XXXX-XXXX-1234',
        );

        expect(expense1, equals(expense2));
        expect(expense1.hashCode, equals(expense2.hashCode));
      });

      test('should consider instances with different values unequal', () {
        final expense1 = testExpense;
        final expense2 = Expense(
          expenseId: 1002,
          transactionId: const Uuid().v4(),
          vendorId: 'V002',
          expenseDate: DateTime(2023, 4, 20),
          amount: 500.00,
          currencyCode: 'EUR',
          projectId: 43,
          staffId: 124,
          taxAmount: 50.00,
          companyId: 2,
          paymentMethod: PaymentMethod.cash,
          creditCardNumber: null,
        );

        expect(expense1, isNot(expense2));
        expect(expense1.hashCode, isNot(expense2.hashCode));
      });
    });
  });
}
