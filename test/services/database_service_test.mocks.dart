// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in we_like_money/test/services/database_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i19;

import 'package:flutter/src/foundation/diagnostics.dart' as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/models/account.dart' as _i7;
import 'package:we_like_money/models/company.dart' as _i2;
import 'package:we_like_money/models/currency.dart' as _i5;
import 'package:we_like_money/models/customer.dart' as _i9;
import 'package:we_like_money/models/exchange_rate.dart' as _i6;
import 'package:we_like_money/models/expense.dart' as _i14;
import 'package:we_like_money/models/general_ledger.dart' as _i8;
import 'package:we_like_money/models/invoice.dart' as _i11;
import 'package:we_like_money/models/payment_in.dart' as _i15;
import 'package:we_like_money/models/payment_out.dart' as _i16;
import 'package:we_like_money/models/project.dart' as _i3;
import 'package:we_like_money/models/staff_member.dart' as _i4;
import 'package:we_like_money/models/vendor.dart' as _i10;
import 'package:we_like_money/models/vendor_invoice.dart' as _i13;
import 'package:we_like_money/models/vendor_invoice_line_item.dart' as _i17;
import 'package:we_like_money/services/database_service.dart' as _i18;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCompany_0 extends _i1.SmartFake implements _i2.Company {
  _FakeCompany_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeProject_1 extends _i1.SmartFake implements _i3.Project {
  _FakeProject_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeStaffMember_2 extends _i1.SmartFake implements _i4.StaffMember {
  _FakeStaffMember_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCurrency_3 extends _i1.SmartFake implements _i5.Currency {
  _FakeCurrency_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeExchangeRate_4 extends _i1.SmartFake implements _i6.ExchangeRate {
  _FakeExchangeRate_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAccount_5 extends _i1.SmartFake implements _i7.Account {
  _FakeAccount_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGeneralLedger_6 extends _i1.SmartFake implements _i8.GeneralLedger {
  _FakeGeneralLedger_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCustomer_7 extends _i1.SmartFake implements _i9.Customer {
  _FakeCustomer_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVendor_8 extends _i1.SmartFake implements _i10.Vendor {
  _FakeVendor_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInvoice_9 extends _i1.SmartFake implements _i11.Invoice {
  _FakeInvoice_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({
    _i12.DiagnosticLevel? minLevel = _i12.DiagnosticLevel.info,
  }) => super.toString();
}

class _FakeVendorInvoice_10 extends _i1.SmartFake
    implements _i13.VendorInvoice {
  _FakeVendorInvoice_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({
    _i12.DiagnosticLevel? minLevel = _i12.DiagnosticLevel.info,
  }) => super.toString();
}

class _FakeExpense_11 extends _i1.SmartFake implements _i14.Expense {
  _FakeExpense_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentIn_12 extends _i1.SmartFake implements _i15.PaymentIn {
  _FakePaymentIn_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentOut_13 extends _i1.SmartFake implements _i16.PaymentOut {
  _FakePaymentOut_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVendorInvoiceLineItem_14 extends _i1.SmartFake
    implements _i17.VendorInvoiceLineItem {
  _FakeVendorInvoiceLineItem_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDatabaseService extends _i1.Mock implements _i18.DatabaseService {
  MockDatabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setMockMode(bool? useMockData) => super.noSuchMethod(
    Invocation.method(#setMockMode, [useMockData]),
    returnValueForMissingStub: null,
  );

  @override
  _i19.Future<List<_i2.Company>> getCompanies() =>
      (super.noSuchMethod(
            Invocation.method(#getCompanies, []),
            returnValue: _i19.Future<List<_i2.Company>>.value(<_i2.Company>[]),
          )
          as _i19.Future<List<_i2.Company>>);

  @override
  _i19.Future<_i2.Company?> getCompanyById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getCompanyById, [id]),
            returnValue: _i19.Future<_i2.Company?>.value(),
          )
          as _i19.Future<_i2.Company?>);

  @override
  _i19.Future<_i2.Company> createCompany(_i2.Company? company) =>
      (super.noSuchMethod(
            Invocation.method(#createCompany, [company]),
            returnValue: _i19.Future<_i2.Company>.value(
              _FakeCompany_0(
                this,
                Invocation.method(#createCompany, [company]),
              ),
            ),
          )
          as _i19.Future<_i2.Company>);

  @override
  _i19.Future<_i2.Company> updateCompany(_i2.Company? company) =>
      (super.noSuchMethod(
            Invocation.method(#updateCompany, [company]),
            returnValue: _i19.Future<_i2.Company>.value(
              _FakeCompany_0(
                this,
                Invocation.method(#updateCompany, [company]),
              ),
            ),
          )
          as _i19.Future<_i2.Company>);

  @override
  _i19.Future<void> deleteCompany(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCompany, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i3.Project>> getProjects([int? companyId]) =>
      (super.noSuchMethod(
            Invocation.method(#getProjects, [companyId]),
            returnValue: _i19.Future<List<_i3.Project>>.value(<_i3.Project>[]),
          )
          as _i19.Future<List<_i3.Project>>);

  @override
  _i19.Future<_i3.Project?> getProjectById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectById, [id]),
            returnValue: _i19.Future<_i3.Project?>.value(),
          )
          as _i19.Future<_i3.Project?>);

  @override
  _i19.Future<_i3.Project> createProject(_i3.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#createProject, [project]),
            returnValue: _i19.Future<_i3.Project>.value(
              _FakeProject_1(
                this,
                Invocation.method(#createProject, [project]),
              ),
            ),
          )
          as _i19.Future<_i3.Project>);

  @override
  _i19.Future<_i3.Project> updateProject(_i3.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#updateProject, [project]),
            returnValue: _i19.Future<_i3.Project>.value(
              _FakeProject_1(
                this,
                Invocation.method(#updateProject, [project]),
              ),
            ),
          )
          as _i19.Future<_i3.Project>);

  @override
  _i19.Future<void> deleteProject(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProject, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i4.StaffMember>> getStaffMembers() =>
      (super.noSuchMethod(
            Invocation.method(#getStaffMembers, []),
            returnValue: _i19.Future<List<_i4.StaffMember>>.value(
              <_i4.StaffMember>[],
            ),
          )
          as _i19.Future<List<_i4.StaffMember>>);

  @override
  _i19.Future<_i4.StaffMember?> getStaffMemberById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getStaffMemberById, [id]),
            returnValue: _i19.Future<_i4.StaffMember?>.value(),
          )
          as _i19.Future<_i4.StaffMember?>);

  @override
  _i19.Future<_i4.StaffMember> createStaffMember(
    _i4.StaffMember? staffMember,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createStaffMember, [staffMember]),
            returnValue: _i19.Future<_i4.StaffMember>.value(
              _FakeStaffMember_2(
                this,
                Invocation.method(#createStaffMember, [staffMember]),
              ),
            ),
          )
          as _i19.Future<_i4.StaffMember>);

  @override
  _i19.Future<_i4.StaffMember> updateStaffMember(
    _i4.StaffMember? staffMember,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateStaffMember, [staffMember]),
            returnValue: _i19.Future<_i4.StaffMember>.value(
              _FakeStaffMember_2(
                this,
                Invocation.method(#updateStaffMember, [staffMember]),
              ),
            ),
          )
          as _i19.Future<_i4.StaffMember>);

  @override
  _i19.Future<void> deleteStaffMember(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteStaffMember, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i5.Currency>> getCurrencies() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrencies, []),
            returnValue: _i19.Future<List<_i5.Currency>>.value(
              <_i5.Currency>[],
            ),
          )
          as _i19.Future<List<_i5.Currency>>);

  @override
  _i19.Future<_i5.Currency?> getCurrencyByCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#getCurrencyByCode, [code]),
            returnValue: _i19.Future<_i5.Currency?>.value(),
          )
          as _i19.Future<_i5.Currency?>);

  @override
  _i19.Future<_i5.Currency> createCurrency(_i5.Currency? currency) =>
      (super.noSuchMethod(
            Invocation.method(#createCurrency, [currency]),
            returnValue: _i19.Future<_i5.Currency>.value(
              _FakeCurrency_3(
                this,
                Invocation.method(#createCurrency, [currency]),
              ),
            ),
          )
          as _i19.Future<_i5.Currency>);

  @override
  _i19.Future<_i5.Currency> updateCurrency(_i5.Currency? currency) =>
      (super.noSuchMethod(
            Invocation.method(#updateCurrency, [currency]),
            returnValue: _i19.Future<_i5.Currency>.value(
              _FakeCurrency_3(
                this,
                Invocation.method(#updateCurrency, [currency]),
              ),
            ),
          )
          as _i19.Future<_i5.Currency>);

  @override
  _i19.Future<void> deleteCurrency(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCurrency, [code]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i6.ExchangeRate>> getExchangeRates() =>
      (super.noSuchMethod(
            Invocation.method(#getExchangeRates, []),
            returnValue: _i19.Future<List<_i6.ExchangeRate>>.value(
              <_i6.ExchangeRate>[],
            ),
          )
          as _i19.Future<List<_i6.ExchangeRate>>);

  @override
  _i19.Future<_i6.ExchangeRate?> getExchangeRateById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getExchangeRateById, [id]),
            returnValue: _i19.Future<_i6.ExchangeRate?>.value(),
          )
          as _i19.Future<_i6.ExchangeRate?>);

  @override
  _i19.Future<_i6.ExchangeRate> createExchangeRate(
    _i6.ExchangeRate? exchangeRate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createExchangeRate, [exchangeRate]),
            returnValue: _i19.Future<_i6.ExchangeRate>.value(
              _FakeExchangeRate_4(
                this,
                Invocation.method(#createExchangeRate, [exchangeRate]),
              ),
            ),
          )
          as _i19.Future<_i6.ExchangeRate>);

  @override
  _i19.Future<_i6.ExchangeRate> updateExchangeRate(
    _i6.ExchangeRate? exchangeRate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateExchangeRate, [exchangeRate]),
            returnValue: _i19.Future<_i6.ExchangeRate>.value(
              _FakeExchangeRate_4(
                this,
                Invocation.method(#updateExchangeRate, [exchangeRate]),
              ),
            ),
          )
          as _i19.Future<_i6.ExchangeRate>);

  @override
  _i19.Future<void> deleteExchangeRate(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteExchangeRate, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i7.Account>> getAccounts([int? companyId]) =>
      (super.noSuchMethod(
            Invocation.method(#getAccounts, [companyId]),
            returnValue: _i19.Future<List<_i7.Account>>.value(<_i7.Account>[]),
          )
          as _i19.Future<List<_i7.Account>>);

  @override
  _i19.Future<_i7.Account?> getAccountByNumber(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#getAccountByNumber, [accountNumber]),
            returnValue: _i19.Future<_i7.Account?>.value(),
          )
          as _i19.Future<_i7.Account?>);

  @override
  _i19.Future<_i7.Account> createAccount(_i7.Account? account) =>
      (super.noSuchMethod(
            Invocation.method(#createAccount, [account]),
            returnValue: _i19.Future<_i7.Account>.value(
              _FakeAccount_5(
                this,
                Invocation.method(#createAccount, [account]),
              ),
            ),
          )
          as _i19.Future<_i7.Account>);

  @override
  _i19.Future<_i7.Account> updateAccount(_i7.Account? account) =>
      (super.noSuchMethod(
            Invocation.method(#updateAccount, [account]),
            returnValue: _i19.Future<_i7.Account>.value(
              _FakeAccount_5(
                this,
                Invocation.method(#updateAccount, [account]),
              ),
            ),
          )
          as _i19.Future<_i7.Account>);

  @override
  _i19.Future<void> deleteAccount(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, [accountNumber]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i8.GeneralLedger>> getGeneralLedgerEntries() =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntries, []),
            returnValue: _i19.Future<List<_i8.GeneralLedger>>.value(
              <_i8.GeneralLedger>[],
            ),
          )
          as _i19.Future<List<_i8.GeneralLedger>>);

  @override
  _i19.Future<List<_i8.GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String? accountNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntriesByAccount, [
              accountNumber,
            ]),
            returnValue: _i19.Future<List<_i8.GeneralLedger>>.value(
              <_i8.GeneralLedger>[],
            ),
          )
          as _i19.Future<List<_i8.GeneralLedger>>);

  @override
  _i19.Future<List<_i8.GeneralLedger>> getLedgerEntriesByTransactionId(
    String? transactionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getLedgerEntriesByTransactionId, [
              transactionId,
            ]),
            returnValue: _i19.Future<List<_i8.GeneralLedger>>.value(
              <_i8.GeneralLedger>[],
            ),
          )
          as _i19.Future<List<_i8.GeneralLedger>>);

  @override
  _i19.Future<_i8.GeneralLedger?> getGeneralLedgerEntryById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneralLedgerEntryById, [id]),
            returnValue: _i19.Future<_i8.GeneralLedger?>.value(),
          )
          as _i19.Future<_i8.GeneralLedger?>);

  @override
  _i19.Future<_i8.GeneralLedger> createGeneralLedgerEntry(
    _i8.GeneralLedger? entry,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createGeneralLedgerEntry, [entry]),
            returnValue: _i19.Future<_i8.GeneralLedger>.value(
              _FakeGeneralLedger_6(
                this,
                Invocation.method(#createGeneralLedgerEntry, [entry]),
              ),
            ),
          )
          as _i19.Future<_i8.GeneralLedger>);

  @override
  _i19.Future<_i8.GeneralLedger> updateGeneralLedgerEntry(
    _i8.GeneralLedger? entry,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateGeneralLedgerEntry, [entry]),
            returnValue: _i19.Future<_i8.GeneralLedger>.value(
              _FakeGeneralLedger_6(
                this,
                Invocation.method(#updateGeneralLedgerEntry, [entry]),
              ),
            ),
          )
          as _i19.Future<_i8.GeneralLedger>);

  @override
  _i19.Future<void> deleteGeneralLedgerEntry(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteGeneralLedgerEntry, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i9.Customer>> getCustomers() =>
      (super.noSuchMethod(
            Invocation.method(#getCustomers, []),
            returnValue: _i19.Future<List<_i9.Customer>>.value(
              <_i9.Customer>[],
            ),
          )
          as _i19.Future<List<_i9.Customer>>);

  @override
  _i19.Future<_i9.Customer?> getCustomerById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomerById, [id]),
            returnValue: _i19.Future<_i9.Customer?>.value(),
          )
          as _i19.Future<_i9.Customer?>);

  @override
  _i19.Future<_i9.Customer> createCustomer(_i9.Customer? customer) =>
      (super.noSuchMethod(
            Invocation.method(#createCustomer, [customer]),
            returnValue: _i19.Future<_i9.Customer>.value(
              _FakeCustomer_7(
                this,
                Invocation.method(#createCustomer, [customer]),
              ),
            ),
          )
          as _i19.Future<_i9.Customer>);

  @override
  _i19.Future<_i9.Customer> updateCustomer(_i9.Customer? customer) =>
      (super.noSuchMethod(
            Invocation.method(#updateCustomer, [customer]),
            returnValue: _i19.Future<_i9.Customer>.value(
              _FakeCustomer_7(
                this,
                Invocation.method(#updateCustomer, [customer]),
              ),
            ),
          )
          as _i19.Future<_i9.Customer>);

  @override
  _i19.Future<void> deleteCustomer(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCustomer, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i10.Vendor>> getVendors() =>
      (super.noSuchMethod(
            Invocation.method(#getVendors, []),
            returnValue: _i19.Future<List<_i10.Vendor>>.value(<_i10.Vendor>[]),
          )
          as _i19.Future<List<_i10.Vendor>>);

  @override
  _i19.Future<_i10.Vendor?> getVendorById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorById, [id]),
            returnValue: _i19.Future<_i10.Vendor?>.value(),
          )
          as _i19.Future<_i10.Vendor?>);

  @override
  _i19.Future<_i10.Vendor> createVendor(_i10.Vendor? vendor) =>
      (super.noSuchMethod(
            Invocation.method(#createVendor, [vendor]),
            returnValue: _i19.Future<_i10.Vendor>.value(
              _FakeVendor_8(this, Invocation.method(#createVendor, [vendor])),
            ),
          )
          as _i19.Future<_i10.Vendor>);

  @override
  _i19.Future<_i10.Vendor> updateVendor(_i10.Vendor? vendor) =>
      (super.noSuchMethod(
            Invocation.method(#updateVendor, [vendor]),
            returnValue: _i19.Future<_i10.Vendor>.value(
              _FakeVendor_8(this, Invocation.method(#updateVendor, [vendor])),
            ),
          )
          as _i19.Future<_i10.Vendor>);

  @override
  _i19.Future<void> deleteVendor(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteVendor, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i10.Vendor>> searchVendors(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchVendors, [query]),
            returnValue: _i19.Future<List<_i10.Vendor>>.value(<_i10.Vendor>[]),
          )
          as _i19.Future<List<_i10.Vendor>>);

  @override
  _i19.Future<List<_i11.Invoice>> getInvoices() =>
      (super.noSuchMethod(
            Invocation.method(#getInvoices, []),
            returnValue: _i19.Future<List<_i11.Invoice>>.value(
              <_i11.Invoice>[],
            ),
          )
          as _i19.Future<List<_i11.Invoice>>);

  @override
  _i19.Future<_i11.Invoice?> getInvoiceById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoiceById, [id]),
            returnValue: _i19.Future<_i11.Invoice?>.value(),
          )
          as _i19.Future<_i11.Invoice?>);

  @override
  _i19.Future<_i11.Invoice> createInvoice(_i11.Invoice? invoice) =>
      (super.noSuchMethod(
            Invocation.method(#createInvoice, [invoice]),
            returnValue: _i19.Future<_i11.Invoice>.value(
              _FakeInvoice_9(
                this,
                Invocation.method(#createInvoice, [invoice]),
              ),
            ),
          )
          as _i19.Future<_i11.Invoice>);

  @override
  _i19.Future<_i11.Invoice> updateInvoice(_i11.Invoice? invoice) =>
      (super.noSuchMethod(
            Invocation.method(#updateInvoice, [invoice]),
            returnValue: _i19.Future<_i11.Invoice>.value(
              _FakeInvoice_9(
                this,
                Invocation.method(#updateInvoice, [invoice]),
              ),
            ),
          )
          as _i19.Future<_i11.Invoice>);

  @override
  _i19.Future<void> deleteInvoice(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteInvoice, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i13.VendorInvoice>> getVendorInvoices() =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoices, []),
            returnValue: _i19.Future<List<_i13.VendorInvoice>>.value(
              <_i13.VendorInvoice>[],
            ),
          )
          as _i19.Future<List<_i13.VendorInvoice>>);

  @override
  _i19.Future<_i13.VendorInvoice?> getVendorInvoiceById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoiceById, [id]),
            returnValue: _i19.Future<_i13.VendorInvoice?>.value(),
          )
          as _i19.Future<_i13.VendorInvoice?>);

  @override
  _i19.Future<List<_i13.VendorInvoice>> getVendorInvoicesByVendorId(
    String? vendorId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoicesByVendorId, [vendorId]),
            returnValue: _i19.Future<List<_i13.VendorInvoice>>.value(
              <_i13.VendorInvoice>[],
            ),
          )
          as _i19.Future<List<_i13.VendorInvoice>>);

  @override
  _i19.Future<_i13.VendorInvoice> createVendorInvoice(
    _i13.VendorInvoice? vendorInvoice,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createVendorInvoice, [vendorInvoice]),
            returnValue: _i19.Future<_i13.VendorInvoice>.value(
              _FakeVendorInvoice_10(
                this,
                Invocation.method(#createVendorInvoice, [vendorInvoice]),
              ),
            ),
          )
          as _i19.Future<_i13.VendorInvoice>);

  @override
  _i19.Future<_i13.VendorInvoice> updateVendorInvoice(
    _i13.VendorInvoice? vendorInvoice,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateVendorInvoice, [vendorInvoice]),
            returnValue: _i19.Future<_i13.VendorInvoice>.value(
              _FakeVendorInvoice_10(
                this,
                Invocation.method(#updateVendorInvoice, [vendorInvoice]),
              ),
            ),
          )
          as _i19.Future<_i13.VendorInvoice>);

  @override
  _i19.Future<void> deleteVendorInvoice(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteVendorInvoice, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i14.Expense>> getExpenses() =>
      (super.noSuchMethod(
            Invocation.method(#getExpenses, []),
            returnValue: _i19.Future<List<_i14.Expense>>.value(
              <_i14.Expense>[],
            ),
          )
          as _i19.Future<List<_i14.Expense>>);

  @override
  _i19.Future<_i14.Expense?> getExpenseById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getExpenseById, [id]),
            returnValue: _i19.Future<_i14.Expense?>.value(),
          )
          as _i19.Future<_i14.Expense?>);

  @override
  _i19.Future<_i14.Expense> createExpense(_i14.Expense? expense) =>
      (super.noSuchMethod(
            Invocation.method(#createExpense, [expense]),
            returnValue: _i19.Future<_i14.Expense>.value(
              _FakeExpense_11(
                this,
                Invocation.method(#createExpense, [expense]),
              ),
            ),
          )
          as _i19.Future<_i14.Expense>);

  @override
  _i19.Future<_i14.Expense> updateExpense(_i14.Expense? expense) =>
      (super.noSuchMethod(
            Invocation.method(#updateExpense, [expense]),
            returnValue: _i19.Future<_i14.Expense>.value(
              _FakeExpense_11(
                this,
                Invocation.method(#updateExpense, [expense]),
              ),
            ),
          )
          as _i19.Future<_i14.Expense>);

  @override
  _i19.Future<void> deleteExpense(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteExpense, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i15.PaymentIn>> getPaymentsIn() =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentsIn, []),
            returnValue: _i19.Future<List<_i15.PaymentIn>>.value(
              <_i15.PaymentIn>[],
            ),
          )
          as _i19.Future<List<_i15.PaymentIn>>);

  @override
  _i19.Future<_i15.PaymentIn?> getPaymentInById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentInById, [id]),
            returnValue: _i19.Future<_i15.PaymentIn?>.value(),
          )
          as _i19.Future<_i15.PaymentIn?>);

  @override
  _i19.Future<_i15.PaymentIn> createPaymentIn(_i15.PaymentIn? paymentIn) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentIn, [paymentIn]),
            returnValue: _i19.Future<_i15.PaymentIn>.value(
              _FakePaymentIn_12(
                this,
                Invocation.method(#createPaymentIn, [paymentIn]),
              ),
            ),
          )
          as _i19.Future<_i15.PaymentIn>);

  @override
  _i19.Future<_i15.PaymentIn> updatePaymentIn(_i15.PaymentIn? paymentIn) =>
      (super.noSuchMethod(
            Invocation.method(#updatePaymentIn, [paymentIn]),
            returnValue: _i19.Future<_i15.PaymentIn>.value(
              _FakePaymentIn_12(
                this,
                Invocation.method(#updatePaymentIn, [paymentIn]),
              ),
            ),
          )
          as _i19.Future<_i15.PaymentIn>);

  @override
  _i19.Future<void> deletePaymentIn(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deletePaymentIn, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i16.PaymentOut>> getPaymentsOut() =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentsOut, []),
            returnValue: _i19.Future<List<_i16.PaymentOut>>.value(
              <_i16.PaymentOut>[],
            ),
          )
          as _i19.Future<List<_i16.PaymentOut>>);

  @override
  _i19.Future<_i16.PaymentOut?> getPaymentOutById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentOutById, [id]),
            returnValue: _i19.Future<_i16.PaymentOut?>.value(),
          )
          as _i19.Future<_i16.PaymentOut?>);

  @override
  _i19.Future<_i16.PaymentOut> createPaymentOut(_i16.PaymentOut? paymentOut) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentOut, [paymentOut]),
            returnValue: _i19.Future<_i16.PaymentOut>.value(
              _FakePaymentOut_13(
                this,
                Invocation.method(#createPaymentOut, [paymentOut]),
              ),
            ),
          )
          as _i19.Future<_i16.PaymentOut>);

  @override
  _i19.Future<_i16.PaymentOut> updatePaymentOut(_i16.PaymentOut? paymentOut) =>
      (super.noSuchMethod(
            Invocation.method(#updatePaymentOut, [paymentOut]),
            returnValue: _i19.Future<_i16.PaymentOut>.value(
              _FakePaymentOut_13(
                this,
                Invocation.method(#updatePaymentOut, [paymentOut]),
              ),
            ),
          )
          as _i19.Future<_i16.PaymentOut>);

  @override
  _i19.Future<void> deletePaymentOut(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deletePaymentOut, [id]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i17.VendorInvoiceLineItem>> getVendorInvoiceLineItems(
    int? invoiceId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoiceLineItems, [invoiceId]),
            returnValue: _i19.Future<List<_i17.VendorInvoiceLineItem>>.value(
              <_i17.VendorInvoiceLineItem>[],
            ),
          )
          as _i19.Future<List<_i17.VendorInvoiceLineItem>>);

  @override
  _i19.Future<_i17.VendorInvoiceLineItem?> getVendorInvoiceLineItemById(
    int? lineItemId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoiceLineItemById, [lineItemId]),
            returnValue: _i19.Future<_i17.VendorInvoiceLineItem?>.value(),
          )
          as _i19.Future<_i17.VendorInvoiceLineItem?>);

  @override
  _i19.Future<_i17.VendorInvoiceLineItem> createVendorInvoiceLineItem(
    _i17.VendorInvoiceLineItem? lineItem,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createVendorInvoiceLineItem, [lineItem]),
            returnValue: _i19.Future<_i17.VendorInvoiceLineItem>.value(
              _FakeVendorInvoiceLineItem_14(
                this,
                Invocation.method(#createVendorInvoiceLineItem, [lineItem]),
              ),
            ),
          )
          as _i19.Future<_i17.VendorInvoiceLineItem>);

  @override
  _i19.Future<_i17.VendorInvoiceLineItem> updateVendorInvoiceLineItem(
    _i17.VendorInvoiceLineItem? lineItem,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateVendorInvoiceLineItem, [lineItem]),
            returnValue: _i19.Future<_i17.VendorInvoiceLineItem>.value(
              _FakeVendorInvoiceLineItem_14(
                this,
                Invocation.method(#updateVendorInvoiceLineItem, [lineItem]),
              ),
            ),
          )
          as _i19.Future<_i17.VendorInvoiceLineItem>);

  @override
  _i19.Future<void> deleteVendorInvoiceLineItem(int? lineItemId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteVendorInvoiceLineItem, [lineItemId]),
            returnValue: _i19.Future<void>.value(),
            returnValueForMissingStub: _i19.Future<void>.value(),
          )
          as _i19.Future<void>);

  @override
  _i19.Future<List<_i17.VendorInvoiceLineItem>>
  getVendorInvoiceLineItemsByAccount(String? accountNumber) =>
      (super.noSuchMethod(
            Invocation.method(#getVendorInvoiceLineItemsByAccount, [
              accountNumber,
            ]),
            returnValue: _i19.Future<List<_i17.VendorInvoiceLineItem>>.value(
              <_i17.VendorInvoiceLineItem>[],
            ),
          )
          as _i19.Future<List<_i17.VendorInvoiceLineItem>>);
}
