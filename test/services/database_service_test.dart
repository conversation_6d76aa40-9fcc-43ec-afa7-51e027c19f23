import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/test_data.dart';
import 'database_service_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  late MockDatabaseService mockDatabaseService;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
  });

  group('Project Operations', () {
    const testProject = Project(
      projectId: 1,
      projectCode: 'TEST001',
      projectName: 'Test Project',
      description: 'Test Description',
    );

    test('getProjects returns list of projects', () async {
      when(
        mockDatabaseService.getProjects(),
      ).thenAnswer((_) async => [testProject]);

      final projects = await mockDatabaseService.getProjects();
      expect(projects, [testProject]);
      verify(mockDatabaseService.getProjects()).called(1);
    });

    test('getProjectById returns project when found', () async {
      when(
        mockDatabaseService.getProjectById(1),
      ).thenAnswer((_) async => testProject);

      final project = await mockDatabaseService.getProjectById(1);
      expect(project, testProject);
      verify(mockDatabaseService.getProjectById(1)).called(1);
    });

    test('getProjectById returns null when not found', () async {
      when(
        mockDatabaseService.getProjectById(999),
      ).thenAnswer((_) async => null);

      final project = await mockDatabaseService.getProjectById(999);
      expect(project, null);
      verify(mockDatabaseService.getProjectById(999)).called(1);
    });
  });

  group('Staff Member Operations', () {
    const testStaffMember = StaffMember(
      staffId: 1,
      staffName: 'Test Staff',
      email: '<EMAIL>',
    );

    test('getStaffMembers returns list of staff members', () async {
      when(
        mockDatabaseService.getStaffMembers(),
      ).thenAnswer((_) async => [testStaffMember]);

      final staffMembers = await mockDatabaseService.getStaffMembers();
      expect(staffMembers, [testStaffMember]);
      verify(mockDatabaseService.getStaffMembers()).called(1);
    });

    test('getStaffMemberById returns staff member when found', () async {
      when(
        mockDatabaseService.getStaffMemberById(1),
      ).thenAnswer((_) async => testStaffMember);

      final staffMember = await mockDatabaseService.getStaffMemberById(1);
      expect(staffMember, testStaffMember);
      verify(mockDatabaseService.getStaffMemberById(1)).called(1);
    });

    test('getStaffMemberById returns null when not found', () async {
      when(
        mockDatabaseService.getStaffMemberById(999),
      ).thenAnswer((_) async => null);

      final staffMember = await mockDatabaseService.getStaffMemberById(999);
      expect(staffMember, null);
      verify(mockDatabaseService.getStaffMemberById(999)).called(1);
    });
  });

  group('Company Operations', () {
    test('getCompanies returns list of companies', () async {
      when(
        mockDatabaseService.getCompanies(),
      ).thenAnswer((_) async => [TestData.testCompany]);

      final companies = await mockDatabaseService.getCompanies();
      expect(companies, [TestData.testCompany]);
      verify(mockDatabaseService.getCompanies()).called(1);
    });

    test('getCompanyById returns company when found', () async {
      when(
        mockDatabaseService.getCompanyById(1),
      ).thenAnswer((_) async => TestData.testCompany);

      final company = await mockDatabaseService.getCompanyById(1);
      expect(company, TestData.testCompany);
      verify(mockDatabaseService.getCompanyById(1)).called(1);
    });

    test('getCompanyById returns null when not found', () async {
      when(
        mockDatabaseService.getCompanyById(999),
      ).thenAnswer((_) async => null);

      final company = await mockDatabaseService.getCompanyById(999);
      expect(company, null);
      verify(mockDatabaseService.getCompanyById(999)).called(1);
    });

    test('createCompany returns created company', () async {
      when(
        mockDatabaseService.createCompany(TestData.testCompany),
      ).thenAnswer((_) async => TestData.testCompany);

      final company = await mockDatabaseService.createCompany(
        TestData.testCompany,
      );
      expect(company, TestData.testCompany);
      verify(mockDatabaseService.createCompany(TestData.testCompany)).called(1);
    });

    test('updateCompany returns updated company', () async {
      when(
        mockDatabaseService.updateCompany(TestData.testCompany),
      ).thenAnswer((_) async => TestData.testCompany);

      final company = await mockDatabaseService.updateCompany(
        TestData.testCompany,
      );
      expect(company, TestData.testCompany);
      verify(mockDatabaseService.updateCompany(TestData.testCompany)).called(1);
    });

    test('deleteCompany completes successfully', () async {
      when(
        mockDatabaseService.deleteCompany(1),
      ).thenAnswer((_) async => Future<void>.value());

      await mockDatabaseService.deleteCompany(1);
      verify(mockDatabaseService.deleteCompany(1)).called(1);
    });
  });
}
