import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/customer.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/services/logger.dart';
import '../mocks/mock_supabase.dart';

class MockSupabaseDatabaseService extends SupabaseDatabaseService {
  final Map<String, Customer> _mockCustomers = {};
  bool mockMode = true;

  MockSupabaseDatabaseService(super.clientProvider, super.errorHandler);

  void resetMockData() {
    _mockCustomers.clear();
    _mockCustomers.addAll({
      'C001': const Customer(
        customerId: 'C001',
        customerName: 'Test Customer 1',
        address: '123 Test St, Test City, USA',
        contactPerson: '<PERSON>',
        companyId: 1,
      ),
      'C002': const Customer(
        customerId: 'C002',
        customerName: 'Test Customer 2',
        address: '456 Test Ave, Test Town, USA',
        contactPerson: '<PERSON>',
        companyId: 1,
      ),
    });
  }

  @override
  Future<List<Customer>> getCustomers() async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    return _mockCustomers.values.toList();
  }

  @override
  Future<Customer?> getCustomerById(String id) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    return _mockCustomers[id];
  }

  @override
  Future<Customer> createCustomer(Customer customer) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    final newCustomer = customer.copyWith(
      customerId: 'C${DateTime.now().millisecondsSinceEpoch}',
    );
    _mockCustomers[newCustomer.customerId] = newCustomer;
    return newCustomer;
  }

  @override
  Future<Customer> updateCustomer(Customer customer) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    if (!_mockCustomers.containsKey(customer.customerId)) {
      throw Exception('Customer not found');
    }
    _mockCustomers[customer.customerId] = customer;
    return customer;
  }

  @override
  Future<void> deleteCustomer(String id) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    if (!_mockCustomers.containsKey(id)) {
      throw Exception('Customer not found');
    }
    _mockCustomers.remove(id);
  }
}

void main() {
  group('SupabaseDatabaseService Customer Operations', () {
    late MockSupabaseDatabaseService databaseService;
    late MockSupabaseClientProvider mockClientProvider;
    late ErrorHandler errorHandler;
    late Logger logger;

    setUp(() {
      mockClientProvider = MockSupabaseClientProvider(useMockData: true);
      logger = Logger(print: debugPrint, debugMode: true);
      errorHandler = ErrorHandler(logger);
      databaseService = MockSupabaseDatabaseService(
        mockClientProvider,
        errorHandler,
      );
      databaseService.resetMockData();
    });

    test('getCustomers returns customers when successful', () async {
      // Act
      final result = await databaseService.getCustomers();

      // Assert
      expect(result, isA<List<Customer>>());
      expect(result, isNotEmpty);
      expect(result.length, equals(2));
      expect(result.first.customerId, equals('C001'));
      expect(result.first.customerName, equals('Test Customer 1'));
    });

    test('getCustomerById returns customer when found', () async {
      // Act
      final result = await databaseService.getCustomerById('C001');

      // Assert
      expect(result, isA<Customer>());
      expect(result?.customerId, equals('C001'));
      expect(result?.customerName, equals('Test Customer 1'));
      expect(result?.address, equals('123 Test St, Test City, USA'));
      expect(result?.contactPerson, equals('John Doe'));
    });

    test('getCustomerById returns null when not found', () async {
      // Act
      final result = await databaseService.getCustomerById('NONEXISTENT');

      // Assert
      expect(result, isNull);
    });

    test('createCustomer creates customer successfully', () async {
      // Arrange
      const customer = Customer(
        customerId: '',
        customerName: 'New Customer',
        address: '789 New St, Newtown, USA',
        contactPerson: 'Bob Wilson',
        companyId: 1,
      );

      // Act
      final result = await databaseService.createCustomer(customer);

      // Assert
      expect(result, isA<Customer>());
      expect(result.customerId, startsWith('C'));
      expect(result.customerName, equals('New Customer'));
      expect(result.address, equals('789 New St, Newtown, USA'));
      expect(result.contactPerson, equals('Bob Wilson'));

      // Verify the item was added to the mock data
      final items = await databaseService.getCustomers();
      expect(items.length, equals(3));
      expect(items.any((item) => item.customerName == 'New Customer'), isTrue);
    });

    test('updateCustomer updates customer successfully', () async {
      // Arrange
      const customer = Customer(
        customerId: 'C001',
        customerName: 'Updated Test Customer 1',
        address: '123 Updated St, Test City, USA',
        contactPerson: 'John Updated',
        companyId: 1,
      );

      // Act
      final result = await databaseService.updateCustomer(customer);

      // Assert
      expect(result, isA<Customer>());
      expect(result.customerId, equals('C001'));
      expect(result.customerName, equals('Updated Test Customer 1'));
      expect(result.address, equals('123 Updated St, Test City, USA'));
      expect(result.contactPerson, equals('John Updated'));

      // Verify the item was updated in the mock data
      final updatedItem = await databaseService.getCustomerById('C001');
      expect(updatedItem?.customerName, equals('Updated Test Customer 1'));
    });

    test('deleteCustomer deletes customer successfully', () async {
      // Act
      await databaseService.deleteCustomer('C001');

      // Assert
      final deletedItem = await databaseService.getCustomerById('C001');
      expect(deletedItem, isNull);

      // Verify the item was removed from the mock data
      final items = await databaseService.getCustomers();
      expect(items.length, equals(1));
      expect(items.any((item) => item.customerId == 'C001'), isFalse);
    });

    test('createCustomer throws error when mock mode is disabled', () async {
      databaseService.mockMode = false;
      const customer = Customer(
        customerId: 'C001',
        customerName: 'New Customer',
        address: '789 New St, Newtown, USA',
        contactPerson: 'Bob Wilson',
        companyId: 1,
      );

      expect(
        () => databaseService.createCustomer(customer),
        throwsA(
          isA<Exception>().having(
            (e) => e.toString(),
            'message',
            'Exception: Supabase client is not initialized',
          ),
        ),
      );
    });

    test('updateCustomer throws error when mock mode is disabled', () async {
      databaseService.mockMode = false;
      const customer = Customer(
        customerId: 'C001',
        customerName: 'Updated Customer',
        address: '789 Updated St, Newtown, USA',
        contactPerson: 'Bob Updated',
        companyId: 1,
      );

      expect(
        () => databaseService.updateCustomer(customer),
        throwsA(
          isA<Exception>().having(
            (e) => e.toString(),
            'message',
            'Exception: Supabase client is not initialized',
          ),
        ),
      );
    });

    test('deleteCustomer throws error when mock mode is disabled', () async {
      databaseService.mockMode = false;

      expect(
        () => databaseService.deleteCustomer('C001'),
        throwsA(
          isA<Exception>().having(
            (e) => e.toString(),
            'message',
            'Exception: Supabase client is not initialized',
          ),
        ),
      );
    });
  });
}
