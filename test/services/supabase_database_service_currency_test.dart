import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/currency.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/services/logger.dart';
import '../mocks/mock_supabase_client_provider.dart';

class MockSupabaseDatabaseService extends SupabaseDatabaseService {
  final Map<String, Currency> _mockCurrencies = {};
  bool mockMode = true;

  MockSupabaseDatabaseService(super.clientProvider, super.errorHandler);

  void resetMockData() {
    _mockCurrencies.clear();
    _mockCurrencies.addAll({
      'USD': const Currency(currencyCode: 'USD', currencyName: 'US Dollar'),
      'EUR': const Currency(currencyCode: 'EUR', currencyName: 'Euro'),
    });
  }

  @override
  Future<List<Currency>> getCurrencies() async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    return _mockCurrencies.values.toList();
  }

  @override
  Future<Currency?> getCurrencyByCode(String currencyCode) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    return _mockCurrencies[currencyCode];
  }

  @override
  Future<Currency> createCurrency(Currency currency) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    _mockCurrencies[currency.currencyCode] = currency;
    return currency;
  }

  @override
  Future<Currency> updateCurrency(Currency currency) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    if (!_mockCurrencies.containsKey(currency.currencyCode)) {
      throw Exception('Currency not found');
    }
    _mockCurrencies[currency.currencyCode] = currency;
    return currency;
  }

  @override
  Future<Currency?> deleteCurrency(String currencyCode) async {
    if (!mockMode) {
      throw Exception('Supabase client is not initialized');
    }
    if (!_mockCurrencies.containsKey(currencyCode)) {
      throw Exception('Currency not found');
    }
    _mockCurrencies.remove(currencyCode);
    return null;
  }
}

void main() {
  late MockSupabaseDatabaseService databaseService;
  late ErrorHandler errorHandler;
  late Logger logger;

  setUp(() {
    logger = Logger(print: print, debugMode: true);
    errorHandler = ErrorHandler(logger);
    databaseService = MockSupabaseDatabaseService(
      MockSupabaseClientProvider(),
      errorHandler,
    );
    databaseService.resetMockData();
  });

  group('Currency Operations', () {
    test('getCurrencies returns all currencies', () async {
      final currencies = await databaseService.getCurrencies();
      expect(currencies.length, 2);
      expect(currencies.any((c) => c.currencyCode == 'USD'), true);
      expect(currencies.any((c) => c.currencyCode == 'EUR'), true);
    });

    test('getCurrencyByCode returns correct currency', () async {
      final currency = await databaseService.getCurrencyByCode('USD');
      expect(currency, isNotNull);
      expect(currency?.currencyCode, 'USD');
      expect(currency?.currencyName, 'US Dollar');
    });

    test('createCurrency adds new currency', () async {
      const newCurrency = Currency(
        currencyCode: 'GBP',
        currencyName: 'British Pound',
      );
      final created = await databaseService.createCurrency(newCurrency);
      expect(created.currencyCode, 'GBP');
      expect(created.currencyName, 'British Pound');

      final currencies = await databaseService.getCurrencies();
      expect(currencies.length, 3);
      expect(currencies.any((c) => c.currencyCode == 'GBP'), true);
    });

    test('updateCurrency modifies existing currency', () async {
      const updatedCurrency = Currency(
        currencyCode: 'USD',
        currencyName: 'United States Dollar',
      );
      final updated = await databaseService.updateCurrency(updatedCurrency);
      expect(updated.currencyCode, 'USD');
      expect(updated.currencyName, 'United States Dollar');

      final currency = await databaseService.getCurrencyByCode('USD');
      expect(currency?.currencyName, 'United States Dollar');
    });

    test('deleteCurrency removes currency', () async {
      await databaseService.deleteCurrency('USD');
      final currencies = await databaseService.getCurrencies();
      expect(currencies.length, 1);
      expect(currencies.any((c) => c.currencyCode == 'USD'), false);
    });

    test('updateCurrency throws error for non-existent currency', () async {
      const nonExistentCurrency = Currency(
        currencyCode: 'XXX',
        currencyName: 'Non-existent Currency',
      );
      expect(
        () => databaseService.updateCurrency(nonExistentCurrency),
        throwsException,
      );
    });

    test('deleteCurrency throws error for non-existent currency', () async {
      expect(() => databaseService.deleteCurrency('XXX'), throwsException);
    });

    test('getCurrencyByCode returns null for non-existent currency', () async {
      final currency = await databaseService.getCurrencyByCode('XXX');
      expect(currency, isNull);
    });
  });
}
