import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/vendor_invoice_line_item_service.dart';
import 'package:we_like_money/utils/exceptions.dart';

@GenerateMocks([DatabaseService])
import 'vendor_invoice_line_item_service_test.mocks.dart';

void main() {
  late VendorInvoiceLineItemService service;
  late MockDatabaseService mockDatabaseService;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    service = VendorInvoiceLineItemService(mockDatabaseService);
  });

  group('VendorInvoiceLineItemService', () {
    const testLineItem = VendorInvoiceLineItem(
      lineItemId: 1,
      invoiceId: 100,
      accountNumber: '5000',
      description: 'Office Supplies',
      amount: 50.0,
      taxAmount: 5.0,
      companyId: 1,
    );

    test('getLineItems returns list of line items when successful', () async {
      when(
        mockDatabaseService.getVendorInvoiceLineItems(100),
      ).thenAnswer((_) async => [testLineItem]);

      final result = await service.getLineItems(100);

      expect(result, [testLineItem]);
      verify(mockDatabaseService.getVendorInvoiceLineItems(100)).called(1);
    });

    test(
      'getLineItems throws BusinessException when database service throws',
      () async {
        when(
          mockDatabaseService.getVendorInvoiceLineItems(100),
        ).thenThrow(Exception('Database error'));

        expect(
          () => service.getLineItems(100),
          throwsA(isA<BusinessException>()),
        );
      },
    );

    test('getLineItemById returns line item when found', () async {
      when(
        mockDatabaseService.getVendorInvoiceLineItemById(1),
      ).thenAnswer((_) async => testLineItem);

      final result = await service.getLineItemById(1);

      expect(result, testLineItem);
      verify(mockDatabaseService.getVendorInvoiceLineItemById(1)).called(1);
    });

    test('getLineItemById returns null when not found', () async {
      when(
        mockDatabaseService.getVendorInvoiceLineItemById(1),
      ).thenAnswer((_) async => null);

      final result = await service.getLineItemById(1);

      expect(result, null);
      verify(mockDatabaseService.getVendorInvoiceLineItemById(1)).called(1);
    });

    test('createLineItem returns created line item', () async {
      when(
        mockDatabaseService.createVendorInvoiceLineItem(testLineItem),
      ).thenAnswer((_) async => testLineItem);

      final result = await service.createLineItem(testLineItem);

      expect(result, testLineItem);
      verify(
        mockDatabaseService.createVendorInvoiceLineItem(testLineItem),
      ).called(1);
    });

    test('updateLineItem returns updated line item', () async {
      when(
        mockDatabaseService.updateVendorInvoiceLineItem(testLineItem),
      ).thenAnswer((_) async => testLineItem);

      final result = await service.updateLineItem(testLineItem);

      expect(result, testLineItem);
      verify(
        mockDatabaseService.updateVendorInvoiceLineItem(testLineItem),
      ).called(1);
    });

    test('deleteLineItem deletes the line item', () async {
      await service.deleteLineItem(1);

      verify(mockDatabaseService.deleteVendorInvoiceLineItem(1)).called(1);
    });

    test('getLineItemsByAccount returns list of line items', () async {
      when(
        mockDatabaseService.getVendorInvoiceLineItemsByAccount('5000'),
      ).thenAnswer((_) async => [testLineItem]);

      final result = await service.getLineItemsByAccount('5000');

      expect(result, [testLineItem]);
      verify(
        mockDatabaseService.getVendorInvoiceLineItemsByAccount('5000'),
      ).called(1);
    });

    test('createLineItems creates multiple line items', () async {
      final lineItems = [testLineItem, testLineItem.copyWith(lineItemId: 2)];
      when(mockDatabaseService.createVendorInvoiceLineItem(any)).thenAnswer(
        (invocation) async =>
            invocation.positionalArguments[0] as VendorInvoiceLineItem,
      );

      final result = await service.createLineItems(lineItems);

      expect(result, lineItems);
      verify(mockDatabaseService.createVendorInvoiceLineItem(any)).called(2);
    });

    test('updateLineItems updates multiple line items', () async {
      final lineItems = [testLineItem, testLineItem.copyWith(lineItemId: 2)];
      when(mockDatabaseService.updateVendorInvoiceLineItem(any)).thenAnswer(
        (invocation) async =>
            invocation.positionalArguments[0] as VendorInvoiceLineItem,
      );

      final result = await service.updateLineItems(lineItems);

      expect(result, lineItems);
      verify(mockDatabaseService.updateVendorInvoiceLineItem(any)).called(2);
    });

    test('deleteLineItems deletes multiple line items', () async {
      await service.deleteLineItems([1, 2]);

      verify(mockDatabaseService.deleteVendorInvoiceLineItem(1)).called(1);
      verify(mockDatabaseService.deleteVendorInvoiceLineItem(2)).called(1);
    });
  });
}
