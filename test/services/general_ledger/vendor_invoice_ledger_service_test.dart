import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/vendor_invoice_ledger_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'vendor_invoice_ledger_service_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  group('VendorInvoiceLedgerService', () {
    late VendorInvoiceLedgerService service;
    late MockDatabaseService mockDatabaseService;
    late MockErrorHandler mockErrorHandler;
    late VendorInvoice testInvoice;
    late String transactionId;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      mockErrorHandler = MockErrorHandler();
      service = VendorInvoiceLedgerService(
        mockDatabaseService,
        mockErrorHandler,
      );

      transactionId = const Uuid().v4();
      testInvoice = VendorInvoice(
        invoiceId: 1,
        invoiceNumber: 'INV-001',
        vendorId: 'V001',
        invoiceDate: DateTime(2024, 1, 1),
        dueDate: DateTime(2024, 2, 1),
        amount: 1000.0,
        currencyCode: 'USD',
        companyId: 1,
        expenseAccountNumber: '5000',
        taxAmount: 100.0,
        projectId: null,
        staffId: null,
        isPaid: false,
      );
    });

    test('creates correct entries for vendor invoice', () async {
      // Arrange
      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: transactionId,
        transactionDate: testInvoice.invoiceDate,
        accountNumber: testInvoice.expenseAccountNumber,
        description: 'Invoice ${testInvoice.invoiceNumber}',
        debit: testInvoice.amount,
        credit: 0,
        currencyCode: testInvoice.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: testInvoice.taxAmount,
        companyId: testInvoice.companyId!,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: transactionId,
        transactionDate: testInvoice.invoiceDate,
        accountNumber: '2100', // Accounts Payable account
        description: 'Invoice ${testInvoice.invoiceNumber}',
        debit: 0,
        credit: testInvoice.amount,
        currencyCode: testInvoice.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: testInvoice.taxAmount,
        companyId: testInvoice.companyId!,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createGeneralLedgerEntriesForVendorInvoice(
        testInvoice,
      );

      // Assert
      expect(result.length, 2);
      expect(result[0].debit, testInvoice.amount);
      expect(result[1].credit, testInvoice.amount);
      expect(result[0].accountNumber, testInvoice.expenseAccountNumber);
      expect(result[1].accountNumber, '2100');
      expect(result[0].transactionId, isNotEmpty);
      expect(result[1].transactionId, isNotEmpty);
      expect(result[0].transactionId, result[1].transactionId);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('handles invoice without tax', () async {
      // Arrange
      final noTaxInvoice = testInvoice.copyWith(taxAmount: null);

      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: transactionId,
        transactionDate: noTaxInvoice.invoiceDate,
        accountNumber: noTaxInvoice.expenseAccountNumber,
        description: 'Invoice ${noTaxInvoice.invoiceNumber}',
        debit: noTaxInvoice.amount,
        credit: 0,
        currencyCode: noTaxInvoice.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: null,
        companyId: noTaxInvoice.companyId!,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: transactionId,
        transactionDate: noTaxInvoice.invoiceDate,
        accountNumber: '2100',
        description: 'Invoice ${noTaxInvoice.invoiceNumber}',
        debit: 0,
        credit: noTaxInvoice.amount,
        currencyCode: noTaxInvoice.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: null,
        companyId: noTaxInvoice.companyId!,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createGeneralLedgerEntriesForVendorInvoice(
        noTaxInvoice,
      );

      // Assert
      expect(result.length, 2);
      expect(result[0].debit, noTaxInvoice.amount);
      expect(result[1].credit, noTaxInvoice.amount);
      expect(result[0].taxAmount, null);
      expect(result[1].taxAmount, null);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('handles invoice with project and staff', () async {
      // Arrange
      final invoiceWithProjectAndStaff = testInvoice.copyWith(
        projectId: 1,
        staffId: 1,
      );

      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: transactionId,
        transactionDate: invoiceWithProjectAndStaff.invoiceDate,
        accountNumber: invoiceWithProjectAndStaff.expenseAccountNumber,
        description: 'Invoice ${invoiceWithProjectAndStaff.invoiceNumber}',
        debit: invoiceWithProjectAndStaff.amount,
        credit: 0,
        currencyCode: invoiceWithProjectAndStaff.currencyCode,
        projectId: 1,
        staffId: 1,
        taxAmount: invoiceWithProjectAndStaff.taxAmount,
        companyId: invoiceWithProjectAndStaff.companyId!,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: transactionId,
        transactionDate: invoiceWithProjectAndStaff.invoiceDate,
        accountNumber: '2100',
        description: 'Invoice ${invoiceWithProjectAndStaff.invoiceNumber}',
        debit: 0,
        credit: invoiceWithProjectAndStaff.amount,
        currencyCode: invoiceWithProjectAndStaff.currencyCode,
        projectId: 1,
        staffId: 1,
        taxAmount: invoiceWithProjectAndStaff.taxAmount,
        companyId: invoiceWithProjectAndStaff.companyId!,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createGeneralLedgerEntriesForVendorInvoice(
        invoiceWithProjectAndStaff,
      );

      // Assert
      expect(result.length, 2);
      expect(result[0].projectId, 1);
      expect(result[1].projectId, 1);
      expect(result[0].staffId, 1);
      expect(result[1].staffId, 1);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('handles database errors', () async {
      // Arrange
      final serviceWithMockDisabled = VendorInvoiceLedgerService(
        mockDatabaseService,
        mockErrorHandler,
        enableMockCreation: false,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('Handled error');

      // Act & Assert
      await expectLater(
        () => serviceWithMockDisabled
            .createGeneralLedgerEntriesForVendorInvoice(testInvoice),
        throwsA(isA<BusinessException>()),
      );
      verify(mockErrorHandler.handleError(any)).called(1);
    });

    test('throws error when company ID is null', () async {
      // Arrange
      final invoiceWithoutCompany = testInvoice.copyWith(companyId: null);

      // Act & Assert
      expect(
        () => service.createGeneralLedgerEntriesForVendorInvoice(
          invoiceWithoutCompany,
        ),
        throwsA(isA<BusinessException>()),
      );
      verifyNever(mockDatabaseService.createGeneralLedgerEntry(any));
    });

    test('creates mock entries in debug mode when database fails', () async {
      // Arrange
      final debugService = VendorInvoiceLedgerService(
        mockDatabaseService,
        mockErrorHandler,
        enableMockCreation: true,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry;
      });

      // Act
      final result = await debugService
          .createGeneralLedgerEntriesForVendorInvoice(testInvoice);

      // Assert
      expect(result.length, 2);
      expect(
        result[0].debit,
        testInvoice.amount + (testInvoice.taxAmount ?? 0),
      );
      expect(
        result[1].credit,
        testInvoice.amount + (testInvoice.taxAmount ?? 0),
      );
      expect(result[0].accountNumber, testInvoice.expenseAccountNumber);
      expect(result[1].accountNumber, '2100');

      // Store and compare transaction IDs
      final debitTransactionId = result[0].transactionId;
      final creditTransactionId = result[1].transactionId;
      expect(debitTransactionId, isNotEmpty);
      expect(creditTransactionId, isNotEmpty);
      expect(
        debitTransactionId,
        equals(creditTransactionId),
        reason: 'Both entries should have the same transaction ID',
      );

      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });
  });
}
