import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart' hide any;
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/expense_ledger_service.dart';
import 'package:we_like_money/utils/error_handler.dart' as error_handler;
import 'package:we_like_money/services/logger.dart';
import 'expense_ledger_service_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  group('ExpenseLedgerService', () {
    late ExpenseLedgerService service;
    late MockDatabaseService mockDatabaseService;
    late error_handler.ErrorHandler mockErrorHandler;
    late Expense testExpense;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      mockErrorHandler = error_handler.ErrorHandler(
        Logger(print: debugPrint, debugMode: true),
      );
      service = ExpenseLedgerService(mockDatabaseService, mockErrorHandler);

      testExpense = Expense(
        expenseId: 1,
        transactionId: const Uuid().v4(),
        vendorId: 'V001',
        expenseDate: DateTime(2024, 1, 1),
        amount: 1000.0,
        currencyCode: 'USD',
        companyId: 1,
        paymentMethod: PaymentMethod.cash,
        taxAmount: 100.0,
        projectId: null,
        staffId: null,
      );
    });

    test('creates correct entries for cash payment', () async {
      // Arrange
      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: testExpense.transactionId,
        transactionDate: testExpense.expenseDate,
        accountNumber: '5000',
        description: 'Expense: ${testExpense.vendorId}',
        debit: 1100.0, // amount + tax
        credit: 0,
        currencyCode: testExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: testExpense.taxAmount,
        companyId: testExpense.companyId,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: testExpense.transactionId,
        transactionDate: testExpense.expenseDate,
        accountNumber: '1000', // Cash account
        description: 'Payment for expense: ${testExpense.vendorId}',
        debit: 0,
        credit: 1100.0, // amount + tax
        currencyCode: testExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: testExpense.taxAmount,
        companyId: testExpense.companyId,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createEntriesForExpense(testExpense);

      // Assert
      expect(result.length, 2);
      expect(result[0].debit, 1100.0);
      expect(result[1].credit, 1100.0);
      expect(result[0].accountNumber, '5000');
      expect(result[1].accountNumber, '1000');
      expect(result[0].transactionId, result[1].transactionId);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('creates correct entries for credit card payment', () async {
      // Arrange
      final creditCardExpense = testExpense.copyWith(
        paymentMethod: PaymentMethod.creditCard,
      );

      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: creditCardExpense.transactionId,
        transactionDate: creditCardExpense.expenseDate,
        accountNumber: '5000',
        description: 'Expense: ${creditCardExpense.vendorId}',
        debit: 1100.0,
        credit: 0,
        currencyCode: creditCardExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: creditCardExpense.taxAmount,
        companyId: creditCardExpense.companyId,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: creditCardExpense.transactionId,
        transactionDate: creditCardExpense.expenseDate,
        accountNumber: '2000', // Credit Card account
        description: 'Payment for expense: ${creditCardExpense.vendorId}',
        debit: 0,
        credit: 1100.0,
        currencyCode: creditCardExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: creditCardExpense.taxAmount,
        companyId: creditCardExpense.companyId,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createEntriesForExpense(creditCardExpense);

      // Assert
      expect(result.length, 2);
      expect(result[0].debit, 1100.0);
      expect(result[1].credit, 1100.0);
      expect(result[0].accountNumber, '5000');
      expect(result[1].accountNumber, '2000');
      expect(result[0].transactionId, result[1].transactionId);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('handles expense without tax', () async {
      // Arrange
      final noTaxExpense = testExpense.copyWith(taxAmount: null);

      final debitEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: noTaxExpense.transactionId,
        transactionDate: noTaxExpense.expenseDate,
        accountNumber: '5000',
        description: 'Expense: ${noTaxExpense.vendorId}',
        debit: 1000.0, // amount only
        credit: 0,
        currencyCode: noTaxExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: null,
        companyId: noTaxExpense.companyId,
      );

      final creditEntry = GeneralLedger(
        ledgerId: 2,
        transactionId: noTaxExpense.transactionId,
        transactionDate: noTaxExpense.expenseDate,
        accountNumber: '1000',
        description: 'Payment for expense: ${noTaxExpense.vendorId}',
        debit: 0,
        credit: 1000.0, // amount only
        currencyCode: noTaxExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: null,
        companyId: noTaxExpense.companyId,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((invocation) async {
        final entry = invocation.positionalArguments[0] as GeneralLedger;
        return entry.debit > 0 ? debitEntry : creditEntry;
      });

      // Act
      final result = await service.createEntriesForExpense(noTaxExpense);

      // Assert
      expect(result.length, 2);
      expect(result[0].debit, 1000.0);
      expect(result[1].credit, 1000.0);
      expect(result[0].taxAmount, null);
      expect(result[1].taxAmount, null);
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(2);
    });

    test('handles database errors', () async {
      // Arrange
      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenThrow(Exception('Database error'));

      // Act & Assert
      expect(
        () => service.createEntriesForExpense(testExpense),
        throwsException,
      );
      verify(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).called(1);
    });

    test('handles all payment methods', () async {
      // Arrange
      final mockEntry = GeneralLedger(
        ledgerId: 1,
        transactionId: testExpense.transactionId,
        transactionDate: testExpense.expenseDate,
        accountNumber: '5000',
        description: 'Test entry',
        debit: 1100.0,
        credit: 0,
        currencyCode: testExpense.currencyCode,
        projectId: null,
        staffId: null,
        taxAmount: testExpense.taxAmount,
        companyId: testExpense.companyId,
      );

      when(
        mockDatabaseService.createGeneralLedgerEntry(
          argThat(isA<GeneralLedger>()),
        ),
      ).thenAnswer((_) async => mockEntry);

      // Test each payment method
      for (final paymentMethod in [
        PaymentMethod.cash,
        PaymentMethod.creditCard,
        PaymentMethod.bankTransfer,
        PaymentMethod.check,
        PaymentMethod.other,
      ]) {
        final result = await service.createEntriesForExpense(
          testExpense.copyWith(paymentMethod: paymentMethod),
        );
        expect(result, isNotEmpty);
      }
    });
  });
}
