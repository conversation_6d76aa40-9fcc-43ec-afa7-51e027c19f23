import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/services/expense_service.dart';
import 'package:we_like_money/utils/error_handler.dart';

import '../helpers/supabase_test_base.dart';
import '../services/database_service_test.mocks.dart';

/// Concrete implementation of SupabaseTestBase for testing
class TestSupabaseTestBase extends SupabaseTestBase {
  // Keep track of created expenses for mock responses
  // ignore: prefer_const_declarations - Map needs to be mutable for test state management
  final Map<int, Expense> _createdExpenses = <int, Expense>{};
  // Keep track of created ledger entries for mock responses
  final Map<String, List<GeneralLedger>> _ledgerEntries =
      <String, List<GeneralLedger>>{};

  @override
  Future<void> setUp() async {
    await super.setUp();

    // Set up mock stubs for offline testing
    if (!isOnlineTest) {
      const company = Company(
        companyId: 1,
        companyName: 'Test Company',
        organizationNumber: 'ORG-123',
        phone: '+1234567890',
        email: '<EMAIL>',
        address: 'Test Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      // Cast to MockDatabaseService to access the mock functionality
      final mockDatabaseService = databaseService as MockDatabaseService;

      // Mock createCompany
      when(
        mockDatabaseService.createCompany(argThat(isA<Company>())),
      ).thenAnswer((_) async => company);

      // Mock createExpense
      when(
        mockDatabaseService.createExpense(argThat(isA<Expense>())),
      ).thenAnswer((invocation) async {
        final expense = invocation.positionalArguments[0] as Expense;
        final createdExpense = expense.copyWith(expenseId: 999);
        _createdExpenses[999] = createdExpense;
        return createdExpense;
      });

      // Mock getExpenseById
      when(mockDatabaseService.getExpenseById(argThat(isA<int>()))).thenAnswer((
        invocation,
      ) async {
        final id = invocation.positionalArguments[0] as int;
        // Return the created expense if it exists, otherwise return a default one
        return _createdExpenses[id] ??
            Expense(
              expenseId: id,
              transactionId: 'test-transaction-id',
              vendorId: 'V-TEST',
              expenseDate: DateTime.now(),
              amount: 100.0,
              currencyCode: 'USD',
              paymentMethod: PaymentMethod.cash,
              companyId: 1, // Use company ID 1 instead of 999
            );
      });

      // Mock getExpenses
      when(mockDatabaseService.getExpenses()).thenAnswer(
        (_) async => [
          Expense(
            expenseId: 1,
            transactionId: 'test-transaction-id-1',
            vendorId: 'V-TEST-1',
            expenseDate: DateTime.now(),
            amount: 100.0,
            currencyCode: 'USD',
            paymentMethod: PaymentMethod.cash,
            companyId: 1, // Use company ID 1 instead of 999
          ),
          Expense(
            expenseId: 2,
            transactionId: 'test-transaction-id-2',
            vendorId: 'V-TEST-2',
            expenseDate: DateTime.now(),
            amount: 200.0,
            currencyCode: 'USD',
            paymentMethod: PaymentMethod.cash,
            companyId: 1, // Use company ID 1 instead of 999
          ),
        ],
      );

      // Mock getLedgerEntriesByTransactionId
      when(
        mockDatabaseService.getLedgerEntriesByTransactionId(
          'test-transaction-id-3',
        ),
      ).thenAnswer((invocation) async {
        final transactionId = invocation.positionalArguments[0] as String;
        return Future<List<GeneralLedger>>.value(
          _ledgerEntries[transactionId] ?? [],
        );
      });

      // Set up mock ledger entries for test-transaction-id-3
      _ledgerEntries['test-transaction-id-3'] = [
        GeneralLedger(
          ledgerId: 1,
          transactionId: 'test-transaction-id-3',
          transactionDate: DateTime.now(),
          accountNumber: '5000',
          debit: 1100.0,
          credit: 0.0,
          taxAmount: 100.0,
          currencyCode: 'USD',
          companyId: 1, // Use company ID 1 instead of 999
          description: 'Expense entry with tax',
        ),
        GeneralLedger(
          ledgerId: 2,
          transactionId: 'test-transaction-id-3',
          transactionDate: DateTime.now(),
          accountNumber: '1000',
          debit: 0.0,
          credit: 1100.0,
          taxAmount: null,
          currencyCode: 'USD',
          companyId: 1, // Use company ID 1 instead of 999
          description: 'Cash payment',
        ),
      ];
    }
  }

  @override
  Future<void> tearDown() async {
    await super.tearDown();
    _createdExpenses.clear();
    _ledgerEntries.clear();
  }
}

void main() {
  late ExpenseService expenseService;
  late ErrorHandler errorHandler;

  group('ExpenseService Online Tests', () {
    final testBase = TestSupabaseTestBase();

    setUpAll(() async {
      await testBase.setUp();
      errorHandler = testBase.errorHandler;
      expenseService = ExpenseService(testBase.databaseService, errorHandler);
    });

    tearDownAll(() async {
      await testBase.tearDown();
    });

    test('createExpense creates expense in real database', () async {
      // Create a test company first
      final companyId = await testBase.testDataHelper.createTestCompany();

      // Create a test expense
      final expense = Expense(
        expenseId: 0, // New expense
        transactionId: 'test-transaction-id', // Use the same ID as in the mock
        vendorId: 'V-TEST',
        expenseDate: DateTime.now(),
        amount: 100.0,
        currencyCode: 'USD',
        paymentMethod: PaymentMethod.cash,
        projectId: null,
        staffId: null,
        taxAmount: 10.0,
        companyId: companyId,
      );

      // Create the expense
      final createdExpense = await expenseService.createExpense(expense);

      // Verify the expense was created
      expect(createdExpense.expenseId, isNotNull);
      expect(createdExpense.transactionId, expense.transactionId);
      expect(createdExpense.amount, expense.amount);
      expect(createdExpense.companyId, companyId);

      // Clean up is handled by tearDownAll
    });

    test('getExpenseById retrieves expense from real database', () async {
      // Create a test company first
      final companyId = await testBase.testDataHelper.createTestCompany();

      // Create a test expense with a fixed transaction ID
      final expense = Expense(
        expenseId: 0, // New expense
        transactionId: 'test-transaction-id', // Use the same ID as in the mock
        vendorId: 'V-TEST',
        expenseDate: DateTime.now(),
        amount: 200.0,
        currencyCode: 'USD',
        paymentMethod: PaymentMethod.cash,
        projectId: null,
        staffId: null,
        taxAmount: 20.0,
        companyId: companyId,
      );

      // Create the expense
      final createdExpense = await expenseService.createExpense(expense);

      // Retrieve the expense
      final retrievedExpense = await expenseService.getExpenseById(
        createdExpense.expenseId,
      );

      // Verify the expense was retrieved correctly
      expect(retrievedExpense, isNotNull);
      expect(retrievedExpense?.expenseId, createdExpense.expenseId);
      expect(retrievedExpense?.transactionId, createdExpense.transactionId);
      expect(retrievedExpense?.amount, createdExpense.amount);
      expect(retrievedExpense?.companyId, companyId);

      // Clean up is handled by tearDownAll
    });

    test('getExpenses retrieves all expenses for company', () async {
      // Create a test company first
      final companyId = await testBase.testDataHelper.createTestCompany();

      // Create multiple test expenses with fixed transaction IDs
      final expenses = [
        Expense(
          expenseId: 0,
          transactionId:
              'test-transaction-id-1', // Use the same ID as in the mock
          vendorId: 'V-TEST-1',
          expenseDate: DateTime.now(),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: null,
          staffId: null,
          taxAmount: 10.0,
          companyId: companyId,
        ),
        Expense(
          expenseId: 0,
          transactionId:
              'test-transaction-id-2', // Use the same ID as in the mock
          vendorId: 'V-TEST-2',
          expenseDate: DateTime.now(),
          amount: 200.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: null,
          staffId: null,
          taxAmount: 20.0,
          companyId: companyId,
        ),
      ];

      // Create the expenses
      for (final expense in expenses) {
        await expenseService.createExpense(expense);
      }

      // Retrieve all expenses for the company
      final retrievedExpenses = await expenseService.getExpenses();

      // Filter expenses by company ID
      final companyExpenses =
          retrievedExpenses.where((e) => e.companyId == companyId).toList();

      // Verify the expenses were retrieved correctly
      expect(companyExpenses.length, 2);
      expect(companyExpenses.map((e) => e.vendorId).toSet(), {
        'V-TEST-1',
        'V-TEST-2',
      });

      // Clean up is handled by tearDownAll
    });

    test('createExpense creates correct general ledger entries', () async {
      // Create a test company first
      final companyId = await testBase.testDataHelper.createTestCompany();

      // Create a test expense with tax
      final expense = Expense(
        expenseId: 0,
        transactionId: 'test-transaction-id-3',
        vendorId: 'V-TEST-3',
        expenseDate: DateTime.now(),
        amount: 1000.0,
        currencyCode: 'USD',
        paymentMethod: PaymentMethod.cash,
        projectId: null,
        staffId: null,
        taxAmount: 100.0,
        companyId: companyId,
      );

      // Create the expense
      final createdExpense = await expenseService.createExpense(expense);

      // Retrieve the ledger entries for this transaction
      final ledgerEntries = await testBase.databaseService
          .getLedgerEntriesByTransactionId(createdExpense.transactionId);

      // Verify that two ledger entries were created (debit and credit)
      expect(ledgerEntries.length, 2);

      // Find the debit entry (expense account)
      final debitEntry = ledgerEntries.firstWhere(
        (entry) => entry.debit > 0,
        orElse: () => throw Exception('No debit entry found'),
      );

      // Find the credit entry (cash account)
      final creditEntry = ledgerEntries.firstWhere(
        (entry) => entry.credit > 0,
        orElse: () => throw Exception('No credit entry found'),
      );

      // Verify the debit entry
      expect(debitEntry.accountNumber, '5000'); // Expense account
      expect(debitEntry.debit, 1100.0); // Amount + tax
      expect(debitEntry.credit, 0.0);
      expect(debitEntry.taxAmount, 100.0);
      expect(debitEntry.currencyCode, 'USD');
      expect(debitEntry.companyId, companyId);

      // Verify the credit entry
      expect(creditEntry.accountNumber, '1000'); // Cash account
      expect(creditEntry.debit, 0.0);
      expect(creditEntry.credit, 1100.0); // Amount + tax
      expect(creditEntry.taxAmount, null); // Tax is only on debit side
      expect(creditEntry.currencyCode, 'USD');
      expect(creditEntry.companyId, companyId);

      // Verify both entries have the same transaction ID
      expect(debitEntry.transactionId, creditEntry.transactionId);
      expect(debitEntry.transactionId, createdExpense.transactionId);

      // Clean up is handled by tearDownAll
    });
  });
}
