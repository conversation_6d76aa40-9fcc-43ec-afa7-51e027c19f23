import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/expense_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/utils/exceptions.dart';

import '../config/test_config.dart';
import 'expense_service_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  group('ExpenseService', () {
    late ExpenseService expenseService;
    late MockDatabaseService mockDatabaseService;
    late MockErrorHandler mockErrorHandler;

    setUpAll(() async {
      await TestConfig.instance.initialize();
      debugPrint('Running tests in OFFLINE mode with mocks');
    });

    setUp(() {
      // Set up mocks for tests
      mockDatabaseService = MockDatabaseService();
      mockErrorHandler = MockErrorHandler();
      expenseService = ExpenseService(mockDatabaseService, mockErrorHandler);
      debugPrint('Using MockDatabaseService and MockErrorHandler for testing');

      // Set up common error handler stubs
      when(mockErrorHandler.handleError(any)).thenAnswer((invocation) {
        final error = invocation.positionalArguments[0];
        if (error is BusinessException) {
          return error.toString();
        }
        return 'An error occurred';
      });
    });

    group('getExpenses', () {
      test('returns expenses when successful', () async {
        // Mock setup
        when(mockDatabaseService.getExpenses()).thenAnswer(
          (_) async => [
            Expense(
              expenseId: 1,
              transactionId: const Uuid().v4(),
              vendorId: 'V001',
              expenseDate: DateTime(2023, 1, 1),
              amount: 100.0,
              currencyCode: 'USD',
              paymentMethod: PaymentMethod.cash,
              projectId: 1,
              staffId: 1,
              taxAmount: 10.0,
              companyId: 1,
            ),
            Expense(
              expenseId: 2,
              transactionId: const Uuid().v4(),
              vendorId: 'V002',
              expenseDate: DateTime(2023, 1, 2),
              amount: 200.0,
              currencyCode: 'USD',
              paymentMethod: PaymentMethod.creditCard,
              projectId: 2,
              staffId: 2,
              taxAmount: 20.0,
              companyId: 1,
            ),
          ],
        );

        // Act
        final expenses = await expenseService.getExpenses();

        // Assert
        expect(expenses.length, 2);
        expect(expenses[0].expenseId, 1);
        expect(expenses[0].vendorId, 'V001');
        expect(expenses[0].expenseDate, DateTime(2023, 1, 1));
        expect(expenses[0].amount, 100.0);
        expect(expenses[1].expenseId, 2);
        expect(expenses[1].vendorId, 'V002');
        expect(expenses[1].expenseDate, DateTime(2023, 1, 2));
        expect(expenses[1].amount, 200.0);
        verify(mockDatabaseService.getExpenses()).called(1);
      });

      test('returns empty list when no expenses exist', () async {
        // Mock setup
        when(mockDatabaseService.getExpenses()).thenAnswer((_) async => []);

        // Act
        final expenses = await expenseService.getExpenses();

        // Assert
        expect(expenses, isEmpty);
        verify(mockDatabaseService.getExpenses()).called(1);
      });

      test('handles database error and throws BusinessException', () async {
        // Mock setup
        final error = Exception('Database connection failed');
        when(mockDatabaseService.getExpenses()).thenThrow(error);
        when(
          mockErrorHandler.handleError(error),
        ).thenReturn('Failed to fetch expenses');

        // Act & Assert
        expect(
          () => expenseService.getExpenses(),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to fetch expenses',
            ),
          ),
        );
        verify(mockDatabaseService.getExpenses()).called(1);
        verify(mockErrorHandler.handleError(error)).called(1);
      });
    });

    group('getExpenseById', () {
      test('returns expense when found', () async {
        // Mock setup
        final testExpense = Expense(
          expenseId: 1,
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        when(
          mockDatabaseService.getExpenseById(1),
        ).thenAnswer((_) async => testExpense);

        // Act
        final expense = await expenseService.getExpenseById(1);

        // Assert
        expect(expense, testExpense);
        verify(mockDatabaseService.getExpenseById(1)).called(1);
      });

      test('returns null when expense not found', () async {
        // Mock setup
        when(
          mockDatabaseService.getExpenseById(999),
        ).thenAnswer((_) async => null);

        // Act
        final expense = await expenseService.getExpenseById(999);

        // Assert
        expect(expense, isNull);
        verify(mockDatabaseService.getExpenseById(999)).called(1);
      });

      test('handles database error and throws BusinessException', () async {
        // Mock setup
        final error = Exception('Database connection failed');
        when(mockDatabaseService.getExpenseById(1)).thenThrow(error);
        when(
          mockErrorHandler.handleError(error),
        ).thenReturn('Failed to fetch expense');

        // Act & Assert
        expect(
          () => expenseService.getExpenseById(1),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to fetch expense',
            ),
          ),
        );
        verify(mockDatabaseService.getExpenseById(1)).called(1);
        verify(mockErrorHandler.handleError(error)).called(1);
      });
    });

    group('createExpense', () {
      test('creates expense successfully', () async {
        // Prepare a test expense
        final testExpense = Expense(
          expenseId: 0, // New expense
          transactionId: const Uuid().v4(),
          vendorId: 'V-TEST',
          expenseDate: DateTime(2023, 1, 1),
          amount: 99.99,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        // Mock setup - return with an ID assigned
        final createdExpense = testExpense.copyWith(expenseId: 999);

        when(
          mockDatabaseService.createExpense(testExpense),
        ).thenAnswer((_) async => createdExpense);

        // Act
        final expense = await expenseService.createExpense(testExpense);

        // Assert
        expect(expense.expenseId, 999);
        expect(expense.transactionId, testExpense.transactionId);
        expect(expense.amount, testExpense.amount);
        verify(mockDatabaseService.createExpense(testExpense)).called(1);
      });

      test('handles database error and throws BusinessException', () async {
        // Prepare a test expense
        final testExpense = Expense(
          expenseId: 0,
          transactionId: const Uuid().v4(),
          vendorId: 'V-TEST',
          expenseDate: DateTime(2023, 1, 1),
          amount: 99.99,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        // Mock setup
        final error = Exception('Database connection failed');
        when(mockDatabaseService.createExpense(testExpense)).thenThrow(error);
        when(
          mockErrorHandler.handleError(error),
        ).thenReturn('Failed to create expense');

        // Act & Assert
        expect(
          () => expenseService.createExpense(testExpense),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to create expense',
            ),
          ),
        );
        verify(mockDatabaseService.createExpense(testExpense)).called(1);
        verify(mockErrorHandler.handleError(error)).called(1);
      });

      test('validates required fields before creation', () async {
        // Prepare an invalid expense (missing required fields)
        final invalidExpense = Expense(
          expenseId: 0,
          transactionId: '', // Invalid empty transaction ID
          vendorId: '', // Invalid empty vendor ID
          expenseDate: DateTime(2023, 1, 1),
          amount: -100.0, // Invalid negative amount
          currencyCode: '', // Invalid empty currency code
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: -10.0, // Invalid negative tax amount
          companyId: 1,
        );

        // Act & Assert
        expect(
          () => expenseService.createExpense(invalidExpense),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Transaction ID is required',
            ),
          ),
        );
        verifyNever(mockDatabaseService.createExpense(any));
      });
    });

    group('updateExpense', () {
      test('updates expense successfully', () async {
        // Mock setup
        final originalExpense = Expense(
          expenseId: 1,
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        final updatedExpense = originalExpense.copyWith(
          vendorId: 'V001-UPDATED',
          expenseDate: DateTime(2023, 1, 2),
          amount: 150.0,
          taxAmount: 15.0,
        );

        when(
          mockDatabaseService.getExpenseById(1),
        ).thenAnswer((_) async => originalExpense);
        when(
          mockDatabaseService.updateExpense(updatedExpense),
        ).thenAnswer((_) async => updatedExpense);

        // Act
        final expense = await expenseService.updateExpense(updatedExpense);

        // Assert
        expect(expense.amount, 150.0);
        expect(expense.vendorId, 'V001-UPDATED');
        expect(expense.expenseDate, DateTime(2023, 1, 2));
        expect(expense.taxAmount, 15.0);
        verify(mockDatabaseService.getExpenseById(1)).called(1);
        verify(mockDatabaseService.updateExpense(updatedExpense)).called(1);
      });

      test('handles database error and throws BusinessException', () async {
        // Prepare a test expense
        final testExpense = Expense(
          expenseId: 1,
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        // Mock setup
        when(
          mockDatabaseService.getExpenseById(1),
        ).thenAnswer((_) async => testExpense);
        final error = Exception('Database connection failed');
        when(mockDatabaseService.updateExpense(testExpense)).thenThrow(error);
        when(
          mockErrorHandler.handleError(error),
        ).thenReturn('Failed to update expense');

        // Act & Assert
        await expectLater(
          () => expenseService.updateExpense(testExpense),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update expense',
            ),
          ),
        );
        verifyInOrder([
          mockDatabaseService.getExpenseById(1),
          mockDatabaseService.updateExpense(testExpense),
        ]);
        verify(mockErrorHandler.handleError(error)).called(1);
      });

      test('validates expense exists before update', () async {
        // Prepare a test expense with non-existent ID
        final testExpense = Expense(
          expenseId: 999, // Non-existent ID
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        // Mock setup
        when(
          mockDatabaseService.getExpenseById(999),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => expenseService.updateExpense(testExpense),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Expense not found',
            ),
          ),
        );
        verify(mockDatabaseService.getExpenseById(999)).called(1);
        verifyNever(mockDatabaseService.updateExpense(any));
      });
    });

    group('deleteExpense', () {
      test('deletes expense successfully', () async {
        // Mock setup
        final testExpense = Expense(
          expenseId: 1,
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        when(
          mockDatabaseService.getExpenseById(1),
        ).thenAnswer((_) async => testExpense);
        when(
          mockDatabaseService.deleteExpense(1),
        ).thenAnswer((_) async => true);

        // Act
        await expenseService.deleteExpense(1);

        // Assert
        verify(mockDatabaseService.getExpenseById(1)).called(1);
        verify(mockDatabaseService.deleteExpense(1)).called(1);
      });

      test('handles database error and throws BusinessException', () async {
        // Mock setup
        final testExpense = Expense(
          expenseId: 1,
          transactionId: const Uuid().v4(),
          vendorId: 'V001',
          expenseDate: DateTime(2023, 1, 1),
          amount: 100.0,
          currencyCode: 'USD',
          paymentMethod: PaymentMethod.cash,
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        );

        when(
          mockDatabaseService.getExpenseById(1),
        ).thenAnswer((_) async => testExpense);
        final error = Exception('Database connection failed');
        when(mockDatabaseService.deleteExpense(1)).thenThrow(error);
        when(
          mockErrorHandler.handleError(error),
        ).thenReturn('Failed to delete expense');

        // Act & Assert
        await expectLater(
          () => expenseService.deleteExpense(1),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to delete expense',
            ),
          ),
        );
        verifyInOrder([
          mockDatabaseService.getExpenseById(1),
          mockDatabaseService.deleteExpense(1),
        ]);
        verify(mockErrorHandler.handleError(error)).called(1);
      });

      test('validates expense exists before deletion', () async {
        // Mock setup
        when(
          mockDatabaseService.getExpenseById(999),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => expenseService.deleteExpense(999),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Expense not found',
            ),
          ),
        );
        verify(mockDatabaseService.getExpenseById(999)).called(1);
        verifyNever(mockDatabaseService.deleteExpense(any));
      });
    });
  });
}
