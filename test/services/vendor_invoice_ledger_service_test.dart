import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/vendor_invoice_ledger_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';

import 'vendor_invoice_ledger_service_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late VendorInvoiceLedgerService service;
  const uuid = Uuid();

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    service = VendorInvoiceLedgerService(mockDatabaseService, mockErrorHandler);
    when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');
  });

  group('VendorInvoiceLedgerService', () {
    final testDate = DateTime(2023, 5, 15);
    final vendorInvoice = VendorInvoice(
      invoiceId: 1,
      vendorId: 'VENDOR001',
      invoiceNumber: 'INV-2023-001',
      invoiceDate: testDate,
      dueDate: testDate.add(const Duration(days: 30)),
      amount: 1000.0,
      currencyCode: 'USD',
      expenseAccountNumber: '6000', // Office supplies expense
      taxAmount: 100.0,
      companyId: 1,
      isPaid: false,
    );

    // Test invoice with no tax
    final noTaxInvoice = VendorInvoice(
      invoiceId: 2,
      vendorId: 'VENDOR002',
      invoiceNumber: 'INV-2023-002',
      invoiceDate: testDate,
      dueDate: testDate.add(const Duration(days: 30)),
      amount: 500.0,
      currencyCode: 'USD',
      expenseAccountNumber: '6000', // Office supplies expense
      companyId: 1,
      isPaid: false,
    );

    test(
      'createGeneralLedgerEntriesForVendorInvoice creates proper debit and credit entries',
      () async {
        // Arrange
        final transactionId = uuid.v4();
        final debitEntry = GeneralLedger(
          ledgerId: 1001,
          transactionId: transactionId,
          transactionDate: vendorInvoice.invoiceDate,
          accountNumber: vendorInvoice.expenseAccountNumber,
          description: 'Invoice ${vendorInvoice.invoiceNumber}',
          debit: vendorInvoice.amount,
          credit: 0,
          currencyCode: vendorInvoice.currencyCode,
          projectId: vendorInvoice.projectId ?? 0,
          staffId: vendorInvoice.staffId ?? 0,
          taxAmount: vendorInvoice.taxAmount,
          companyId: vendorInvoice.companyId ?? 1,
        );

        final creditEntry = GeneralLedger(
          ledgerId: 1002,
          transactionId: transactionId,
          transactionDate: vendorInvoice.invoiceDate,
          accountNumber: '2100', // Accounts Payable
          description: 'Invoice ${vendorInvoice.invoiceNumber}',
          debit: 0,
          credit: vendorInvoice.amount,
          currencyCode: vendorInvoice.currencyCode,
          companyId: vendorInvoice.companyId ?? 1,
          taxAmount: 0,
          projectId: 0,
          staffId: 0,
        );

        when(mockDatabaseService.createGeneralLedgerEntry(any)).thenAnswer((
          invocation,
        ) async {
          final entry = invocation.positionalArguments[0] as GeneralLedger;
          if (entry.debit > 0) {
            return debitEntry;
          } else {
            return creditEntry;
          }
        });

        // Act
        final result = await service.createGeneralLedgerEntriesForVendorInvoice(
          vendorInvoice,
        );

        // Assert
        expect(result.length, 2);

        // Verify debit entry (expense)
        final resultDebitEntry = result[0];
        expect(
          resultDebitEntry.accountNumber,
          vendorInvoice.expenseAccountNumber,
        );
        expect(resultDebitEntry.debit, vendorInvoice.amount);
        expect(resultDebitEntry.credit, 0);
        expect(resultDebitEntry.taxAmount, vendorInvoice.taxAmount);

        // Verify credit entry (accounts payable)
        final resultCreditEntry = result[1];
        expect(
          resultCreditEntry.accountNumber,
          '2100',
        ); // Accounts Payable account
        expect(resultCreditEntry.debit, 0);
        expect(resultCreditEntry.credit, vendorInvoice.amount);

        // Verify both entries share common data
        for (final entry in result) {
          expect(entry.transactionDate, vendorInvoice.invoiceDate);
          expect(entry.description, 'Invoice ${vendorInvoice.invoiceNumber}');
          expect(entry.currencyCode, vendorInvoice.currencyCode);
          expect(entry.companyId, vendorInvoice.companyId ?? 1);
        }

        // Verify database calls
        verify(mockDatabaseService.createGeneralLedgerEntry(any)).called(2);
      },
    );

    test(
      'createGeneralLedgerEntriesForVendorInvoice handles invoice with no tax',
      () async {
        // Arrange
        final transactionId = uuid.v4();
        final debitEntry = GeneralLedger(
          ledgerId: 2001,
          transactionId: transactionId,
          transactionDate: noTaxInvoice.invoiceDate,
          accountNumber: noTaxInvoice.expenseAccountNumber,
          description: 'Invoice ${noTaxInvoice.invoiceNumber}',
          debit: noTaxInvoice.amount,
          credit: 0,
          currencyCode: noTaxInvoice.currencyCode,
          companyId: noTaxInvoice.companyId ?? 1,
          taxAmount: 0,
          projectId: 0,
          staffId: 0,
        );

        final creditEntry = GeneralLedger(
          ledgerId: 2002,
          transactionId: transactionId,
          transactionDate: noTaxInvoice.invoiceDate,
          accountNumber: '2100', // Accounts Payable
          description: 'Invoice ${noTaxInvoice.invoiceNumber}',
          debit: 0,
          credit: noTaxInvoice.amount,
          currencyCode: noTaxInvoice.currencyCode,
          companyId: noTaxInvoice.companyId ?? 1,
          taxAmount: 0,
          projectId: 0,
          staffId: 0,
        );

        when(mockDatabaseService.createGeneralLedgerEntry(any)).thenAnswer((
          invocation,
        ) async {
          final entry = invocation.positionalArguments[0] as GeneralLedger;
          if (entry.debit > 0) {
            return debitEntry;
          } else {
            return creditEntry;
          }
        });

        // Act
        final result = await service.createGeneralLedgerEntriesForVendorInvoice(
          noTaxInvoice,
        );

        // Assert
        expect(result.length, 2);

        // Verify debit entry
        final resultDebitEntry = result[0];
        expect(resultDebitEntry.debit, noTaxInvoice.amount);
        expect(resultDebitEntry.taxAmount, 0);

        // Verify credit entry
        final resultCreditEntry = result[1];
        expect(resultCreditEntry.credit, noTaxInvoice.amount);

        verify(mockDatabaseService.createGeneralLedgerEntry(any)).called(2);
      },
    );

    test(
      'createGeneralLedgerEntriesForVendorInvoice handles database errors',
      () async {
        // Arrange
        when(
          mockDatabaseService.createGeneralLedgerEntry(any),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to create ledger entries');

        // Act & Assert
        expect(
          () =>
              service.createGeneralLedgerEntriesForVendorInvoice(vendorInvoice),
          throwsA(isA<BusinessException>()),
        );
      },
    );
  });
}
