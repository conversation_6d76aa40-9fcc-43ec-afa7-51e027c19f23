import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/services/logger.dart';
import '../mocks/mock_supabase.dart';

class MockSupabaseDatabaseService extends SupabaseDatabaseService {
  MockSupabaseDatabaseService(super.clientProvider, super.errorHandler)
    : super(useMockData: true);

  final Map<int, Company> _mockCompanies = {};

  void resetMockData() {
    _mockCompanies[1] = const Company(
      companyId: 1,
      companyName: 'Test Company',
      organizationNumber: '*********',
      phone: '************',
      email: '<EMAIL>',
      address: '123 Test St',
      zipCode: '12345',
      city: 'Test City',
      country: 'Test Country',
    );

    _mockCompanies[2] = const Company(
      companyId: 2,
      companyName: 'Another Company',
      organizationNumber: '*********',
      phone: '************',
      email: '<EMAIL>',
      address: '456 Test Ave',
      zipCode: '12345',
      city: 'Test City',
      country: 'Test Country',
    );
  }

  @override
  Future<List<Company>> getCompanies() async {
    return _mockCompanies.values.toList();
  }

  @override
  Future<Company?> getCompanyById(int id) async {
    return _mockCompanies[id];
  }

  @override
  Future<Company> createCompany(Company company) async {
    final newId =
        _mockCompanies.isEmpty
            ? 1
            : _mockCompanies.keys.reduce((a, b) => a > b ? a : b) + 1;
    final newCompany = company.copyWith(companyId: newId);
    _mockCompanies[newId] = newCompany;
    return newCompany;
  }

  @override
  Future<Company> updateCompany(Company company) async {
    if (!_mockCompanies.containsKey(company.companyId)) {
      throw Exception('Company not found');
    }
    _mockCompanies[company.companyId] = company;
    return company;
  }

  @override
  Future<void> deleteCompany(int id) async {
    if (!_mockCompanies.containsKey(id)) {
      throw Exception('Company not found');
    }
    _mockCompanies.remove(id);
  }
}

void main() {
  group('SupabaseDatabaseService Company Operations', () {
    late MockSupabaseDatabaseService service;
    late ErrorHandler errorHandler;
    late Logger logger;

    setUp(() {
      logger = Logger(print: debugPrint, debugMode: true);
      errorHandler = ErrorHandler(logger);
      service = MockSupabaseDatabaseService(
        MockSupabaseClientProvider(),
        errorHandler,
      );
      service.resetMockData();
    });

    group('getCompanies', () {
      test('returns all companies', () async {
        final companies = await service.getCompanies();
        expect(companies.length, 2);
        expect(companies.first.companyName, 'Test Company');
        expect(companies.last.companyName, 'Another Company');
      });
    });

    group('getCompanyById', () {
      test('returns company when id exists', () async {
        final company = await service.getCompanyById(1);
        expect(company, isNotNull);
        expect(company!.companyName, 'Test Company');
      });

      test('returns null when id does not exist', () async {
        final company = await service.getCompanyById(999);
        expect(company, isNull);
      });
    });

    group('createCompany', () {
      test('creates a new company', () async {
        const newCompany = Company(
          companyId: 0, // Will be assigned by the service
          companyName: 'New Company',
          organizationNumber: '*********',
          phone: '************',
          email: '<EMAIL>',
          address: '789 Test Blvd',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
        );

        final created = await service.createCompany(newCompany);
        expect(created.companyId, isPositive);
        expect(created.companyName, 'New Company');

        final companies = await service.getCompanies();
        expect(companies.length, 3);
      });
    });

    group('updateCompany', () {
      test('updates an existing company', () async {
        final company = await service.getCompanyById(1);
        expect(company, isNotNull);

        final updated = company!.copyWith(companyName: 'Updated Company');
        final result = await service.updateCompany(updated);
        expect(result.companyName, 'Updated Company');

        final fetched = await service.getCompanyById(1);
        expect(fetched!.companyName, 'Updated Company');
      });

      test('throws exception when updating non-existent company', () async {
        const nonExistent = Company(
          companyId: 999,
          companyName: 'Non Existent',
          organizationNumber: '*********',
          phone: '************',
          email: '<EMAIL>',
          address: '999 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
        );

        expect(() => service.updateCompany(nonExistent), throwsException);
      });
    });

    group('deleteCompany', () {
      test('deletes an existing company', () async {
        await service.deleteCompany(1);
        final companies = await service.getCompanies();
        expect(companies.length, 1);
        expect(companies.first.companyId, 2);
      });

      test('throws exception when deleting non-existent company', () async {
        expect(() => service.deleteCompany(999), throwsException);
      });
    });
  });
}
