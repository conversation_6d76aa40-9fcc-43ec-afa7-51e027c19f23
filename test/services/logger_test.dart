import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/services/logger.dart';

/// A test logger that captures output instead of printing it
class TestLogger extends Logger {
  final List<String> logOutput = [];
  bool _isDebugMode = true;

  void setDebugMode(bool value) {
    _isDebugMode = value;
  }

  @override
  void info(String message) {
    if (_isDebugMode) {
      logOutput.add('ℹ️ INFO: $message');
    }
  }

  @override
  void warning(String message) {
    if (_isDebugMode) {
      logOutput.add('⚠️ WARNING: $message');
    }
  }

  @override
  void error(String message, [dynamic exception, StackTrace? stackTrace]) {
    if (_isDebugMode) {
      logOutput.add('❌ ERROR: $message');
      if (exception != null) {
        logOutput.add('Exception: $exception');
      }
      if (stackTrace != null) {
        logOutput.add('Stack trace: $stackTrace');
      }
    }
  }

  @override
  void debug(String message) {
    if (_isDebugMode) {
      logOutput.add('🔍 DEBUG: $message');
    }
  }
}

void main() {
  late Logger logger;
  late StringBuffer output;
  late PrintFunction mockPrint;

  setUp(() {
    output = StringBuffer();
    mockPrint = (String message) => output.write('$message\n');
  });

  group('Logger in debug mode', () {
    setUp(() {
      logger = Logger(print: mockPrint, debugMode: true);
    });

    test('info prints message', () {
      logger.info('Test info message');
      expect(output.toString(), 'ℹ️ INFO: Test info message\n');
    });

    test('warning prints message', () {
      logger.warning('Test warning message');
      expect(output.toString(), '⚠️ WARNING: Test warning message\n');
    });

    test('error prints message and exception', () {
      final exception = Exception('Test exception');
      final stackTrace = StackTrace.current;
      logger.error('Test error message', exception, stackTrace);
      expect(output.toString(), contains('❌ ERROR: Test error message\n'));
      expect(output.toString(), contains('Exception: $exception\n'));
      expect(output.toString(), contains('Stack trace: $stackTrace\n'));
    });

    test('debug prints message', () {
      logger.debug('Test debug message');
      expect(output.toString(), '🔍 DEBUG: Test debug message\n');
    });
  });

  group('Logger in release mode', () {
    setUp(() {
      logger = Logger(print: mockPrint, debugMode: false);
    });

    test('info does not print in release mode', () {
      logger.info('Test info message');
      expect(output.toString(), isEmpty);
    });

    test('warning does not print in release mode', () {
      logger.warning('Test warning message');
      expect(output.toString(), isEmpty);
    });

    test('error does not print in release mode', () {
      final exception = Exception('Test exception');
      final stackTrace = StackTrace.current;
      logger.error('Test error message', exception, stackTrace);
      expect(output.toString(), isEmpty);
    });

    test('debug does not print in release mode', () {
      logger.debug('Test debug message');
      expect(output.toString(), isEmpty);
    });
  });
}
