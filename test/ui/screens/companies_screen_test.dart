import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/ui/features/companies/companies_screen.dart';
import 'package:we_like_money/utils/test_data.dart';
import 'package:mockito/mockito.dart';
import '../../utils/test_helper.dart';

void main() {
  setUp(() {
    // Arrange: Initialize mock objects before each test
    TestHelper.setupMocks();
  });

  testWidgets('CompaniesScreen shows loading indicator', (tester) async {
    // Arrange: Configure mock to return a delayed future to ensure loading state is visible
    when(TestHelper.mockCompanyViewModel.getCompanies()).thenAnswer(
      (_) => Future.delayed(const Duration(milliseconds: 500), () => []),
    );

    // Act: Build the widget and trigger initial load
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );

    // Pump once to start the build process
    await tester.pump();

    // Assert: Verify loading indicator is visible
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // Clean up: Wait for any pending animations or futures
    await tester.pumpAndSettle();
  });

  testWidgets('CompaniesScreen shows companies list', (tester) async {
    // Arrange: Configure mock to return test companies and set up widget
    await TestHelper.setupCompanyMocks(
      companies: TestData.getTestCompanies(),
      shouldThrow: false,
    );

    // Act: Build the widget and wait for data to load
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );
    await tester.pump();

    // Assert: Verify both test companies are displayed in the list
    expect(find.text(TestData.testCompany.companyName), findsOneWidget);
    expect(find.text(TestData.testCompany2.companyName), findsOneWidget);
  });

  testWidgets('CompaniesScreen shows error message', (tester) async {
    // Arrange: Configure mock to throw error and set up widget
    await TestHelper.setupCompanyMocks(
      shouldThrow: true,
      errorMessage: 'Test error',
    );

    // Act: Build the widget and wait for error state to propagate
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );
    await tester.pumpAndSettle();

    // Assert: Verify error message is displayed to user
    expect(find.text('Error loading companies: Test error'), findsOneWidget);
  });

  testWidgets('CompaniesScreen handles empty list', (tester) async {
    // Arrange: Configure mock to return empty list and set up widget
    await TestHelper.setupCompanyMocks(companies: [], shouldThrow: false);

    // Act: Build the widget and wait for data to load
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );
    await tester.pump();

    // Assert: Verify empty state message is displayed
    expect(
      find.text('No companies found. Add one to get started.'),
      findsOneWidget,
    );
  });

  testWidgets('CompaniesScreen shows add company button', (tester) async {
    // Arrange: Configure mock to return empty list and set up widget
    await TestHelper.setupCompanyMocks(companies: [], shouldThrow: false);

    // Act: Build the widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );

    // Assert: Verify floating action button is present
    expect(find.byIcon(Icons.add), findsOneWidget);
  });

  testWidgets('CompaniesScreen shows company details', (tester) async {
    // Arrange: Configure mock to return single test company and set up widget
    await TestHelper.setupCompanyMocks(
      singleCompany: TestData.testCompany,
      shouldThrow: false,
    );

    // Act: Build the widget and wait for data to load
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          companyViewModelProvider.overrideWithValue(
            TestHelper.mockCompanyViewModel,
          ),
        ],
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );
    await tester.pump();

    // Assert: Verify company name and ID are displayed
    expect(find.text(TestData.testCompany.companyName), findsOneWidget);
    expect(find.text('ID: ${TestData.testCompany.companyId}'), findsOneWidget);
  });

  testWidgets('CompaniesScreen shows add company dialog when FAB is tapped', (
    tester,
  ) async {
    // Arrange: Configure mock behavior and set up test container
    await TestHelper.setupCompanyMocks(
      singleCompany: TestData.testCompany,
      shouldThrow: false,
    );
    when(
      TestHelper.mockCompanyViewModel.createCompany(any),
    ).thenAnswer((_) async => TestData.testCompany2);

    final container = TestHelper.createTestContainer(
      overrides: [
        companiesProvider.overrideWith(
          (ref) => Future.value([TestData.testCompany]),
        ),
      ],
    );

    // Act: Build widget, wait for data, and trigger dialog
    await tester.pumpWidget(
      UncontrolledProviderScope(
        container: container,
        child: const MaterialApp(home: CompaniesScreen()),
      ),
    );
    await tester.pump();
    await tester.tap(find.byType(FloatingActionButton));
    await tester.pumpAndSettle();

    // Assert: Verify dialog UI elements are present
    expect(find.text('Add Company'), findsOneWidget);
    expect(find.byType(TextField), findsOneWidget);
    expect(find.text('Cancel'), findsOneWidget);
    expect(find.text('Add'), findsOneWidget);
  });

  testWidgets(
    'CompaniesScreen shows edit company dialog when edit icon is tapped',
    (tester) async {
      // Arrange: Configure mock behavior and set up test container
      await TestHelper.setupCompanyMocks(
        singleCompany: TestData.testCompany,
        shouldThrow: false,
      );
      when(TestHelper.mockCompanyViewModel.updateCompany(any)).thenAnswer(
        (_) async =>
            TestData.testCompany.copyWith(companyName: 'Updated Company'),
      );

      final container = TestHelper.createTestContainer(
        overrides: [
          companiesProvider.overrideWith(
            (ref) => Future.value([TestData.testCompany]),
          ),
        ],
      );

      // Act: Build widget, wait for data, and trigger dialog
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const MaterialApp(home: CompaniesScreen()),
        ),
      );
      await tester.pump();
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();

      // Assert: Verify dialog UI elements are present
      expect(find.text('Edit Company'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Update'), findsOneWidget);
    },
  );

  testWidgets(
    'CompaniesScreen shows delete company dialog when delete icon is tapped',
    (tester) async {
      // Arrange: Configure mock behavior and set up test container
      await TestHelper.setupCompanyMocks(
        singleCompany: TestData.testCompany,
        shouldThrow: false,
      );
      when(
        TestHelper.mockCompanyViewModel.deleteCompany(any),
      ).thenAnswer((_) async => {});

      final container = TestHelper.createTestContainer(
        overrides: [
          companiesProvider.overrideWith(
            (ref) => Future.value([TestData.testCompany]),
          ),
        ],
      );

      // Act: Build widget, wait for data, and trigger dialog
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const MaterialApp(home: CompaniesScreen()),
        ),
      );
      await tester.pump();
      await tester.tap(find.byIcon(Icons.delete));
      await tester.pumpAndSettle();

      // Assert: Verify dialog UI elements and confirmation message are present
      expect(find.text('Delete Company'), findsOneWidget);
      expect(
        find.text(
          'Are you sure you want to delete ${TestData.testCompany.companyName}? This action cannot be undone.',
        ),
        findsOneWidget,
      );
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Delete'), findsOneWidget);
    },
  );
}
