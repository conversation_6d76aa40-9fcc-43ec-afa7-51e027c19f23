// Mocks generated by Mockito 5.4.5 from annotations
// in we_like_money/test/ui/widgets/company_selector_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/models/company.dart' as _i2;
import 'package:we_like_money/viewmodels/company_viewmodel.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCompany_0 extends _i1.SmartFake implements _i2.Company {
  _FakeCompany_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CompanyViewModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockCompanyViewModel extends _i1.Mock implements _i3.CompanyViewModel {
  MockCompanyViewModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Company>> getCompanies() =>
      (super.noSuchMethod(
            Invocation.method(#getCompanies, []),
            returnValue: _i4.Future<List<_i2.Company>>.value(<_i2.Company>[]),
          )
          as _i4.Future<List<_i2.Company>>);

  @override
  _i4.Future<_i2.Company?> getCompanyById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getCompanyById, [id]),
            returnValue: _i4.Future<_i2.Company?>.value(),
          )
          as _i4.Future<_i2.Company?>);

  @override
  _i4.Future<_i2.Company> createCompany(_i2.Company? company) =>
      (super.noSuchMethod(
            Invocation.method(#createCompany, [company]),
            returnValue: _i4.Future<_i2.Company>.value(
              _FakeCompany_0(
                this,
                Invocation.method(#createCompany, [company]),
              ),
            ),
          )
          as _i4.Future<_i2.Company>);

  @override
  _i4.Future<_i2.Company> updateCompany(_i2.Company? company) =>
      (super.noSuchMethod(
            Invocation.method(#updateCompany, [company]),
            returnValue: _i4.Future<_i2.Company>.value(
              _FakeCompany_0(
                this,
                Invocation.method(#updateCompany, [company]),
              ),
            ),
          )
          as _i4.Future<_i2.Company>);

  @override
  _i4.Future<void> deleteCompany(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCompany, [id]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
