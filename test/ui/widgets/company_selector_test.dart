import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/ui/common/widgets/company_selector.dart';
import 'package:we_like_money/viewmodels/company_viewmodel.dart';
import '../../utils/test_data.dart';
import 'company_selector_test.mocks.dart';

@GenerateMocks([CompanyViewModel])
void main() {
  late MockCompanyViewModel mockViewModel;

  setUp(() {
    mockViewModel = MockCompanyViewModel();
  });

  testWidgets('CompanySelector shows loading indicator', (tester) async {
    // Arrange: Configure mock to return a delayed future to ensure loading state is visible
    when(mockViewModel.getCompanies()).thenAnswer(
      (_) => Future.delayed(const Duration(milliseconds: 500), () => []),
    );

    // Act: Build the widget
    await tester.pumpWidget(
      ProviderScope(
        overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
        child: const MaterialApp(home: Scaffold(body: CompanySelector())),
      ),
    );

    // Pump once to start the build process
    await tester.pump();

    // Verify loading indicator is shown
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // Complete the future and verify it transitions to the loaded state
    await tester.pumpAndSettle();
  });

  testWidgets('CompanySelector shows companies list', (tester) async {
    // Arrange
    final companies = TestData.getTestCompanies();
    when(mockViewModel.getCompanies()).thenAnswer((_) async => companies);

    // Act
    await tester.pumpWidget(
      ProviderScope(
        overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
        child: const MaterialApp(home: Scaffold(body: CompanySelector())),
      ),
    );

    // Wait for the initial frame to be rendered
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 50));

    // Verify dropdown is shown
    expect(find.byType(DropdownButtonFormField<int?>), findsOneWidget);

    // Verify initial text is shown in the dropdown button
    expect(
      find.descendant(
        of: find.byType(DropdownButtonFormField<int?>),
        matching: find.text('All Companies'),
      ),
      findsOneWidget,
    );

    // Tap the dropdown to show items
    await tester.tap(find.byType(DropdownButtonFormField<int?>));
    await tester.pumpAndSettle();

    // Verify all items are shown in the dropdown menu
    expect(
      find.text('All Companies'),
      findsNWidgets(2),
    ); // One in button, one in menu
    expect(find.text(TestData.testCompany.companyName), findsOneWidget);
    expect(find.text(TestData.testCompany2.companyName), findsOneWidget);
  });

  testWidgets('CompanySelector shows error message', (tester) async {
    // Arrange
    when(mockViewModel.getCompanies()).thenThrow('Test error');

    // Act
    await tester.pumpWidget(
      ProviderScope(
        overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
        child: const MaterialApp(home: Scaffold(body: CompanySelector())),
      ),
    );

    // Wait for the initial frame to be rendered
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 50));

    // Verify error message is shown
    expect(find.text('Error loading companies: Test error'), findsOneWidget);
  });

  testWidgets('CompanySelector handles empty list', (tester) async {
    // Arrange
    when(mockViewModel.getCompanies()).thenAnswer((_) async => []);

    // Act
    await tester.pumpWidget(
      ProviderScope(
        overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
        child: const MaterialApp(home: Scaffold(body: CompanySelector())),
      ),
    );

    // Wait for the initial frame to be rendered
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 50));

    // Verify dropdown is shown
    expect(find.byType(DropdownButtonFormField<int?>), findsOneWidget);

    // Verify initial text is shown in the dropdown button
    expect(
      find.descendant(
        of: find.byType(DropdownButtonFormField<int?>),
        matching: find.text('All Companies'),
      ),
      findsOneWidget,
    );

    // Tap the dropdown to show items
    await tester.tap(find.byType(DropdownButtonFormField<int?>));
    await tester.pumpAndSettle();

    // Verify only "All Companies" option is shown (one in button, one in menu)
    expect(find.text('All Companies'), findsNWidgets(2));
  });

  testWidgets('CompanySelector allows selection', (tester) async {
    // Arrange
    final companies = [TestData.testCompany];
    when(mockViewModel.getCompanies()).thenAnswer((_) async => companies);

    // Act
    await tester.pumpWidget(
      ProviderScope(
        overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
        child: const MaterialApp(home: Scaffold(body: CompanySelector())),
      ),
    );

    // Wait for the initial frame to be rendered
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 50));

    // Verify initial text is shown in the dropdown button
    expect(
      find.descendant(
        of: find.byType(DropdownButtonFormField<int?>),
        matching: find.text('All Companies'),
      ),
      findsOneWidget,
    );

    // Tap the dropdown to show items
    await tester.tap(find.byType(DropdownButtonFormField<int?>));
    await tester.pumpAndSettle();

    // Verify all items are shown
    expect(
      find.text('All Companies'),
      findsNWidgets(2),
    ); // One in button, one in menu
    expect(find.text(TestData.testCompany.companyName), findsOneWidget);

    // Select a company
    await tester.tap(find.text(TestData.testCompany.companyName).last);
    await tester.pumpAndSettle();

    // Verify selection was made
    expect(find.text(TestData.testCompany.companyName), findsOneWidget);
  });
}
