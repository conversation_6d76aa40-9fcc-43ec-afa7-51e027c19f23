import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/viewmodels/project_viewmodel.dart';

import 'project_provider_test.mocks.dart';

@GenerateMocks([ProjectViewModel])
void main() {
  late MockProjectViewModel mockViewModel;
  late ProviderContainer container;

  setUp(() {
    mockViewModel = MockProjectViewModel();

    // Override the projectViewModelProvider to return our mock
    container = ProviderContainer(
      overrides: [projectViewModelProvider.overrideWithValue(mockViewModel)],
    );

    // Add a listener to the container to prevent "not listened to" exceptions
    addTearDown(container.dispose);
  });

  group('projectsProvider', () {
    test('returns projects from the view model', () async {
      // Arrange
      final testProjects = [
        const Project(
          projectId: 1,
          projectCode: 'PROJ-1',
          projectName: 'Test Project 1',
        ),
        const Project(
          projectId: 2,
          projectCode: 'PROJ-2',
          projectName: 'Test Project 2',
        ),
      ];

      when(mockViewModel.getProjects()).thenAnswer((_) async => testProjects);

      // Act
      final result = await container.read(projectsProvider.future);

      // Assert
      expect(result, equals(testProjects));
      verify(mockViewModel.getProjects()).called(1);
    });

    test('propagates errors from the view model', () async {
      // Arrange
      when(mockViewModel.getProjects()).thenThrow(Exception('Test error'));

      // Act & Assert
      expect(
        () => container.read(projectsProvider.future),
        throwsA(isA<Exception>()),
      );
      verify(mockViewModel.getProjects()).called(1);
    });
  });

  group('projectByIdProvider', () {
    test('returns project by ID from the view model', () async {
      // Arrange
      const testProject = Project(
        projectId: 1,
        projectCode: 'PROJ-1',
        projectName: 'Test Project 1',
      );

      when(
        mockViewModel.getProjectById(1),
      ).thenAnswer((_) async => testProject);

      // Act
      final result = await container.read(projectByIdProvider(1).future);

      // Assert
      expect(result, equals(testProject));
      verify(mockViewModel.getProjectById(1)).called(1);
    });

    test('returns null when project is not found', () async {
      // Arrange
      when(mockViewModel.getProjectById(999)).thenAnswer((_) async => null);

      // Act
      final result = await container.read(projectByIdProvider(999).future);

      // Assert
      expect(result, isNull);
      verify(mockViewModel.getProjectById(999)).called(1);
    });

    test('propagates errors from the view model', () async {
      // Arrange
      when(mockViewModel.getProjectById(1)).thenThrow(Exception('Test error'));

      // Act & Assert
      expect(
        () => container.read(projectByIdProvider(1).future),
        throwsA(isA<Exception>()),
      );
      verify(mockViewModel.getProjectById(1)).called(1);
    });
  });

  group('projectCreationProvider', () {
    test('calls createProject on the view model', () async {
      // Arrange
      const newProject = Project(
        projectId: 0,
        projectCode: 'NEW-PROJ',
        projectName: 'New Project',
      );

      const createdProject = Project(
        projectId: 3,
        projectCode: 'NEW-PROJ',
        projectName: 'New Project',
      );

      when(
        mockViewModel.createProject(newProject),
      ).thenAnswer((_) async => createdProject);

      // Act
      final createProjectFunction = container.read(projectCreationProvider);
      final result = await createProjectFunction(newProject);

      // Assert
      expect(result, equals(createdProject));
      verify(mockViewModel.createProject(newProject)).called(1);
    });
  });

  group('projectUpdateProvider', () {
    test('calls updateProject on the view model', () async {
      // Arrange
      const project = Project(
        projectId: 1,
        projectCode: 'PROJ-1',
        projectName: 'Updated Project',
      );

      when(
        mockViewModel.updateProject(project),
      ).thenAnswer((_) async => project);

      // Act
      final updateProjectFunction = container.read(projectUpdateProvider);
      final result = await updateProjectFunction(project);

      // Assert
      expect(result, equals(project));
      verify(mockViewModel.updateProject(project)).called(1);
    });
  });

  group('projectDeletionProvider', () {
    test('calls deleteProject on the view model', () async {
      // Arrange
      when(
        mockViewModel.deleteProject(1),
      ).thenAnswer((_) async => Future<void>.value());

      // Act
      final deleteProjectFunction = container.read(projectDeletionProvider);
      await deleteProjectFunction(1);

      // Assert
      verify(mockViewModel.deleteProject(1)).called(1);
    });
  });
}
