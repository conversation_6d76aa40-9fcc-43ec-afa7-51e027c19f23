// Mocks generated by Mockito 5.4.5 from annotations
// in we_like_money/test/providers/project_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:we_like_money/models/project.dart' as _i2;
import 'package:we_like_money/viewmodels/project_viewmodel.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeProject_0 extends _i1.SmartFake implements _i2.Project {
  _FakeProject_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ProjectViewModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockProjectViewModel extends _i1.Mock implements _i3.ProjectViewModel {
  MockProjectViewModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Project>> getProjects() =>
      (super.noSuchMethod(
            Invocation.method(#getProjects, []),
            returnValue: _i4.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i4.Future<List<_i2.Project>>);

  @override
  _i4.Future<_i2.Project?> getProjectById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectById, [id]),
            returnValue: _i4.Future<_i2.Project?>.value(),
          )
          as _i4.Future<_i2.Project?>);

  @override
  _i4.Future<_i2.Project> createProject(_i2.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#createProject, [project]),
            returnValue: _i4.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#createProject, [project]),
              ),
            ),
          )
          as _i4.Future<_i2.Project>);

  @override
  _i4.Future<_i2.Project> updateProject(_i2.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#updateProject, [project]),
            returnValue: _i4.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#updateProject, [project]),
              ),
            ),
          )
          as _i4.Future<_i2.Project>);

  @override
  _i4.Future<void> deleteProject(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProject, [id]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
