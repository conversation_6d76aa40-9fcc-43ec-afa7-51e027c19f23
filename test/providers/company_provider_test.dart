import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/company_viewmodel.dart';

import 'company_provider_test.mocks.dart';

@GenerateMocks([CompanyViewModel])
void main() {
  late MockCompanyViewModel mockViewModel;
  late ProviderContainer container;

  setUp(() {
    mockViewModel = MockCompanyViewModel();

    // Override the companyViewModelProvider to return our mock
    container = ProviderContainer(
      overrides: [companyViewModelProvider.overrideWithValue(mockViewModel)],
    );

    // Add a listener to the container to prevent "not listened to" exceptions
    addTearDown(container.dispose);
  });

  group('companiesProvider', () {
    test('returns companies from the view model', () async {
      // Arrange
      final testCompanies = [
        const Company(
          companyId: 1,
          companyName: 'Test Company 1',
          organizationNumber: 'ORG-123',
          phone: '+**********',
          email: '<EMAIL>',
          address: 'Test Address 1',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
        ),
        const Company(
          companyId: 2,
          companyName: 'Test Company 2',
          organizationNumber: 'ORG-456',
          phone: '+**********',
          email: '<EMAIL>',
          address: 'Test Address 2',
          zipCode: '67890',
          city: 'Test City',
          country: 'Test Country',
        ),
      ];

      when(mockViewModel.getCompanies()).thenAnswer((_) async => testCompanies);

      // Act
      final result = await container.read(companiesProvider.future);

      // Assert
      expect(result, equals(testCompanies));
      verify(mockViewModel.getCompanies()).called(1);
    });

    test('propagates errors from the view model', () async {
      // Arrange
      when(mockViewModel.getCompanies()).thenThrow(Exception('Test error'));

      // Act & Assert
      expect(
        () => container.read(companiesProvider.future),
        throwsA(isA<Exception>()),
      );
      verify(mockViewModel.getCompanies()).called(1);
    });
  });

  group('selectedCompanyProvider', () {
    test('returns null when no company is selected', () async {
      // Act
      final result = await container.read(selectedCompanyProvider.future);

      // Assert
      expect(result, isNull);
      verifyZeroInteractions(mockViewModel);
    });

    test('returns company when a company is selected', () async {
      // Arrange
      const testCompany = Company(
        companyId: 1,
        companyName: 'Test Company',
        organizationNumber: 'ORG-123',
        phone: '+**********',
        email: '<EMAIL>',
        address: 'Test Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      when(
        mockViewModel.getCompanyById(1),
      ).thenAnswer((_) async => testCompany);

      // Set the selected company ID
      container.read(selectedCompanyIdProvider.notifier).state = 1;

      // Act
      final result = await container.read(selectedCompanyProvider.future);

      // Assert
      expect(result, equals(testCompany));
      verify(mockViewModel.getCompanyById(1)).called(1);
    });

    test('returns null when selected company is not found', () async {
      // Arrange
      when(mockViewModel.getCompanyById(999)).thenAnswer((_) async => null);

      // Set the selected company ID
      container.read(selectedCompanyIdProvider.notifier).state = 999;

      // Act
      final result = await container.read(selectedCompanyProvider.future);

      // Assert
      expect(result, isNull);
      verify(mockViewModel.getCompanyById(999)).called(1);
    });
  });

  group('companyCreationProvider', () {
    test('calls createCompany on the view model', () async {
      // Arrange
      const newCompany = Company(
        companyId: 2,
        companyName: 'New Company',
        organizationNumber: 'ORG-456',
        phone: '+**********',
        email: '<EMAIL>',
        address: 'New Address',
        zipCode: '67890',
        city: 'New City',
        country: 'New Country',
      );

      const createdCompany = Company(
        companyId: 3,
        companyName: 'New Company',
        organizationNumber: 'ORG-789',
        phone: '+**********',
        email: '<EMAIL>',
        address: 'New Address',
        zipCode: '12345',
        city: 'New City',
        country: 'New Country',
      );

      when(
        mockViewModel.createCompany(newCompany),
      ).thenAnswer((_) async => createdCompany);

      // Act
      final createCompanyFunction = container.read(companyCreationProvider);
      final result = await createCompanyFunction(newCompany);

      // Assert
      expect(result, equals(createdCompany));
      verify(mockViewModel.createCompany(newCompany)).called(1);
    });
  });

  group('companyUpdateProvider', () {
    test('calls updateCompany on the view model', () async {
      // Arrange
      const updatedCompany = Company(
        companyId: 1,
        companyName: 'Updated Company',
        organizationNumber: 'ORG-123',
        phone: '+**********',
        email: '<EMAIL>',
        address: 'Updated Address',
        zipCode: '12345',
        city: 'Test City',
        country: 'Test Country',
      );

      when(
        mockViewModel.updateCompany(updatedCompany),
      ).thenAnswer((_) async => updatedCompany);

      // Act
      final updateCompanyFunction = container.read(companyUpdateProvider);
      final result = await updateCompanyFunction(updatedCompany);

      // Assert
      expect(result, equals(updatedCompany));
      verify(mockViewModel.updateCompany(updatedCompany)).called(1);
    });
  });

  group('companyDeletionProvider', () {
    test('calls deleteCompany on the view model', () async {
      // Arrange
      when(
        mockViewModel.deleteCompany(1),
      ).thenAnswer((_) async => Future<void>.value());

      // Act
      final deleteCompanyFunction = container.read(companyDeletionProvider);
      await deleteCompanyFunction(1);

      // Assert
      verify(mockViewModel.deleteCompany(1)).called(1);
    });
  });
}
