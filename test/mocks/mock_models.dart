import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';

/// Mock expense model for testing
class MockExpense {
  final int expenseId;
  final String vendorId;
  final DateTime expenseDate;
  final double amount;
  final String currencyCode;
  final int? projectId;
  final int? staffId;
  final double? taxAmount;
  final int? companyId;
  final String? paymentMethod;
  final String? creditCardNumber;

  MockExpense({
    required this.expenseId,
    required this.vendorId,
    required this.expenseDate,
    required this.amount,
    required this.currencyCode,
    this.projectId,
    this.staffId,
    this.taxAmount,
    this.companyId,
    this.paymentMethod,
    this.creditCardNumber,
  });

  MockExpense copyWith({
    int? expenseId,
    String? vendorId,
    DateTime? expenseDate,
    double? amount,
    String? currencyCode,
    int? projectId,
    int? staffId,
    double? taxAmount,
    int? companyId,
    String? paymentMethod,
    String? creditCardNumber,
  }) {
    return MockExpense(
      expenseId: expenseId ?? this.expenseId,
      vendorId: vendorId ?? this.vendorId,
      expenseDate: expenseDate ?? this.expenseDate,
      amount: amount ?? this.amount,
      currencyCode: currencyCode ?? this.currencyCode,
      projectId: projectId ?? this.projectId,
      staffId: staffId ?? this.staffId,
      taxAmount: taxAmount ?? this.taxAmount,
      companyId: companyId ?? this.companyId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      creditCardNumber: creditCardNumber ?? this.creditCardNumber,
    );
  }

  /// Creates a mock expense from a real expense
  factory MockExpense.fromExpense(Expense expense) {
    return MockExpense(
      expenseId: expense.expenseId,
      vendorId: expense.vendorId,
      expenseDate: expense.expenseDate,
      amount: expense.amount,
      currencyCode: expense.currencyCode,
      projectId: expense.projectId,
      staffId: expense.staffId,
      taxAmount: expense.taxAmount,
      companyId: expense.companyId,
      paymentMethod: expense.paymentMethod.toString(),
      creditCardNumber: expense.creditCardNumber,
    );
  }
}

/// Mock general ledger model for testing
class MockGeneralLedger {
  final int ledgerId;
  final DateTime transactionDate;
  final String accountNumber;
  final String description;
  final double debit;
  final double credit;
  final String currencyCode;
  final int? projectId;
  final int? staffId;
  final double? taxAmount;
  final int? companyId;

  MockGeneralLedger({
    required this.ledgerId,
    required this.transactionDate,
    required this.accountNumber,
    required this.description,
    required this.debit,
    required this.credit,
    required this.currencyCode,
    this.projectId,
    this.staffId,
    this.taxAmount,
    this.companyId,
  });

  MockGeneralLedger copyWith({
    int? ledgerId,
    DateTime? transactionDate,
    String? accountNumber,
    String? description,
    double? debit,
    double? credit,
    String? currencyCode,
    int? projectId,
    int? staffId,
    double? taxAmount,
    int? companyId,
  }) {
    return MockGeneralLedger(
      ledgerId: ledgerId ?? this.ledgerId,
      transactionDate: transactionDate ?? this.transactionDate,
      accountNumber: accountNumber ?? this.accountNumber,
      description: description ?? this.description,
      debit: debit ?? this.debit,
      credit: credit ?? this.credit,
      currencyCode: currencyCode ?? this.currencyCode,
      projectId: projectId ?? this.projectId,
      staffId: staffId ?? this.staffId,
      taxAmount: taxAmount ?? this.taxAmount,
      companyId: companyId ?? this.companyId,
    );
  }

  /// Creates a mock general ledger from a real general ledger
  factory MockGeneralLedger.fromGeneralLedger(GeneralLedger ledger) {
    return MockGeneralLedger(
      ledgerId: ledger.ledgerId,
      transactionDate: ledger.transactionDate,
      accountNumber: ledger.accountNumber,
      description: ledger.description,
      debit: ledger.debit,
      credit: ledger.credit,
      currencyCode: ledger.currencyCode,
      projectId: ledger.projectId,
      staffId: ledger.staffId,
      taxAmount: ledger.taxAmount,
      companyId: ledger.companyId,
    );
  }
}
