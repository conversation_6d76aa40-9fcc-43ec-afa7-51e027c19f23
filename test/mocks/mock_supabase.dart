import 'dart:async';

import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:we_like_money/config/supabase_config.dart';

/// A more complete mock implementation of SupabaseClient
// ignore: must_be_immutable
class MockSupabaseClient extends Mock implements SupabaseClient {
  final bool _useMockData;

  MockSupabaseClient([this._useMockData = false]);

  @override
  SupabaseQueryBuilder from(String table) {
    if (!_useMockData) {
      throw Exception('Supabase client is not initialized');
    }
    return MockDataQueryBuilder(table);
  }
}

/// Mock implementation of SupabaseQueryBuilder
// ignore: must_be_immutable
class MockDataQueryBuilder extends Mock implements SupabaseQueryBuilder {
  String? _filterColumn;
  dynamic _filterValue;
  final String _table;
  static final Map<String, List<Map<String, dynamic>>> _mockData = {
    'vendor_invoice_line_items': [
      {
        'line_item_id': 1,
        'invoice_id': 1,
        'description': 'Test line item',
        'amount': 100.0,
        'tax_amount': 10.0,
        'account_number': '5000',
        'company_id': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      {
        'line_item_id': 2,
        'invoice_id': 1,
        'description': 'Another line item',
        'amount': 200.0,
        'tax_amount': 20.0,
        'account_number': '5000',
        'company_id': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
    ],
  };

  static void resetMockData() {
    _mockData['vendor_invoice_line_items'] = [
      {
        'line_item_id': 1,
        'invoice_id': 1,
        'description': 'Test line item',
        'amount': 100.0,
        'tax_amount': 10.0,
        'account_number': '5000',
        'company_id': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      {
        'line_item_id': 2,
        'invoice_id': 1,
        'description': 'Another line item',
        'amount': 200.0,
        'tax_amount': 20.0,
        'account_number': '5000',
        'company_id': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
    ];
  }

  MockDataQueryBuilder(this._table);

  PostgrestFilterBuilder<Map<String, dynamic>> single() {
    if (_filterColumn != null && _filterValue != null) {
      final mockData = _mockData[_table] ?? [];
      try {
        final filteredData = mockData.firstWhere(
          (item) => item[_filterColumn] == _filterValue,
          orElse: () => <String, dynamic>{},
        );
        return MockPostgrestFilterBuilder<Map<String, dynamic>>(filteredData);
      } catch (e) {
        return MockPostgrestFilterBuilder<Map<String, dynamic>>({});
      }
    }
    final mockData = _mockData[_table] ?? [];
    return MockPostgrestFilterBuilder<Map<String, dynamic>>(
      mockData.isEmpty ? {} : mockData.first,
    );
  }

  @override
  PostgrestFilterBuilder<List<Map<String, dynamic>>> select([
    String columns = '*',
  ]) {
    if (_filterColumn != null && _filterValue != null) {
      final mockData = _mockData[_table] ?? [];
      final filteredData =
          mockData
              .where((item) => item[_filterColumn] == _filterValue)
              .toList();
      return MockPostgrestFilterBuilder<List<Map<String, dynamic>>>(
        filteredData,
      );
    }
    return MockPostgrestFilterBuilder<List<Map<String, dynamic>>>(
      _mockData[_table] ?? [],
    );
  }

  @override
  PostgrestFilterBuilder<dynamic> insert(
    Object data, {
    bool defaultToNull = false,
  }) {
    if (data is Map<String, dynamic>) {
      final mockData = _mockData[_table] ?? [];
      final newId =
          mockData.isEmpty
              ? 1
              : mockData
                      .map((item) => item['line_item_id'] as int)
                      .reduce((a, b) => a > b ? a : b) +
                  1;
      final newData =
          Map<String, dynamic>.from(data)
            ..['line_item_id'] = newId
            ..['created_at'] = DateTime.now().toIso8601String()
            ..['updated_at'] = DateTime.now().toIso8601String();
      mockData.add(newData);
      _mockData[_table] = mockData;
      return MockPostgrestFilterBuilder<Map<String, dynamic>>(newData);
    }
    final mockData = _mockData[_table] ?? [];
    return MockPostgrestFilterBuilder<Map<String, dynamic>>(
      mockData.isEmpty ? {} : mockData.first,
    );
  }

  @override
  PostgrestFilterBuilder<dynamic> delete() {
    if (_filterColumn != null && _filterValue != null) {
      final mockData = _mockData[_table] ?? [];
      mockData.removeWhere((item) => item[_filterColumn] == _filterValue);
      _mockData[_table] = mockData;
    }
    return MockPostgrestFilterBuilder<Map<String, dynamic>>({});
  }

  @override
  PostgrestFilterBuilder<dynamic> update(Map<dynamic, dynamic> json) {
    if (_filterColumn != null && _filterValue != null) {
      final mockData = _mockData[_table] ?? [];
      final index = mockData.indexWhere(
        (item) => item[_filterColumn] == _filterValue,
      );
      if (index != -1) {
        final updatedData = Map<String, dynamic>.from(mockData[index]);
        json.forEach((key, value) {
          if (key is String) {
            updatedData[key] = value;
          }
        });
        updatedData['updated_at'] = DateTime.now().toIso8601String();
        mockData[index] = updatedData;
        _mockData[_table] = mockData;
        return MockPostgrestFilterBuilder<Map<String, dynamic>>(updatedData);
      }
    }
    final mockData = _mockData[_table] ?? [];
    return MockPostgrestFilterBuilder<Map<String, dynamic>>(
      mockData.isEmpty ? {} : mockData.first,
    );
  }

  MockDataQueryBuilder eq(String column, dynamic value) {
    _filterColumn = column;
    _filterValue = value;
    return this;
  }
}

/// Mock implementation of PostgrestFilterBuilder that also implements Future
// ignore: must_be_immutable
class MockPostgrestFilterBuilder<T> extends Mock
    implements PostgrestFilterBuilder<T>, Future<T> {
  final T _returnValue;

  MockPostgrestFilterBuilder(this._returnValue);

  Future<T> execute() => Future.value(_returnValue);

  @override
  MockPostgrestFilterBuilder<T> eq(String column, dynamic value) => this;

  @override
  MockPostgrestFilterBuilder<T> filter(
    String column,
    String operator,
    dynamic value,
  ) => this;

  // Direct Future implementation for then()
  @override
  Future<R> then<R>(
    FutureOr<R> Function(T value) onValue, {
    Function? onError,
  }) {
    try {
      final result = onValue(_returnValue);
      if (result is Future<R>) {
        return result;
      }
      return Future.value(result);
    } catch (e) {
      if (onError != null) {
        return Future.value(onError(e) as R);
      }
      return Future.error(e);
    }
  }

  // Stream implementation
  @override
  Stream<T> asStream() => Stream.fromFuture(Future.value(_returnValue));

  @override
  Future<T> catchError(Function onError, {bool Function(Object error)? test}) =>
      Future.value(_returnValue);

  @override
  Future<T> whenComplete(FutureOr Function() action) {
    action();
    return Future.value(_returnValue);
  }

  @override
  Future<T> timeout(Duration timeLimit, {FutureOr<T> Function()? onTimeout}) =>
      Future.value(_returnValue);
}

/// Mock implementation that properly implements SupabaseClientProvider
class MockSupabaseClientProvider implements SupabaseClientProvider {
  final SupabaseClient? _mockClient;
  final bool _useMockData;

  /// Supports both old style constructor with positional parameter and new style with named parameters
  MockSupabaseClientProvider({
    SupabaseClient? mockClient,
    bool useMockData = false,
  }) : _mockClient = mockClient,
       _useMockData = useMockData;

  /// Legacy constructor (kept for backward compatibility)
  factory MockSupabaseClientProvider.legacy(
    SupabaseClient? mockClient, {
    bool useMockData = false,
  }) {
    return MockSupabaseClientProvider(
      mockClient: mockClient,
      useMockData: useMockData,
    );
  }

  /// Returns a mock Supabase client, implementing the interface from SupabaseClientProvider
  @override
  SupabaseClient? getClient() {
    if (_useMockData) {
      return MockSupabaseClient(true);
    }
    return _mockClient;
  }
}

/// Provider that always returns the real Supabase client for online testing
class TestSupabaseClientProvider implements SupabaseClientProvider {
  final SupabaseClient _client;

  TestSupabaseClientProvider(this._client);

  @override
  SupabaseClient? getClient() {
    return _client;
  }
}
