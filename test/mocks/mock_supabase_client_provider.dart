import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:we_like_money/config/supabase_config.dart';

import 'mock_supabase.dart';

/// Mock implementation that properly implements SupabaseClientProvider
class MockSupabaseClientProvider implements SupabaseClientProvider {
  final SupabaseClient? _mockClient;
  final bool _useMockData;

  MockSupabaseClientProvider({
    SupabaseClient? mockClient,
    bool useMockData = false,
  }) : _mockClient = mockClient,
       _useMockData = useMockData;

  /// Returns a mock Supabase client, implementing the interface from SupabaseClientProvider
  @override
  SupabaseClient? getClient() {
    if (_useMockData) {
      return MockSupabaseClient(_useMockData);
    }
    return _mockClient;
  }
}
