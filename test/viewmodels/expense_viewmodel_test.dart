import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/expense_ledger_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/expense_viewmodel.dart';
import 'package:uuid/uuid.dart';

import 'expense_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler, ExpenseLedgerService])
void main() {
  late ExpenseViewModel viewModel;
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late MockExpenseLedgerService mockExpenseLedgerService;
  const uuid = Uuid();

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    mockExpenseLedgerService = MockExpenseLedgerService();
    viewModel = ExpenseViewModel(
      mockDatabaseService,
      mockErrorHandler,
      mockExpenseLedgerService,
    );
  });

  group('ExpenseViewModel', () {
    final testExpense = Expense(
      expenseId: 1,
      transactionId: uuid.v4(),
      vendorId: 'V001',
      expenseDate: DateTime(2023, 3, 15),
      amount: 150.00,
      currencyCode: 'USD',
      taxAmount: 15.0,
      companyId: 1,
      paymentMethod: PaymentMethod.creditCard,
      creditCardNumber: 'XXXX-XXXX-XXXX-1234',
    );

    test('getExpenses returns list of expenses', () async {
      // Arrange
      when(
        mockDatabaseService.getExpenses(),
      ).thenAnswer((_) => Future.value([testExpense]));

      // Act
      final result = await viewModel.getExpenses();

      // Assert
      expect(result, [testExpense]);
      verify(mockDatabaseService.getExpenses()).called(1);
    });

    test('getExpenses handles errors', () async {
      // Arrange
      when(
        mockDatabaseService.getExpenses(),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');

      // Act & Assert
      expect(() => viewModel.getExpenses(), throwsA(isA<BusinessException>()));
    });

    test('getExpenseById returns expense when found', () async {
      // Arrange
      when(
        mockDatabaseService.getExpenseById(1),
      ).thenAnswer((_) => Future.value(testExpense));

      // Act
      final result = await viewModel.getExpenseById(1);

      // Assert
      expect(result, testExpense);
      verify(mockDatabaseService.getExpenseById(1)).called(1);
    });

    test('getExpenseById returns null when not found', () async {
      // Arrange
      when(
        mockDatabaseService.getExpenseById(999),
      ).thenAnswer((_) => Future.value(null));

      // Act
      final result = await viewModel.getExpenseById(999);

      // Assert
      expect(result, null);
      verify(mockDatabaseService.getExpenseById(999)).called(1);
    });

    test('getExpenseById handles errors', () async {
      // Arrange
      when(
        mockDatabaseService.getExpenseById(any),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');

      // Act & Assert
      expect(
        () => viewModel.getExpenseById(1),
        throwsA(isA<BusinessException>()),
      );
    });

    test('createExpense creates and returns expense', () async {
      // Arrange
      final createdExpense = testExpense.copyWith(expenseId: 2);
      when(
        mockDatabaseService.createExpense(testExpense),
      ).thenAnswer((_) => Future.value(createdExpense));

      // Mock the expense ledger service
      when(
        mockExpenseLedgerService.createEntriesForExpense(createdExpense),
      ).thenAnswer(
        (_) => Future.value([
          GeneralLedger(
            ledgerId: 1,
            transactionId: createdExpense.transactionId,
            transactionDate: createdExpense.expenseDate,
            accountNumber: '5000',
            description: 'Expense: ${createdExpense.vendorId}',
            debit: 165.0,
            credit: 0,
            currencyCode: createdExpense.currencyCode,
            companyId: createdExpense.companyId,
          ),
          GeneralLedger(
            ledgerId: 2,
            transactionId: createdExpense.transactionId,
            transactionDate: createdExpense.expenseDate,
            accountNumber: '2000',
            description: 'Expense payment: ${createdExpense.vendorId}',
            debit: 0,
            credit: 165.0,
            currencyCode: createdExpense.currencyCode,
            companyId: createdExpense.companyId,
          ),
        ]),
      );

      // Act
      final result = await viewModel.createExpense(testExpense);

      // Assert
      expect(result.expenseId, 2);
      verify(mockDatabaseService.createExpense(testExpense)).called(1);
      verify(
        mockExpenseLedgerService.createEntriesForExpense(createdExpense),
      ).called(1);
    });

    test('createExpense handles errors', () async {
      // Arrange
      when(
        mockDatabaseService.createExpense(any),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');

      // Act & Assert
      expect(
        () => viewModel.createExpense(testExpense),
        throwsA(isA<BusinessException>()),
      );
    });

    test('updateExpense updates and returns expense', () async {
      // Arrange
      final updatedExpense = testExpense.copyWith(amount: 200.0);
      when(
        mockDatabaseService.updateExpense(updatedExpense),
      ).thenAnswer((_) => Future.value(updatedExpense));

      // Act
      final result = await viewModel.updateExpense(updatedExpense);

      // Assert
      expect(result.amount, 200.0);
      verify(mockDatabaseService.updateExpense(updatedExpense)).called(1);
    });

    test('updateExpense handles errors', () async {
      // Arrange
      when(
        mockDatabaseService.updateExpense(any),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');

      // Act & Assert
      expect(
        () => viewModel.updateExpense(testExpense),
        throwsA(isA<BusinessException>()),
      );
    });

    test('deleteExpense deletes expense', () async {
      // Arrange
      when(
        mockDatabaseService.deleteExpense(1),
      ).thenAnswer((_) => Future.value());

      // Act
      await viewModel.deleteExpense(1);

      // Assert
      verify(mockDatabaseService.deleteExpense(1)).called(1);
    });

    test('deleteExpense handles errors', () async {
      // Arrange
      when(
        mockDatabaseService.deleteExpense(any),
      ).thenThrow(Exception('Database error'));
      when(mockErrorHandler.handleError(any)).thenReturn('An error occurred');

      // Act & Assert
      expect(
        () => viewModel.deleteExpense(1),
        throwsA(isA<BusinessException>()),
      );
    });
  });
}
