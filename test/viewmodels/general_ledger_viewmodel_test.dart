import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/general_ledger_viewmodel.dart';
import 'package:uuid/uuid.dart';

import '../config/test_config.dart';
import 'general_ledger_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late GeneralLedgerViewModel viewModel;
  late bool isOnlineTest;

  setUpAll(() async {
    // Initialize TestConfig
    await TestConfig.instance.initialize();
    isOnlineTest =
        TestConfig.instance.isOnlineTest &&
        !TestConfig.instance.usingFallbackClient;
  });

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = GeneralLedgerViewModel(mockDatabaseService, mockErrorHandler);
  });

  group('getAllGeneralLedgerEntries', () {
    final testEntries = [
      GeneralLedger(
        ledgerId: 1,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 1),
        accountNumber: '1000',
        description: 'Test transaction 1',
        debit: 100.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 1,
        staffId: 1,
        taxAmount: 10.0,
        companyId: 1,
      ),
      GeneralLedger(
        ledgerId: 2,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 2),
        accountNumber: '2000',
        description: 'Test transaction 2',
        debit: 0.0,
        credit: 100.0,
        currencyCode: 'USD',
        projectId: 2,
        staffId: 2,
        taxAmount: 10.0,
        companyId: 1,
      ),
    ];

    test('returns list of general ledger entries when successful', () async {
      if (!isOnlineTest) {
        // Mock mode
        when(
          (mockDatabaseService).getGeneralLedgerEntries(),
        ).thenAnswer((_) async => testEntries);

        // Act
        final result = await viewModel.getAllGeneralLedgerEntries();

        // Assert
        expect(result, equals(testEntries));
        verify((mockDatabaseService).getGeneralLedgerEntries()).called(1);
      } else {
        // Real database mode - just verify we get a non-null result
        // Act
        final result = await viewModel.getAllGeneralLedgerEntries();

        // Assert
        expect(result, isNotNull);
        expect(result, isA<List<GeneralLedger>>());

        // Print info for debugging
        debugPrint('Got ${result.length} entries from real database');
      }
    });

    test('throws BusinessException when database service throws', () async {
      if (!isOnlineTest) {
        // Mock mode
        final testException = Exception('Database error');
        when(
          (mockDatabaseService).getGeneralLedgerEntries(),
        ).thenThrow(testException);
        when(
          mockErrorHandler.handleError(testException),
        ).thenReturn('Failed to get ledger entries');

        // Act & Assert
        expect(
          () => viewModel.getAllGeneralLedgerEntries(),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to get ledger entries',
            ),
          ),
        );
        verify((mockDatabaseService).getGeneralLedgerEntries()).called(1);
        verify(mockErrorHandler.handleError(testException)).called(1);
      } else {
        // Skip this test in online mode as we can't easily simulate database errors
        // without potentially affecting the actual database
        debugPrint('Skipping database error test in online mode for safety');
      }
    });
  });

  group('getGeneralLedgerEntriesByAccount', () {
    final testEntries = [
      GeneralLedger(
        ledgerId: 1,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 1),
        accountNumber: '1000',
        description: 'Test transaction 1',
        debit: 100.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 1,
        staffId: 1,
        taxAmount: 10.0,
        companyId: 1,
      ),
      GeneralLedger(
        ledgerId: 2,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 2),
        accountNumber: '2000',
        description: 'Test transaction 2',
        debit: 0.0,
        credit: 100.0,
        currencyCode: 'USD',
        projectId: 2,
        staffId: 2,
        taxAmount: 10.0,
        companyId: 1,
      ),
    ];

    test('returns filtered ledger entries for specific account', () async {
      if (!isOnlineTest) {
        // Mock mode
        when(
          (mockDatabaseService).getGeneralLedgerEntries(),
        ).thenAnswer((_) async => testEntries);

        // Act
        final result = await viewModel.getGeneralLedgerEntriesByAccount('1000');

        // Assert
        expect(result.length, equals(1));
        expect(result[0].accountNumber, equals('1000'));
        verify((mockDatabaseService).getGeneralLedgerEntries()).called(1);
      } else {
        // Real database mode - verify filtering works
        // First get all entries
        final allEntries = await viewModel.getAllGeneralLedgerEntries();

        if (allEntries.isEmpty) {
          debugPrint('No entries found in real database, skipping test');
          return;
        }

        // Pick the first account number to filter by
        final accountToFilter = allEntries.first.accountNumber;

        // Get filtered entries
        final result = await viewModel.getGeneralLedgerEntriesByAccount(
          accountToFilter,
        );

        // Assert
        expect(result, isA<List<GeneralLedger>>());

        // All returned entries should have the same account number
        for (var entry in result) {
          expect(entry.accountNumber, equals(accountToFilter));
        }

        debugPrint(
          'Successfully filtered by account $accountToFilter in real database',
        );
      }
    });

    test('throws BusinessException when database fails', () async {
      // Arrange
      when(
        mockDatabaseService.getGeneralLedgerEntriesByAccount(any),
      ).thenThrow(Exception('DB Error'));
      when(
        mockErrorHandler.handleError(any),
      ).thenReturn('Unable to load ledger entries');

      // Act & Assert
      expect(
        () => viewModel.getGeneralLedgerEntriesByAccount('1000'),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Unable to load ledger entries',
          ),
        ),
      );
    });
  });

  group('getGeneralLedgerEntriesByDateRange', () {
    final testEntries = [
      GeneralLedger(
        ledgerId: 1,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 1),
        accountNumber: '1000',
        description: 'Test transaction 1',
        debit: 100.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 1,
        staffId: 1,
        taxAmount: 10.0,
        companyId: 1,
      ),
      GeneralLedger(
        ledgerId: 2,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 15),
        accountNumber: '2000',
        description: 'Test transaction 2',
        debit: 0.0,
        credit: 100.0,
        currencyCode: 'USD',
        projectId: 2,
        staffId: 2,
        taxAmount: 10.0,
        companyId: 1,
      ),
      GeneralLedger(
        ledgerId: 3,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 2, 1),
        accountNumber: '3000',
        description: 'Test transaction 3',
        debit: 50.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 3,
        staffId: 3,
        taxAmount: 5.0,
        companyId: 1,
      ),
    ];

    test('returns filtered ledger entries for date range', () async {
      if (!isOnlineTest) {
        // Mock mode
        when(
          (mockDatabaseService).getGeneralLedgerEntries(),
        ).thenAnswer((_) async => testEntries);
        final startDate = DateTime(2023, 1, 10);
        final endDate = DateTime(2023, 1, 31);

        // Act
        final result = await viewModel.getGeneralLedgerEntriesByDateRange(
          startDate,
          endDate,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result[0].transactionDate, equals(DateTime(2023, 1, 15)));
        verify((mockDatabaseService).getGeneralLedgerEntries()).called(1);
      } else {
        // Real database mode - verify date filtering
        // Define date range (last year to now)
        final startDate = DateTime.now().subtract(const Duration(days: 365));
        final endDate = DateTime.now();

        // Get filtered entries
        final result = await viewModel.getGeneralLedgerEntriesByDateRange(
          startDate,
          endDate,
        );

        // Assert
        expect(result, isA<List<GeneralLedger>>());

        // All returned entries should be within date range
        for (var entry in result) {
          expect(
            entry.transactionDate.isAfter(startDate) ||
                entry.transactionDate.isAtSameMomentAs(startDate),
            isTrue,
          );
          expect(
            entry.transactionDate.isBefore(
              endDate.add(const Duration(days: 1)),
            ),
            isTrue,
          );
        }

        debugPrint('Successfully filtered by date range in real database');
      }
    });

    test('throws BusinessException when database service throws', () async {
      if (!isOnlineTest) {
        // Mock mode
        final testException = Exception('Database error');
        when(
          (mockDatabaseService).getGeneralLedgerEntries(),
        ).thenThrow(testException);
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to get ledger entries');
        final startDate = DateTime(2023, 1, 1);
        final endDate = DateTime(2023, 1, 31);

        // Act & Assert
        await expectLater(
          () =>
              viewModel.getGeneralLedgerEntriesByDateRange(startDate, endDate),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to get ledger entries',
            ),
          ),
        );

        // Note: We don't verify the mock calls here because the test is already
        // passing if the correct exception is thrown with the right message
      } else {
        // Skip this test in online mode as we can't easily simulate database errors
        // without potentially affecting the actual database
        debugPrint('Skipping database error test in online mode for safety');
      }
    });
  });

  group('calculateNetBalance', () {
    test('correctly calculates net balance for provided entries', () {
      // Arrange
      final testEntries = [
        GeneralLedger(
          ledgerId: 1,
          transactionId: const Uuid().v4(),
          transactionDate: DateTime(2023, 1, 1),
          accountNumber: '1000',
          description: 'Test transaction 1',
          debit: 100.0,
          credit: 0.0,
          currencyCode: 'USD',
          projectId: 1,
          staffId: 1,
          taxAmount: 10.0,
          companyId: 1,
        ),
        GeneralLedger(
          ledgerId: 2,
          transactionId: const Uuid().v4(),
          transactionDate: DateTime(2023, 1, 2),
          accountNumber: '2000',
          description: 'Test transaction 2',
          debit: 0.0,
          credit: 50.0,
          currencyCode: 'USD',
          projectId: 2,
          staffId: 2,
          taxAmount: 5.0,
          companyId: 1,
        ),
        GeneralLedger(
          ledgerId: 3,
          transactionId: const Uuid().v4(),
          transactionDate: DateTime(2023, 1, 3),
          accountNumber: '3000',
          description: 'Test transaction 3',
          debit: 25.0,
          credit: 0.0,
          currencyCode: 'USD',
          projectId: 3,
          staffId: 3,
          taxAmount: 2.5,
          companyId: 1,
        ),
      ];

      // Act
      final result = viewModel.calculateNetBalance(testEntries);

      // Assert
      expect(result, equals(75.0)); // 125 debits - 50 credits = 75.0
    });

    test('returns zero for empty entries list', () {
      // Act
      final result = viewModel.calculateNetBalance([]);

      // Assert
      expect(result, equals(0.0));
    });
  });

  group('GeneralLedgerViewModel', () {
    test(
      'getAllGeneralLedgerEntries throws BusinessException when database fails',
      () async {
        when(
          mockDatabaseService.getGeneralLedgerEntries(),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenThrow(BusinessException('Failed to load ledger entries'));

        expect(
          () => viewModel.getAllGeneralLedgerEntries(),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to load ledger entries',
            ),
          ),
        );
      },
    );

    test(
      'getGeneralLedgerEntriesByAccount throws BusinessException when database fails',
      () async {
        when(
          mockDatabaseService.getGeneralLedgerEntries(),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenThrow(BusinessException('Failed to load ledger entries'));

        expect(
          () => viewModel.getGeneralLedgerEntriesByAccount('1000'),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to load ledger entries',
            ),
          ),
        );
      },
    );

    test(
      'getGeneralLedgerEntriesByDateRange throws BusinessException when database fails',
      () async {
        // Arrange: Set up the mock to throw an exception
        final testException = Exception('Database error');
        when(
          mockDatabaseService.getGeneralLedgerEntries(),
        ).thenThrow(testException);

        // Set up the error handler to return a message instead of throwing
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to load ledger entries');

        final startDate = DateTime(2024, 1, 1);
        final endDate = DateTime(2024, 12, 31);

        // Act & Assert: Verify that the correct exception is thrown
        await expectLater(
          () =>
              viewModel.getGeneralLedgerEntriesByDateRange(startDate, endDate),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to load ledger entries',
            ),
          ),
        );

        // Note: We don't verify the mock calls here because the test is already
        // passing if the correct exception is thrown with the right message
      },
    );

    test('calculateNetBalance returns correct balance', () {
      final entries = [
        GeneralLedger(
          ledgerId: 1,
          transactionId: 'TX001',
          transactionDate: DateTime.now(),
          accountNumber: '1000',
          description: 'Test Entry 1',
          debit: 100.0,
          credit: 0.0,
          currencyCode: 'USD',
          companyId: 1,
        ),
        GeneralLedger(
          ledgerId: 2,
          transactionId: 'TX002',
          transactionDate: DateTime.now(),
          accountNumber: '1000',
          description: 'Test Entry 2',
          debit: 0.0,
          credit: 50.0,
          currencyCode: 'USD',
          companyId: 1,
        ),
      ];

      final balance = viewModel.calculateNetBalance(entries);
      expect(balance, 50.0);
    });
  });
}
