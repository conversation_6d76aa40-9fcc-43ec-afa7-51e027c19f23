import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/staff_member_viewmodel.dart';

import 'staff_member_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late StaffMemberViewModel viewModel;
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = StaffMemberViewModel(mockDatabaseService, mockErrorHandler);
  });

  group('StaffMemberViewModel', () {
    const testStaffMember = StaffMember(
      staffId: 1,
      staffName: '<PERSON>',
      email: '<EMAIL>',
      companyId: 1,
    );

    group('getStaffMembers', () {
      test('returns list of staff members when successful', () async {
        final staffMembers = [testStaffMember];
        when(
          mockDatabaseService.getStaffMembers(),
        ).thenAnswer((_) async => staffMembers);

        final result = await viewModel.getStaffMembers();

        expect(result, equals(staffMembers));
        verify(mockDatabaseService.getStaffMembers()).called(1);
      });

      test('throws BusinessException when database fails', () async {
        when(
          mockDatabaseService.getStaffMembers(),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to get staff members');

        expect(
          () => viewModel.getStaffMembers(),
          throwsA(isA<BusinessException>()),
        );
        verify(mockDatabaseService.getStaffMembers()).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('getStaffMemberById', () {
      test('returns staff member when found', () async {
        when(
          mockDatabaseService.getStaffMemberById(1),
        ).thenAnswer((_) async => testStaffMember);

        final result = await viewModel.getStaffMemberById(1);

        expect(result, equals(testStaffMember));
        verify(mockDatabaseService.getStaffMemberById(1)).called(1);
      });

      test('returns null when staff member not found', () async {
        when(
          mockDatabaseService.getStaffMemberById(999),
        ).thenAnswer((_) async => null);

        final result = await viewModel.getStaffMemberById(999);

        expect(result, isNull);
        verify(mockDatabaseService.getStaffMemberById(999)).called(1);
      });

      test('throws BusinessException when database fails', () async {
        when(
          mockDatabaseService.getStaffMemberById(1),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to get staff member');

        expect(
          () => viewModel.getStaffMemberById(1),
          throwsA(isA<BusinessException>()),
        );
        verify(mockDatabaseService.getStaffMemberById(1)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('createStaffMember', () {
      test('creates and returns staff member', () async {
        when(
          mockDatabaseService.createStaffMember(testStaffMember),
        ).thenAnswer((_) async => testStaffMember);

        final result = await viewModel.createStaffMember(testStaffMember);

        expect(result, equals(testStaffMember));
        verify(
          mockDatabaseService.createStaffMember(testStaffMember),
        ).called(1);
      });

      test('throws BusinessException when database fails', () async {
        when(
          mockDatabaseService.createStaffMember(testStaffMember),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to create staff member');

        expect(
          () => viewModel.createStaffMember(testStaffMember),
          throwsA(isA<BusinessException>()),
        );
        verify(
          mockDatabaseService.createStaffMember(testStaffMember),
        ).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('updateStaffMember', () {
      test('updates and returns staff member', () async {
        when(
          mockDatabaseService.updateStaffMember(testStaffMember),
        ).thenAnswer((_) async => testStaffMember);

        final result = await viewModel.updateStaffMember(testStaffMember);

        expect(result, equals(testStaffMember));
        verify(
          mockDatabaseService.updateStaffMember(testStaffMember),
        ).called(1);
      });

      test('throws BusinessException when database fails', () async {
        when(
          mockDatabaseService.updateStaffMember(testStaffMember),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to update staff member');

        expect(
          () => viewModel.updateStaffMember(testStaffMember),
          throwsA(isA<BusinessException>()),
        );
        verify(
          mockDatabaseService.updateStaffMember(testStaffMember),
        ).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('deleteStaffMember', () {
      test('deletes staff member successfully', () async {
        when(mockDatabaseService.deleteStaffMember(1)).thenAnswer((_) async {});

        await viewModel.deleteStaffMember(1);

        verify(mockDatabaseService.deleteStaffMember(1)).called(1);
      });

      test('throws BusinessException when database fails', () async {
        when(
          mockDatabaseService.deleteStaffMember(1),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to delete staff member');

        expect(
          () => viewModel.deleteStaffMember(1),
          throwsA(isA<BusinessException>()),
        );
        verify(mockDatabaseService.deleteStaffMember(1)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });
  });
}
