import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/vendor_viewmodel.dart';

import 'vendor_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late VendorViewModel viewModel;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = VendorViewModel(mockDatabaseService, mockErrorHandler);
  });

  group('VendorViewModel', () {
    group('getVendors', () {
      test('returns vendors when the call is successful', () async {
        // Arrange
        final testVendors = [
          const Vendor(
            vendorId: 'V001',
            vendorName: 'Test Vendor 1',
            vendorNumber: 'VN001',
            organizationNumber: 'ORG001',
            isActive: true,
            phone: '*********0',
            email: '<EMAIL>',
            address: '123 Test St',
            zipCode: '12345',
            city: 'Test City',
            country: 'Test Country',
            bankAccountType: 'checking',
            bankAccountNumber: '*********',
            contactPerson: 'John Doe',
            companyId: 1,
          ),
          const Vendor(
            vendorId: 'V002',
            vendorName: 'Test Vendor 2',
            vendorNumber: 'VN002',
            organizationNumber: 'ORG002',
            isActive: true,
            phone: '0*********',
            email: '<EMAIL>',
            address: '456 Test Ave',
            zipCode: '54321',
            city: 'Test Town',
            country: 'Test Country',
            bankAccountType: 'savings',
            bankAccountNumber: '*********',
            contactPerson: 'Jane Smith',
            companyId: 1,
          ),
        ];
        when(
          mockDatabaseService.getVendors(),
        ).thenAnswer((_) async => testVendors);

        // Act
        final result = await viewModel.getVendors();

        // Assert
        expect(result, equals(testVendors));
        verify(mockDatabaseService.getVendors()).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        when(
          mockDatabaseService.getVendors(),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to load vendors');

        // Act & Assert
        expect(
          () => viewModel.getVendors(),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to load vendors',
            ),
          ),
        );
        verify(mockDatabaseService.getVendors()).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('getVendorById', () {
      test('returns vendor when found', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Test Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.getVendorById('V001'),
        ).thenAnswer((_) async => vendor);

        // Act
        final result = await viewModel.getVendorById('V001');

        // Assert
        expect(result, equals(vendor));
        verify(mockDatabaseService.getVendorById('V001')).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('returns null when vendor not found', () async {
        // Arrange
        when(
          mockDatabaseService.getVendorById('NONEXISTENT'),
        ).thenAnswer((_) async => null);

        // Act
        final result = await viewModel.getVendorById('NONEXISTENT');

        // Assert
        expect(result, isNull);
        verify(mockDatabaseService.getVendorById('NONEXISTENT')).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        when(
          mockDatabaseService.getVendorById('V001'),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Database error occurred');

        // Act & Assert
        expect(
          () => viewModel.getVendorById('V001'),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Database error occurred',
            ),
          ),
        );
        verify(mockDatabaseService.getVendorById('V001')).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('getVendorByIdOrThrow', () {
      test('returns vendor when found', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Test Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.getVendorById('V001'),
        ).thenAnswer((_) async => vendor);

        // Act
        final result = await viewModel.getVendorByIdOrThrow('V001');

        // Assert
        expect(result, equals(vendor));
        verify(mockDatabaseService.getVendorById('V001')).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when vendor not found', () async {
        // Arrange
        when(
          mockDatabaseService.getVendorById('NONEXISTENT'),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => viewModel.getVendorByIdOrThrow('NONEXISTENT'),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Vendor not found',
            ),
          ),
        );
        verify(mockDatabaseService.getVendorById('NONEXISTENT')).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        when(
          mockDatabaseService.getVendorById('V001'),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Database error occurred');

        // Act & Assert
        expect(
          () => viewModel.getVendorByIdOrThrow('V001'),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Database error occurred',
            ),
          ),
        );
        verify(mockDatabaseService.getVendorById('V001')).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('createVendor', () {
      test('creates vendor successfully', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Test Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.createVendor(vendor),
        ).thenAnswer((_) async => vendor);

        // Act
        final result = await viewModel.createVendor(vendor);

        // Assert
        expect(result, equals(vendor));
        verify(mockDatabaseService.createVendor(vendor)).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Test Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.createVendor(vendor),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to create vendor');

        // Act & Assert
        expect(
          () => viewModel.createVendor(vendor),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to create vendor',
            ),
          ),
        );
        verify(mockDatabaseService.createVendor(vendor)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('updateVendor', () {
      test('updates vendor successfully', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Updated Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.updateVendor(vendor),
        ).thenAnswer((_) async => vendor);

        // Act
        final result = await viewModel.updateVendor(vendor);

        // Assert
        expect(result, equals(vendor));
        verify(mockDatabaseService.updateVendor(vendor)).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        const vendor = Vendor(
          vendorId: 'V001',
          vendorName: 'Updated Vendor',
          vendorNumber: 'VN001',
          organizationNumber: 'ORG001',
          isActive: true,
          phone: '*********0',
          email: '<EMAIL>',
          address: '123 Test St',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
          bankAccountType: 'checking',
          bankAccountNumber: '*********',
          contactPerson: 'John Doe',
          companyId: 1,
        );
        when(
          mockDatabaseService.updateVendor(vendor),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to update vendor');

        // Act & Assert
        expect(
          () => viewModel.updateVendor(vendor),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update vendor',
            ),
          ),
        );
        verify(mockDatabaseService.updateVendor(vendor)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });

    group('deleteVendor', () {
      test('deletes vendor successfully', () async {
        // Arrange
        const vendorId = 'V001';
        when(
          mockDatabaseService.deleteVendor(vendorId),
        ).thenAnswer((_) async {});

        // Act
        await viewModel.deleteVendor(vendorId);

        // Assert
        verify(mockDatabaseService.deleteVendor(vendorId)).called(1);
        verifyNever(mockErrorHandler.handleError(any));
      });

      test('throws BusinessException when database fails', () async {
        // Arrange
        const vendorId = 'V001';
        when(
          mockDatabaseService.deleteVendor(vendorId),
        ).thenThrow(Exception('Database error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to delete vendor');

        // Act & Assert
        expect(
          () => viewModel.deleteVendor(vendorId),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to delete vendor',
            ),
          ),
        );
        verify(mockDatabaseService.deleteVendor(vendorId)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      });
    });
  });
}
