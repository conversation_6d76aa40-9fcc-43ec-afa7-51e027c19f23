import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/project_viewmodel.dart';

import 'project_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late ProjectViewModel viewModel;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = ProjectViewModel(mockDatabaseService, mockErrorHandler);
  });

  group('ProjectViewModel', () {
    final testProjects = [
      const Project(
        projectId: 1,
        projectCode: 'PROJ-1',
        projectName: 'Test Project 1',
        description: 'Description 1',
      ),
      const Project(
        projectId: 2,
        projectCode: 'PROJ-2',
        projectName: 'Test Project 2',
      ),
    ];

    const testProject = Project(
      projectId: 1,
      projectCode: 'PROJ-1',
      projectName: 'Test Project 1',
      description: 'Description 1',
    );

    test('getProjects returns list of projects', () async {
      // Arrange
      when(
        mockDatabaseService.getProjects(),
      ).thenAnswer((_) async => testProjects);

      // Act
      final result = await viewModel.getProjects();

      // Assert
      expect(result, equals(testProjects));
      verify(mockDatabaseService.getProjects()).called(1);
    });

    test('getProjects handles errors', () async {
      // Arrange
      final testException = Exception('Database error');
      when(mockDatabaseService.getProjects()).thenThrow(testException);
      when(
        mockErrorHandler.handleError(testException),
      ).thenReturn('Error fetching projects');

      // Act & Assert
      expect(
        () => viewModel.getProjects(),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Error fetching projects',
          ),
        ),
      );
      verify(mockDatabaseService.getProjects()).called(1);
      verify(mockErrorHandler.handleError(testException)).called(1);
    });

    test('getProjectById returns project when found', () async {
      // Arrange
      when(
        mockDatabaseService.getProjectById(1),
      ).thenAnswer((_) async => testProject);

      // Act
      final result = await viewModel.getProjectById(1);

      // Assert
      expect(result, equals(testProject));
      verify(mockDatabaseService.getProjectById(1)).called(1);
    });

    test('getProjectById returns null when not found', () async {
      // Arrange
      when(
        mockDatabaseService.getProjectById(999),
      ).thenAnswer((_) async => null);

      // Act
      final result = await viewModel.getProjectById(999);

      // Assert
      expect(result, isNull);
      verify(mockDatabaseService.getProjectById(999)).called(1);
    });

    test('getProjectById handles errors', () async {
      // Arrange
      final testException = Exception('Database error');
      when(mockDatabaseService.getProjectById(1)).thenThrow(testException);
      when(
        mockErrorHandler.handleError(testException),
      ).thenReturn('Error fetching project');

      // Act & Assert
      expect(
        () => viewModel.getProjectById(1),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Error fetching project',
          ),
        ),
      );
      verify(mockDatabaseService.getProjectById(1)).called(1);
      verify(mockErrorHandler.handleError(testException)).called(1);
    });

    test('createProject returns created project', () async {
      // Arrange
      const newProject = Project(
        projectId: 0,
        projectCode: 'NEW-PROJ',
        projectName: 'New Project',
      );

      const createdProject = Project(
        projectId: 3,
        projectCode: 'NEW-PROJ',
        projectName: 'New Project',
      );

      when(
        mockDatabaseService.createProject(newProject),
      ).thenAnswer((_) async => createdProject);

      // Act
      final result = await viewModel.createProject(newProject);

      // Assert
      expect(result, equals(createdProject));
      verify(mockDatabaseService.createProject(newProject)).called(1);
    });

    test('createProject handles errors', () async {
      // Arrange
      const newProject = Project(
        projectId: 0,
        projectCode: 'NEW-PROJ',
        projectName: 'New Project',
      );

      final testException = Exception('Database error');
      when(
        mockDatabaseService.createProject(newProject),
      ).thenThrow(testException);
      when(
        mockErrorHandler.handleError(testException),
      ).thenReturn('Error creating project');

      // Act & Assert
      expect(
        () => viewModel.createProject(newProject),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Error creating project',
          ),
        ),
      );
      verify(mockDatabaseService.createProject(newProject)).called(1);
      verify(mockErrorHandler.handleError(testException)).called(1);
    });

    test('updateProject returns updated project', () async {
      // Arrange
      final updatedProject = testProject.copyWith(
        projectName: 'Updated Project Name',
      );

      when(
        mockDatabaseService.updateProject(updatedProject),
      ).thenAnswer((_) async => updatedProject);

      // Act
      final result = await viewModel.updateProject(updatedProject);

      // Assert
      expect(result, equals(updatedProject));
      verify(mockDatabaseService.updateProject(updatedProject)).called(1);
    });

    test('updateProject handles errors', () async {
      // Arrange
      final updatedProject = testProject.copyWith(
        projectName: 'Updated Project Name',
      );

      final testException = Exception('Database error');
      when(
        mockDatabaseService.updateProject(updatedProject),
      ).thenThrow(testException);
      when(
        mockErrorHandler.handleError(testException),
      ).thenReturn('Error updating project');

      // Act & Assert
      expect(
        () => viewModel.updateProject(updatedProject),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Error updating project',
          ),
        ),
      );
      verify(mockDatabaseService.updateProject(updatedProject)).called(1);
      verify(mockErrorHandler.handleError(testException)).called(1);
    });

    test('deleteProject completes successfully', () async {
      // Arrange
      when(
        mockDatabaseService.deleteProject(1),
      ).thenAnswer((_) async => Future<void>.value());

      // Act
      await viewModel.deleteProject(1);

      // Assert
      verify(mockDatabaseService.deleteProject(1)).called(1);
    });

    test('deleteProject handles errors', () async {
      // Arrange
      final testException = Exception('Database error');
      when(mockDatabaseService.deleteProject(1)).thenThrow(testException);
      when(
        mockErrorHandler.handleError(testException),
      ).thenReturn('Error deleting project');

      // Act & Assert
      expect(
        () => viewModel.deleteProject(1),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Error deleting project',
          ),
        ),
      );
      verify(mockDatabaseService.deleteProject(1)).called(1);
      verify(mockErrorHandler.handleError(testException)).called(1);
    });

    test('throws BusinessException when database fails', () async {
      // Arrange
      when(mockDatabaseService.getProjects()).thenThrow(Exception('DB Error'));
      when(
        mockErrorHandler.handleError(any),
      ).thenReturn('Unable to fetch projects');

      // Act & Assert
      expect(
        () => viewModel.getProjects(),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Unable to fetch projects',
          ),
        ),
      );
    });
  });
}
