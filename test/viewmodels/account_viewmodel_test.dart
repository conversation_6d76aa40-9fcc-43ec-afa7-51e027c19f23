import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';
import 'package:uuid/uuid.dart';

import '../config/test_config.dart';
import '../mocks/mock_supabase.dart';
import 'account_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late DatabaseService databaseService;
  late ErrorHandler errorHandler;
  late AccountViewModel viewModel;
  late bool isOnlineTest;

  setUpAll(() async {
    // Initialize TestConfig
    await TestConfig.instance.initialize();
    isOnlineTest =
        TestConfig.instance.isOnlineTest &&
        !TestConfig.instance.usingFallbackClient;
  });

  setUp(() {
    errorHandler = MockErrorHandler();

    if (isOnlineTest) {
      // Use real database service with mock client provider
      final mockClientProvider = MockSupabaseClientProvider(mockClient: null);
      databaseService = SupabaseDatabaseService(
        mockClientProvider,
        errorHandler,
      );
      debugPrint('Using real SupabaseDatabaseService for testing');
    } else {
      // Use mock database service
      databaseService = MockDatabaseService();
      debugPrint('Using MockDatabaseService for testing');
    }

    viewModel = AccountViewModel(databaseService, errorHandler);
  });

  group('AccountViewModel', () {
    group('getAccounts', () {
      test('returns accounts when the call is successful', () async {
        if (!isOnlineTest) {
          // Mock mode
          final testAccounts = [
            const Account(
              accountNumber: '1000',
              accountName: 'Cash Account',
              accountType: 'asset',
              companyId: 1,
            ),
            const Account(
              accountNumber: '2000',
              accountName: 'Credit Card',
              accountType: 'liability',
              companyId: 1,
            ),
          ];
          when(
            (databaseService as MockDatabaseService).getAccounts(),
          ).thenAnswer((_) async => testAccounts);

          // Act
          final result = await viewModel.getAccounts();

          // Assert
          expect(result, equals(testAccounts));
          verify(
            (databaseService as MockDatabaseService).getAccounts(),
          ).called(1);
        } else {
          // Real database mode - just verify we get valid accounts
          // Act
          final result = await viewModel.getAccounts();

          // Assert
          expect(result, isNotNull);
          expect(result, isA<List<Account>>());
          debugPrint('Got ${result.length} accounts from real database');
        }
      });

      test('throws BusinessException when database fails', () async {
        if (!isOnlineTest) {
          // Mock mode
          when(
            (databaseService as MockDatabaseService).getAccounts(),
          ).thenThrow(Exception('DB Error'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to fetch accounts');

          // Act & Assert
          expect(
            () => viewModel.getAccounts(),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to fetch accounts',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService).getAccounts(),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode as we can't safely simulate database errors
          // without potentially affecting the actual database
          debugPrint('Skipping database error test in online mode for safety');
        }
      });
    });

    group('getAccountByNumber', () {
      test('returns account when found', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          const account = Account(
            accountName: 'Test Account',
            accountNumber: '1000',
            accountType: 'asset',
            companyId: 1,
          );
          when(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).thenAnswer((_) async => account);

          // Act
          final result = await viewModel.getAccountByNumber(accountNumber);

          // Assert
          expect(result, equals(account));
          verify(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).called(1);
        } else {
          // Real database mode - get accounts and try to look up the first one
          final accounts = await viewModel.getAccounts();

          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          final accountToLookup = accounts.first.accountNumber;

          // Act
          final result = await viewModel.getAccountByNumber(accountToLookup);

          // Assert
          expect(result, isNotNull);
          expect(result?.accountNumber, equals(accountToLookup));
          debugPrint(
            'Successfully found account $accountToLookup in real database',
          );
        }
      });

      test('returns null when account not found', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '9999';
          when(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).thenAnswer((_) async => null);

          // Act
          final result = await viewModel.getAccountByNumber(accountNumber);

          // Assert
          expect(result, isNull);
          verify(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).called(1);
        } else {
          // Real database mode - try to look up a non-existent account
          const nonExistentAccount = '99999-NONEXISTENT';

          // Act
          final result = await viewModel.getAccountByNumber(nonExistentAccount);

          // Assert
          expect(result, isNull);
          debugPrint('Successfully verified non-existent account returns null');
        }
      });

      test('throws BusinessException when an error occurs', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).thenThrow(Exception('Account not found'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Account does not exist');

          // Act & Assert
          expect(
            () => viewModel.getAccountByNumber(accountNumber),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Account does not exist',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService).getAccountByNumber(
              accountNumber,
            ),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode as we can't safely simulate database errors
          // without potentially affecting the actual database
          debugPrint('Skipping database error test in online mode for safety');
        }
      });
    });

    group('getGeneralLedgerEntriesByAccount', () {
      test('returns ledger entries when the call is successful', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          final testLedgerEntries = [
            GeneralLedger(
              ledgerId: 1,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 1),
              accountNumber: '1000',
              description: 'Test transaction 1',
              debit: 100.0,
              credit: 0.0,
              currencyCode: 'USD',
              projectId: 1,
              staffId: 1,
              taxAmount: 10.0,
              companyId: 1,
            ),
            GeneralLedger(
              ledgerId: 2,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 2),
              accountNumber: '1000',
              description: 'Test transaction 2',
              debit: 0.0,
              credit: 50.0,
              currencyCode: 'USD',
              projectId: 2,
              staffId: 2,
              taxAmount: 5.0,
              companyId: 1,
            ),
          ];
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenAnswer((_) async => testLedgerEntries);

          // Act
          final result = await viewModel.getGeneralLedgerEntriesByAccount(
            accountNumber,
          );

          // Assert
          expect(result, equals(testLedgerEntries));
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
        } else {
          // Real database mode - get accounts and use the first account to look up ledger entries
          final accounts = await viewModel.getAccounts();

          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          final accountToLookup = accounts.first.accountNumber;

          // Act
          final result = await viewModel.getGeneralLedgerEntriesByAccount(
            accountToLookup,
          );

          // Assert
          expect(result, isA<List<GeneralLedger>>());
          // Check that all entries are for the correct account
          for (var entry in result) {
            expect(entry.accountNumber, equals(accountToLookup));
          }
          debugPrint(
            'Found ${result.length} ledger entries for account $accountToLookup',
          );
        }
      });

      test('throws BusinessException when an error occurs', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenThrow(Exception('Failed to fetch ledger entries'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to load ledger entries');

          // Act & Assert
          expect(
            () => viewModel.getGeneralLedgerEntriesByAccount(accountNumber),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to load ledger entries',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode as we can't safely simulate database errors
          // without potentially affecting the actual database
          debugPrint('Skipping database error test in online mode for safety');
        }
      });
    });

    group('calculateAccountBalance', () {
      test('correctly calculates account balance from ledger entries', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          final testLedgerEntries = [
            // Debits increase the balance
            GeneralLedger(
              ledgerId: 1,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 1),
              accountNumber: accountNumber,
              description: 'Initial balance',
              debit: 1000.0,
              credit: 0.0,
              currencyCode: 'USD',
              projectId: 1,
              staffId: 1,
              taxAmount: 10.0,
              companyId: 1,
            ),
            // Credits decrease the balance
            GeneralLedger(
              ledgerId: 2,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 2),
              accountNumber: accountNumber,
              description: 'Expense payment',
              debit: 0.0,
              credit: 250.0,
              currencyCode: 'USD',
              projectId: 2,
              staffId: 2,
              taxAmount: 5.0,
              companyId: 1,
            ),
            // Another debit
            GeneralLedger(
              ledgerId: 3,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 3),
              accountNumber: accountNumber,
              description: 'Customer payment',
              debit: 500.0,
              credit: 0.0,
              currencyCode: 'USD',
              projectId: 3,
              staffId: 3,
              taxAmount: 0.0,
              companyId: 1,
            ),
          ];
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenAnswer((_) async => testLedgerEntries);

          // Expected balance: 1000 (debit) - 250 (credit) + 500 (debit) = 1250
          const expectedBalance = 1250.0;

          // Act
          final result = await viewModel.calculateAccountBalance(accountNumber);

          // Assert
          expect(result, equals(expectedBalance));
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
        } else {
          // Real database mode - get accounts and calculate balance for first account
          final accounts = await viewModel.getAccounts();

          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          final accountToTest = accounts.first.accountNumber;

          // Act
          final result = await viewModel.calculateAccountBalance(accountToTest);

          // Assert
          expect(result, isA<double>());
          debugPrint('Account $accountToTest has balance of $result');
        }
      });

      test('returns zero balance for an account with no entries', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '9999';
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenAnswer((_) async => []);

          // Act
          final result = await viewModel.calculateAccountBalance(accountNumber);

          // Assert
          expect(result, equals(0.0));
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
        } else {
          // Real database mode - use a non-existent account number
          const nonExistentAccount = '99999-NONEXISTENT';

          // Act
          final result = await viewModel.calculateAccountBalance(
            nonExistentAccount,
          );

          // Assert
          expect(result, equals(0.0));
          debugPrint(
            'Successfully verified non-existent account has zero balance',
          );
        }
      });

      test('throws BusinessException when an error occurs', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenThrow(Exception('Failed to fetch ledger entries'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to load ledger entries');

          // Act & Assert
          expect(
            () => viewModel.calculateAccountBalance(accountNumber),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to load ledger entries',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode as we can't safely simulate database errors
          // without potentially affecting the actual database
          debugPrint('Skipping database error test in online mode for safety');
        }
      });
    });

    group('createAccount', () {
      test('creates account successfully', () async {
        if (!isOnlineTest) {
          // Mock mode
          const account = Account(
            accountNumber: '3000',
            accountName: 'New Account',
            accountType: 'expense',
            companyId: 1,
          );
          when(
            (databaseService as MockDatabaseService).createAccount(account),
          ).thenAnswer((_) async => account);

          // Act
          final result = await viewModel.createAccount(account);

          // Assert
          expect(result, equals(account));
          verify(
            (databaseService as MockDatabaseService).createAccount(account),
          ).called(1);
        } else {
          // Real database mode - create a test account
          const account = Account(
            accountNumber: '3000',
            accountName: 'Test Account',
            accountType: 'expense',
            companyId: 1,
          );

          // Act
          final result = await viewModel.createAccount(account);

          // Assert
          expect(result, isNotNull);
          expect(result.accountNumber, equals(account.accountNumber));
          expect(result.accountName, equals(account.accountName));
          debugPrint('Successfully created account ${result.accountNumber}');

          // Clean up - delete the test account
          await viewModel.deleteAccount(account.accountNumber);
        }
      });

      test('throws BusinessException when creation fails', () async {
        if (!isOnlineTest) {
          // Mock mode
          const account = Account(
            accountNumber: '3000',
            accountName: 'New Account',
            accountType: 'expense',
            companyId: 1,
          );
          when(
            (databaseService as MockDatabaseService).createAccount(account),
          ).thenThrow(Exception('Failed to create account'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to create account');

          // Act & Assert
          expect(
            () => viewModel.createAccount(account),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to create account',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService).createAccount(account),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode for safety
          debugPrint('Skipping account creation error test in online mode');
        }
      });
    });

    group('updateAccount', () {
      test('updates account successfully', () async {
        if (!isOnlineTest) {
          // Mock mode
          const account = Account(
            accountNumber: '1000',
            accountName: 'Updated Account',
            accountType: 'asset',
            companyId: 1,
          );
          when(
            (databaseService as MockDatabaseService).updateAccount(account),
          ).thenAnswer((_) async => account);

          // Act
          final result = await viewModel.updateAccount(account);

          // Assert
          expect(result, equals(account));
          verify(
            (databaseService as MockDatabaseService).updateAccount(account),
          ).called(1);
        } else {
          // Real database mode - update an existing account
          final accounts = await viewModel.getAccounts();
          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          final accountToUpdate = accounts.first;
          final updatedAccount = Account(
            accountNumber: accountToUpdate.accountNumber,
            accountName: 'Updated ${accountToUpdate.accountName}',
            accountType: accountToUpdate.accountType,
            companyId: accountToUpdate.companyId,
          );

          // Act
          final result = await viewModel.updateAccount(updatedAccount);

          // Assert
          expect(result, isNotNull);
          expect(result.accountNumber, equals(updatedAccount.accountNumber));
          expect(result.accountName, equals(updatedAccount.accountName));
          debugPrint('Successfully updated account ${result.accountNumber}');

          // Restore original account name
          await viewModel.updateAccount(accountToUpdate);
        }
      });

      test('throws BusinessException when update fails', () async {
        if (!isOnlineTest) {
          // Mock mode
          const account = Account(
            accountNumber: '1000',
            accountName: 'Updated Account',
            accountType: 'asset',
            companyId: 1,
          );
          when(
            (databaseService as MockDatabaseService).updateAccount(account),
          ).thenThrow(Exception('Failed to update account'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to update account');

          // Act & Assert
          expect(
            () => viewModel.updateAccount(account),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to update account',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService).updateAccount(account),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode for safety
          debugPrint('Skipping account update error test in online mode');
        }
      });
    });

    group('deleteAccount', () {
      test('deletes account successfully', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService).deleteAccount(
              accountNumber,
            ),
          ).thenAnswer((_) async {});

          // Act
          await viewModel.deleteAccount(accountNumber);

          // Assert
          verify(
            (databaseService as MockDatabaseService).deleteAccount(
              accountNumber,
            ),
          ).called(1);
        } else {
          // Real database mode - create and delete a test account
          const account = Account(
            accountNumber: '3000',
            accountName: 'Test Account',
            accountType: 'expense',
            companyId: 1,
          );

          // Create test account
          await viewModel.createAccount(account);

          // Act
          await viewModel.deleteAccount(account.accountNumber);

          // Assert
          final deletedAccount = await viewModel.getAccountByNumber(
            account.accountNumber,
          );
          expect(deletedAccount, isNull);
          debugPrint('Successfully deleted account ${account.accountNumber}');
        }
      });

      test('throws BusinessException when deletion fails', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService).deleteAccount(
              accountNumber,
            ),
          ).thenThrow(Exception('Failed to delete account'));
          when(
            errorHandler.handleError(any),
          ).thenReturn('Unable to delete account');

          // Act & Assert
          expect(
            () => viewModel.deleteAccount(accountNumber),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.message,
                'message',
                'Unable to delete account',
              ),
            ),
          );
          verify(
            (databaseService as MockDatabaseService).deleteAccount(
              accountNumber,
            ),
          ).called(1);
          verify(errorHandler.handleError(any)).called(1);
        } else {
          // Skip this test in online mode for safety
          debugPrint('Skipping account deletion error test in online mode');
        }
      });
    });

    group('getAccounts with company filter', () {
      test('returns accounts filtered by company ID', () async {
        if (!isOnlineTest) {
          // Mock mode
          const companyId = 1;
          final testAccounts = [
            const Account(
              accountNumber: '1000',
              accountName: 'Company 1 Account',
              accountType: 'asset',
              companyId: 1,
            ),
            const Account(
              accountNumber: '2000',
              accountName: 'Company 1 Liability',
              accountType: 'liability',
              companyId: 1,
            ),
          ];
          when(
            (databaseService as MockDatabaseService).getAccounts(companyId),
          ).thenAnswer((_) async => testAccounts);

          // Act
          final result = await viewModel.getAccounts(companyId);

          // Assert
          expect(result, equals(testAccounts));
          verify(
            (databaseService as MockDatabaseService).getAccounts(companyId),
          ).called(1);
        } else {
          // Real database mode - get accounts for a specific company
          const companyId = 1;

          // Act
          final result = await viewModel.getAccounts(companyId);

          // Assert
          expect(result, isA<List<Account>>());
          for (var account in result) {
            expect(account.companyId, equals(companyId));
          }
          debugPrint('Found ${result.length} accounts for company $companyId');
        }
      });
    });

    group('calculateAccountBalance edge cases', () {
      test('returns zero balance for account with no entries', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenAnswer((_) async => []);

          // Act
          final result = await viewModel.calculateAccountBalance(accountNumber);

          // Assert
          expect(result, equals(0.0));
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
        } else {
          // Real database mode - find an account with no entries
          final accounts = await viewModel.getAccounts();
          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          // Find an account with no entries
          for (var account in accounts) {
            final entries = await viewModel.getGeneralLedgerEntriesByAccount(
              account.accountNumber,
            );
            if (entries.isEmpty) {
              // Act
              final result = await viewModel.calculateAccountBalance(
                account.accountNumber,
              );

              // Assert
              expect(result, equals(0.0));
              debugPrint(
                'Verified zero balance for account ${account.accountNumber}',
              );
              break;
            }
          }
        }
      });

      test('handles negative balance correctly', () async {
        if (!isOnlineTest) {
          // Mock mode
          const accountNumber = '1000';
          final testLedgerEntries = [
            GeneralLedger(
              ledgerId: 1,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 1),
              accountNumber: accountNumber,
              description: 'Initial balance',
              debit: 100.0,
              credit: 0.0,
              currencyCode: 'USD',
              projectId: 1,
              staffId: 1,
              taxAmount: 0.0,
              companyId: 1,
            ),
            GeneralLedger(
              ledgerId: 2,
              transactionId: const Uuid().v4(),
              transactionDate: DateTime(2023, 1, 2),
              accountNumber: accountNumber,
              description: 'Large expense',
              debit: 0.0,
              credit: 500.0,
              currencyCode: 'USD',
              projectId: 2,
              staffId: 2,
              taxAmount: 0.0,
              companyId: 1,
            ),
          ];
          when(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).thenAnswer((_) async => testLedgerEntries);

          // Expected balance: 100 (debit) - 500 (credit) = -400
          const expectedBalance = -400.0;

          // Act
          final result = await viewModel.calculateAccountBalance(accountNumber);

          // Assert
          expect(result, equals(expectedBalance));
          verify(
            (databaseService as MockDatabaseService)
                .getGeneralLedgerEntriesByAccount(accountNumber),
          ).called(1);
        } else {
          // Real database mode - find an account with negative balance
          final accounts = await viewModel.getAccounts();
          if (accounts.isEmpty) {
            debugPrint('No accounts found in real database, skipping test');
            return;
          }

          // Find an account with negative balance
          for (var account in accounts) {
            final balance = await viewModel.calculateAccountBalance(
              account.accountNumber,
            );
            if (balance < 0) {
              debugPrint(
                'Found account ${account.accountNumber} with negative balance: $balance',
              );
              break;
            }
          }
        }
      });

      test(
        'throws BusinessException when ledger entries fetch fails',
        () async {
          if (!isOnlineTest) {
            // Mock mode
            const accountNumber = '1000';
            when(
              (databaseService as MockDatabaseService)
                  .getGeneralLedgerEntriesByAccount(accountNumber),
            ).thenThrow(Exception('Failed to fetch ledger entries'));
            when(
              errorHandler.handleError(any),
            ).thenReturn('Unable to calculate account balance');

            // Act & Assert
            expect(
              () => viewModel.calculateAccountBalance(accountNumber),
              throwsA(
                isA<BusinessException>().having(
                  (e) => e.message,
                  'message',
                  'Unable to calculate account balance',
                ),
              ),
            );
            verify(
              (databaseService as MockDatabaseService)
                  .getGeneralLedgerEntriesByAccount(accountNumber),
            ).called(1);
            verify(errorHandler.handleError(any)).called(1);
          } else {
            // Skip this test in online mode for safety
            debugPrint(
              'Skipping balance calculation error test in online mode',
            );
          }
        },
      );
    });

    // Tests for createAccount, updateAccount, and deleteAccount can follow
    // the same pattern as above
  });
}
