import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/utils/test_data.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/viewmodels/company_viewmodel.dart';

import 'company_viewmodel_test.mocks.dart';

@GenerateMocks([DatabaseService, ErrorHandler])
void main() {
  late MockDatabaseService mockDatabaseService;
  late MockErrorHandler mockErrorHandler;
  late CompanyViewModel viewModel;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = CompanyViewModel(mockDatabaseService, mockErrorHandler);
  });

  group('CompanyViewModel', () {
    test('getCompanies returns list of companies', () async {
      // Arrange
      when(
        mockDatabaseService.getCompanies(),
      ).thenAnswer((_) async => [TestData.testCompany]);

      // Act
      final result = await viewModel.getCompanies();

      // Assert
      expect(result, [TestData.testCompany]);
      verify(mockDatabaseService.getCompanies()).called(1);
    });

    test('getCompanies throws BusinessException when database fails', () async {
      // Arrange
      when(
        mockDatabaseService.getCompanies(),
      ).thenThrow(Exception('Database error'));
      when(
        mockErrorHandler.handleError(any),
      ).thenReturn('Failed to load companies');

      // Act & Assert
      expect(
        () => viewModel.getCompanies(),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Failed to load companies',
          ),
        ),
      );
      verify(mockDatabaseService.getCompanies()).called(1);
      verify(mockErrorHandler.handleError(any)).called(1);
    });

    test('getCompanyById returns company when found', () async {
      // Arrange
      when(
        mockDatabaseService.getCompanyById(1),
      ).thenAnswer((_) async => TestData.testCompany);

      // Act
      final result = await viewModel.getCompanyById(1);

      // Assert
      expect(result, TestData.testCompany);
      verify(mockDatabaseService.getCompanyById(1)).called(1);
    });

    test('getCompanyById returns null when company not found', () async {
      // Arrange
      when(
        mockDatabaseService.getCompanyById(any),
      ).thenAnswer((_) async => null);

      // Act
      final result = await viewModel.getCompanyById(1);

      // Assert
      expect(result, isNull);
      verify(mockDatabaseService.getCompanyById(1)).called(1);
    });

    test(
      'getCompanyById throws BusinessException when database fails',
      () async {
        // Arrange
        when(
          mockDatabaseService.getCompanyById(any),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Unable to fetch company');

        // Act & Assert
        expect(
          () => viewModel.getCompanyById(1),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Unable to fetch company',
            ),
          ),
        );
        verify(mockDatabaseService.getCompanyById(1)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      },
    );

    test('createCompany returns created company', () async {
      // Arrange
      when(
        mockDatabaseService.createCompany(TestData.testCompany),
      ).thenAnswer((_) async => TestData.testCompany);

      // Act
      final result = await viewModel.createCompany(TestData.testCompany);

      // Assert
      expect(result, TestData.testCompany);
      verify(mockDatabaseService.createCompany(TestData.testCompany)).called(1);
    });

    test('createCompany validates required fields', () async {
      // Arrange
      const invalidCompany = Company(
        companyId: 1,
        companyName: '',
        organizationNumber: 'ORG-123',
        city: 'Test City',
        country: 'Test Country',
      );

      // Act & Assert
      expect(
        () => viewModel.createCompany(invalidCompany),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Company name is required',
          ),
        ),
      );
      verifyNever(mockDatabaseService.createCompany(any));
      verifyNever(mockErrorHandler.handleError(any));
    });

    test(
      'createCompany throws BusinessException when database fails',
      () async {
        // Arrange
        when(
          mockDatabaseService.createCompany(any),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Unable to create company');

        // Act & Assert
        expect(
          () => viewModel.createCompany(TestData.testCompany),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Unable to create company',
            ),
          ),
        );
        verify(mockDatabaseService.createCompany(any)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      },
    );

    test('updateCompany returns updated company', () async {
      // Arrange
      when(
        mockDatabaseService.updateCompany(TestData.testCompany),
      ).thenAnswer((_) async => TestData.testCompany);

      // Act
      final result = await viewModel.updateCompany(TestData.testCompany);

      // Assert
      expect(result, TestData.testCompany);
      verify(mockDatabaseService.updateCompany(TestData.testCompany)).called(1);
    });

    test('updateCompany validates required fields', () async {
      // Arrange
      const invalidCompany = Company(
        companyId: 1,
        companyName: '',
        organizationNumber: 'ORG-123',
        city: 'Test City',
        country: 'Test Country',
      );

      // Act & Assert
      expect(
        () => viewModel.updateCompany(invalidCompany),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Company name is required',
          ),
        ),
      );
      verifyNever(mockDatabaseService.updateCompany(any));
      verifyNever(mockErrorHandler.handleError(any));
    });

    test(
      'updateCompany throws BusinessException when database fails',
      () async {
        // Arrange
        when(
          mockDatabaseService.updateCompany(any),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Unable to update company');

        // Act & Assert
        expect(
          () => viewModel.updateCompany(TestData.testCompany),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Unable to update company',
            ),
          ),
        );
        verify(mockDatabaseService.updateCompany(any)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      },
    );

    test('deleteCompany succeeds when database operation succeeds', () async {
      // Arrange
      when(mockDatabaseService.deleteCompany(any)).thenAnswer((_) async => {});

      // Act
      await viewModel.deleteCompany(1);

      // Assert
      verify(mockDatabaseService.deleteCompany(1)).called(1);
    });

    test(
      'deleteCompany throws BusinessException when database fails',
      () async {
        // Arrange
        when(
          mockDatabaseService.deleteCompany(any),
        ).thenThrow(Exception('DB Error'));
        when(
          mockErrorHandler.handleError(any),
        ).thenReturn('Failed to delete company');

        // Act & Assert
        expect(
          () => viewModel.deleteCompany(1),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to delete company',
            ),
          ),
        );
        verify(mockDatabaseService.deleteCompany(1)).called(1);
        verify(mockErrorHandler.handleError(any)).called(1);
      },
    );
  });
}
