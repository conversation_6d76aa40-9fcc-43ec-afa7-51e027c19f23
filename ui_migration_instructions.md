# UI Directory Structure Migration Instructions

## Overview

This document provides instructions for migrating the UI directory structure in the WeLikeMoney application. The goal is to create a more consistent and organized structure for UI components.

## Prerequisites

Before starting the migration, make sure you have:

1. A backup of your code (preferably in a version control system)
2. All tests passing in the current state
3. No uncommitted changes

## Migration Steps

### 1. Review the Migration Plan

Review the `ui_migration_plan.md` file to understand the changes that will be made.

### 2. Run the Migration Script

The migration process is automated through several scripts:

```bash
# Make the scripts executable
chmod +x *.sh

# Run the master migration script
./run_migration.sh
```

The master script will:

1. Create the new directory structure
2. Copy files to their new locations
3. Update imports in all files
4. Run tests to verify the migration
5. Optionally clean up old directories

### 3. Manual Verification

After the automated migration, manually verify:

1. The application builds without errors
2. All screens render correctly
3. Navigation between screens works
4. Forms can be submitted
5. Data is displayed correctly

### 4. Fix Any Issues

If you encounter any issues:

1. Check the import statements in the affected files
2. Verify that all files were copied to the correct locations
3. Make sure the form section imports were updated correctly

### 5. Clean Up

If you didn't run the cleanup step during the migration, you can run it separately:

```bash
./cleanup_migration.sh
```

This will remove the old directories and files.

### 6. Update Documentation

Update any documentation that references the old directory structure.

## Troubleshooting

### Import Errors

If you encounter import errors:

1. Check if the file exists in the new location
2. Verify that the import path is correct
3. Run the import update script again for the specific file:

```bash
./update_imports.sh
```

### Build Errors

If the application fails to build:

1. Check the error messages for clues
2. Verify that all dependencies are correctly imported
3. Make sure all files were copied to the correct locations

### Test Failures

If tests fail after migration:

1. Check if the test is referencing the old directory structure
2. Update test imports to use the new directory structure
3. Verify that the test is using the correct components

## Rollback Plan

If you need to rollback the migration:

1. Restore from your backup
2. Or, if using version control, revert the changes

## Conclusion

This migration will create a more consistent and organized directory structure for UI components, making it easier to navigate the codebase and understand the organization of UI components.
