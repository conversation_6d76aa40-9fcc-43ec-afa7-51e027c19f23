# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project setup
- Basic project structure following clean architecture
- Data models for all entities (Project, StaffMember, Currency, etc.)
- Database service interface and Supabase implementation stub
- Basic UI components (CurrencyDisplay)
- Unit tests for models and widgets
- Integration test setup
- CI/CD pipeline with GitHub Actions

### Changed
- None

### Deprecated
- None

### Removed
- None

### Fixed
- None

### Security
- None 