{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "packageRules": [{"matchPackagePatterns": ["^flutter$", "^flutter_test$", "^integration_test$", "^flutter_driver$"], "groupName": "flutter", "allowedVersions": ">=3.19.0", "schedule": ["every weekend"], "prNotPendingHours": 72, "stabilityDays": 3, "internalChecksFilter": "strict"}, {"matchPackagePatterns": ["^build_runner$", "^freezed$", "^json_serializable$", "^injectable_generator$"], "groupName": "code generation"}, {"matchPackagePatterns": ["^flutter_riverpod$", "^flutter_bloc$", "^get_it$", "^injectable$"], "groupName": "state management"}], "schedule": ["every weekend"], "prHourlyLimit": 4, "prConcurrentLimit": 16, "rangeStrategy": "pin", "semanticCommits": "enabled", "labels": ["dependencies"], "assignees": ["<PERSON><PERSON><PERSON><PERSON>"], "reviewers": ["<PERSON><PERSON><PERSON><PERSON>"], "automerge": false, "automergeType": "pr", "platformCommit": "enabled", "stabilityDays": 3, "internalChecksFilter": "strict", "prNotPendingHours": 72, "lockFileMaintenance": {"enabled": true, "extends": "config:base", "packageRules": [{"matchPackagePatterns": ["^"], "rangeStrategy": "pin"}]}, "postUpdateOptions": ["yarnDedupeFewer"], "ignoreDeps": [], "ignorePaths": [], "ignoreUnstable": true, "respectLatest": true, "timezone": "Europe/Stockholm", "updateNotScheduled": false, "versioning": "semver", "vulnerabilityAlert": {"enabled": true, "schedule": null, "prCreation": "immediate", "labels": ["security"], "assignees": ["<PERSON><PERSON><PERSON><PERSON>"], "reviewers": ["<PERSON><PERSON><PERSON><PERSON>"]}}