#!/bin/bash

# Update form section imports in all files

# Find all files that import form_section.dart or form_section_header.dart
grep -l "import.*ui/forms/form_section.dart" $(find lib -name "*.dart") | while read file; do
  echo "Updating form_section import in $file"
  sed -i '' 's|import "package:we_like_money/ui/forms/form_section.dart"|import "package:we_like_money/ui/forms/sections/form_section.dart"|g' "$file"
done

grep -l "import.*ui/forms/form_section_header.dart" $(find lib -name "*.dart") | while read file; do
  echo "Updating form_section_header import in $file"
  sed -i '' 's|import "package:we_like_money/ui/forms/form_section_header.dart"|import "package:we_like_money/ui/forms/sections/form_section_header.dart"|g' "$file"
done

# Update index.dart to point to the new locations
if [ -f "lib/ui/forms/index.dart" ]; then
  echo "Updating lib/ui/forms/index.dart"
  sed -i '' 's|export "form_section.dart";|export "sections/form_section.dart";|g' "lib/ui/forms/index.dart"
  sed -i '' 's|export "form_section_header.dart";|export "sections/form_section_header.dart";|g' "lib/ui/forms/index.dart"
fi

echo "Form section import updates complete."
