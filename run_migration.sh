#!/bin/bash

# Master script to run all migration steps

echo "This script will migrate the UI directory structure."
echo "Make sure you have a backup of your code before proceeding."
echo "Press Enter to continue or Ctrl+C to cancel..."
read

# Step 1: Run the migration script
echo "Step 1: Running migration script..."
bash ui_migration_script.sh

# Step 2: Update imports
echo "Step 2: Updating imports..."
bash update_imports.sh

# Step 3: Update form section imports
echo "Step 3: Updating form section imports..."
bash update_form_section_imports.sh

# Step 4: Test the application
echo "Step 4: Testing the application..."
bash test_migration.sh

# Step 5: Ask if cleanup should be performed
echo "Migration and testing complete."
echo "Do you want to clean up the old directories? (y/n)"
read answer

if [ "$answer" = "y" ]; then
  echo "Running cleanup..."
  bash cleanup_migration.sh
else
  echo "Skipping cleanup. You can run cleanup_migration.sh later if needed."
fi

echo "Migration process complete."
