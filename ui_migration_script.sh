#!/bin/bash

# UI Directory Structure Migration Script

# Create the new directory structure
echo "Creating new directory structure..."
mkdir -p lib/ui/common/widgets
mkdir -p lib/ui/common/dialogs
mkdir -p lib/ui/common/layouts
mkdir -p lib/ui/forms/sections
mkdir -p lib/ui/features/accounts/components
mkdir -p lib/ui/features/expenses/components/refactored
mkdir -p lib/ui/features/general_ledger/components
mkdir -p lib/ui/features/home
mkdir -p lib/ui/features/settings
mkdir -p lib/ui/features/vendors/components
mkdir -p lib/ui/features/vendor_invoices/components
mkdir -p lib/ui/features/companies
mkdir -p lib/ui/features/projects

# Migrate widgets
echo "Migrating widgets..."
mkdir -p lib/ui/common/widgets
cp lib/widgets/custom_dropdown.dart lib/ui/common/widgets/
cp lib/widgets/date_picker_field.dart lib/ui/common/widgets/
cp lib/widgets/error_display.dart lib/ui/common/widgets/
cp lib/widgets/loading_display.dart lib/ui/common/widgets/
cp lib/ui/widgets/company_selector.dart lib/ui/common/widgets/
cp lib/ui/widgets/connection_status.dart lib/ui/common/widgets/
cp lib/ui/widgets/currency_display.dart lib/ui/common/widgets/
cp lib/ui/widgets/form_fields.dart lib/ui/common/widgets/

# Migrate account feature
echo "Migrating account feature..."
cp lib/screens/account/account_list_screen.dart lib/ui/features/accounts/
cp lib/screens/account/account_list_screen_refactored.dart lib/ui/features/accounts/
cp lib/screens/account/account_register_screen.dart lib/ui/features/accounts/
cp lib/screens/account/components/account_list_item.dart lib/ui/features/accounts/components/
cp lib/screens/account/components/account_search_bar.dart lib/ui/features/accounts/components/
cp lib/screens/account/components/empty_accounts_view.dart lib/ui/features/accounts/components/
cp lib/screens/account/components/error_view.dart lib/ui/features/accounts/components/
cp lib/screens/account/components/loading_view.dart lib/ui/features/accounts/components/
cp lib/ui/screens/account_details_screen.dart lib/ui/features/accounts/
cp lib/ui/screens/accounts_screen.dart lib/ui/features/accounts/

# Migrate general ledger feature
echo "Migrating general ledger feature..."
cp lib/screens/general_ledger/general_ledger_screen_refactored.dart lib/ui/features/general_ledger/general_ledger_screen.dart
cp lib/screens/general_ledger/components/empty_ledger_view.dart lib/ui/features/general_ledger/components/
cp lib/screens/general_ledger/components/error_view.dart lib/ui/features/general_ledger/components/
cp lib/screens/general_ledger/components/filter_bar.dart lib/ui/features/general_ledger/components/
cp lib/screens/general_ledger/components/ledger_table.dart lib/ui/features/general_ledger/components/
cp lib/screens/general_ledger/components/loading_view.dart lib/ui/features/general_ledger/components/
cp lib/screens/general_ledger/components/summary_card.dart lib/ui/features/general_ledger/components/

# Migrate vendor feature
echo "Migrating vendor feature..."
cp lib/screens/vendor/vendor_detail_screen.dart lib/ui/features/vendors/
cp lib/screens/vendor/vendor_detail_screen_refactored.dart lib/ui/features/vendors/
cp lib/screens/vendor/vendor_list_screen.dart lib/ui/features/vendors/
cp lib/screens/vendor/vendor_quick_create_dialog.dart lib/ui/features/vendors/
cp lib/screens/vendor/components/address_section.dart lib/ui/features/vendors/components/
cp lib/screens/vendor/components/banking_info_section.dart lib/ui/features/vendors/components/
cp lib/screens/vendor/components/basic_info_section.dart lib/ui/features/vendors/components/
cp lib/screens/vendor/components/contact_info_section.dart lib/ui/features/vendors/components/
cp lib/screens/vendor/components/save_button_section.dart lib/ui/features/vendors/components/

# Migrate vendor invoice feature
echo "Migrating vendor invoice feature..."
cp lib/screens/vendor_invoice/vendor_invoice_detail_screen.dart lib/ui/features/vendor_invoices/
cp lib/screens/vendor_invoice/vendor_invoice_entry_screen.dart lib/ui/features/vendor_invoices/
cp lib/screens/vendor_invoice/vendor_invoice_form_state.dart lib/ui/features/vendor_invoices/
cp lib/screens/vendor_invoice/vendor_invoice_list_screen.dart lib/ui/features/vendor_invoices/
cp lib/screens/vendor_invoice/vendor_invoice_view_model.dart lib/ui/features/vendor_invoices/
cp lib/screens/vendor_invoice/components/accounting_section.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/invoice_details_section.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/invoice_filter_dialog.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/invoice_list_item.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/invoice_search_bar.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/optional_fields_section.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/save_button.dart lib/ui/features/vendor_invoices/components/
cp lib/screens/vendor_invoice/components/vendor_selection_section.dart lib/ui/features/vendor_invoices/components/

# Migrate expense feature
echo "Migrating expense feature..."
cp lib/ui/screens/expense/expense_entry_screen.dart lib/ui/features/expenses/
cp lib/ui/screens/expense/expense_entry_screen_refactored.dart lib/ui/features/expenses/
cp lib/ui/screens/expense/expense_form_state.dart lib/ui/features/expenses/
cp lib/ui/screens/expense/expense_view_model.dart lib/ui/features/expenses/
cp lib/ui/screens/expense/components/expense_details_section.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/form_section_header.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/payment_details_section.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/project_staff_section.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/save_button.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/vendor_selection_section.dart lib/ui/features/expenses/components/
cp lib/ui/screens/expense/components/refactored/expense_details_section.dart lib/ui/features/expenses/components/refactored/
cp lib/ui/screens/expense/components/refactored/payment_details_section.dart lib/ui/features/expenses/components/refactored/
cp lib/ui/screens/expense/components/refactored/project_staff_section.dart lib/ui/features/expenses/components/refactored/
cp lib/ui/screens/expense/components/refactored/vendor_section.dart lib/ui/features/expenses/components/refactored/
cp lib/ui/screens/expenses_screen.dart lib/ui/features/expenses/

# Migrate other screens
echo "Migrating other screens..."
cp lib/ui/screens/home_screen.dart lib/ui/features/home/
cp lib/ui/screens/settings_screen.dart lib/ui/features/settings/
cp lib/ui/screens/companies_screen.dart lib/ui/features/companies/
cp lib/ui/screens/projects_screen.dart lib/ui/features/projects/

# Migrate form sections
echo "Migrating form sections..."
cp lib/ui/forms/form_section.dart lib/ui/forms/sections/
cp lib/ui/forms/form_section_header.dart lib/ui/forms/sections/

echo "Migration complete. Now you need to update imports in all files."
