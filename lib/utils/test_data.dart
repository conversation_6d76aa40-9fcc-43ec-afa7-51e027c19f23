import 'package:we_like_money/models/company.dart';

/// Utility class containing common test data used across tests
class TestData {
  // Company test data
  static const testCompany = Company(
    companyId: 1,
    companyName: 'Test Company',
    organizationNumber: 'ORG-123',
    phone: '+1234567890',
    email: '<EMAIL>',
    address: 'Test Address',
    zipCode: '12345',
    city: 'Test City',
    country: 'Test Country',
  );

  static const testCompany2 = Company(
    companyId: 2,
    companyName: 'Test Company 2',
    organizationNumber: 'ORG-456',
    phone: '+0987654321',
    email: '<EMAIL>',
    address: 'Test Address 2',
    zipCode: '67890',
    city: 'Test City',
    country: 'Test Country',
  );

  static List<Company> getTestCompanies() => [testCompany, testCompany2];
}
