import 'validation_rule.dart';
import 'base_validator.dart';
import 'date_validator.dart';
import 'currency_validator.dart';
import 'invoice_validator.dart';

/// A unified validator class that provides access to all validation methods
class Validator {
  // Base validation methods
  
  /// Creates a validation rule that ensures a value is not null or empty
  static ValidationRule<String> required([String errorMessage = 'This field is required']) {
    return BaseValidator.required(errorMessage);
  }

  /// Creates a validation rule that ensures a value matches a pattern
  static ValidationRule<String> pattern(
    RegExp pattern, [
    String errorMessage = 'Invalid format',
  ]) {
    return BaseValidator.pattern(pattern, errorMessage);
  }

  /// Creates a validation rule that ensures a value has a minimum length
  static ValidationRule<String> minLength(
    int minLength, [
    String? errorMessage,
  ]) {
    return BaseValidator.minLength(minLength, errorMessage);
  }

  /// Creates a validation rule that ensures a value has a maximum length
  static ValidationRule<String> maxLength(
    int maxLength, [
    String? errorMessage,
  ]) {
    return BaseValidator.maxLength(maxLength, errorMessage);
  }

  /// Creates a validation rule that ensures a value is a valid email address
  static ValidationRule<String> email([
    String errorMessage = 'Please enter a valid email address',
  ]) {
    return BaseValidator.email(errorMessage);
  }

  /// Creates a validation rule that ensures a value is a valid numeric value
  static ValidationRule<String> numeric([
    String errorMessage = 'Please enter a valid number',
  ]) {
    return BaseValidator.numeric(errorMessage);
  }

  /// Creates a validation rule that ensures a numeric value is within a range
  static ValidationRule<String> numericRange({
    double? min,
    double? max,
    bool allowNegative = true,
    String? errorMessage,
  }) {
    return BaseValidator.numericRange(
      min: min,
      max: max,
      allowNegative: allowNegative,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule that ensures two values match
  static ValidationRule<String> matches(
    String Function() getCompareValue, [
    String errorMessage = 'Values do not match',
  ]) {
    return BaseValidator.matches(getCompareValue, errorMessage);
  }

  /// Creates a validator for password fields with customizable requirements
  static ValidationRule<String> password({
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = true,
    String? defaultErrorMessage,
  }) {
    return BaseValidator.password(
      minLength: minLength,
      requireUppercase: requireUppercase,
      requireLowercase: requireLowercase,
      requireNumbers: requireNumbers,
      requireSpecialChars: requireSpecialChars,
      defaultErrorMessage: defaultErrorMessage,
    );
  }

  /// Creates a validator for money/currency input
  static ValidationRule<String> moneyAmount({
    double? minValue,
    double? maxValue,
    bool allowNegative = false,
    String? errorMessage,
  }) {
    return BaseValidator.moneyAmount(
      minValue: minValue,
      maxValue: maxValue,
      allowNegative: allowNegative,
      errorMessage: errorMessage,
    );
  }

  // Date validation methods

  /// Creates a validation rule that ensures a date is in the correct format
  static ValidationRule<String> dateFormat(
    String format, [
    String? errorMessage,
  ]) {
    return DateValidator.format(format, errorMessage);
  }

  /// Creates a validation rule that ensures a date is after a minimum date
  static ValidationRule<String> dateAfter(
    DateTime minDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusive = false,
  ]) {
    return DateValidator.after(minDate, dateFormat, errorMessage, inclusive);
  }

  /// Creates a validation rule that ensures a date is before a maximum date
  static ValidationRule<String> dateBefore(
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusive = false,
  ]) {
    return DateValidator.before(maxDate, dateFormat, errorMessage, inclusive);
  }

  /// Creates a validation rule that ensures a date is between a minimum and maximum date
  static ValidationRule<String> dateBetween(
    DateTime minDate,
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusiveMin = true,
    bool inclusiveMax = true,
  ]) {
    return DateValidator.between(
      minDate, 
      maxDate, 
      dateFormat, 
      errorMessage, 
      inclusiveMin, 
      inclusiveMax,
    );
  }

  /// Creates a validation rule that ensures a date is not a weekend
  static ValidationRule<String> dateNotWeekend(
    String dateFormat, [
    String errorMessage = 'Date cannot be a weekend',
  ]) {
    return DateValidator.notWeekend(dateFormat, errorMessage);
  }

  /// Creates a validation rule that ensures a date is a business day
  static ValidationRule<String> businessDay(
    String dateFormat, {
    List<DateTime> holidays = const [],
    String errorMessage = 'Date must be a business day',
  }) {
    return DateValidator.businessDay(
      dateFormat,
      holidays: holidays,
      errorMessage: errorMessage,
    );
  }

  // Currency validation methods

  /// Creates a validation rule for currency codes (ISO 4217)
  static ValidationRule<String> currencyCode([
    String? errorMessage,
  ]) {
    return CurrencyValidator.currencyCode(errorMessage);
  }

  /// Creates a validation rule for currency names
  static ValidationRule<String> currencyName({
    int maxLength = 50,
    String? errorMessage,
  }) {
    return CurrencyValidator.currencyName(
      maxLength: maxLength,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule for exchange rates
  static ValidationRule<String> exchangeRate({
    double minRate = 0.00001,
    double? maxRate,
    String? errorMessage,
  }) {
    return CurrencyValidator.exchangeRate(
      minRate: minRate,
      maxRate: maxRate,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule that ensures target currency is not the same as base
  static ValidationRule<String> targetCurrencyNotSameAsBase(
    String Function() getBaseCurrencyCode, [
    String errorMessage = 'Target currency cannot be the same as base currency',
  ]) {
    return CurrencyValidator.targetCurrencyNotSameAsBase(
      getBaseCurrencyCode,
      errorMessage,
    );
  }

  /// Creates a validation rule for currency symbols
  static ValidationRule<String> currencySymbol({
    int maxLength = 5,
    String? errorMessage,
  }) {
    return CurrencyValidator.currencySymbol(
      maxLength: maxLength,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule for decimal places (0-6)
  static ValidationRule<String> decimalPlaces([
    String? errorMessage,
  ]) {
    return CurrencyValidator.decimalPlaces(errorMessage);
  }

  // Invoice validation methods

  /// Creates a validation rule for invoice numbers
  static ValidationRule<String> invoiceNumber({
    RegExp? pattern,
    String? errorMessage,
    int maxLength = 50,
  }) {
    return InvoiceValidator.invoiceNumber(
      pattern: pattern,
      errorMessage: errorMessage,
      maxLength: maxLength,
    );
  }

  /// Creates a validation rule for due dates relative to invoice dates
  static ValidationRule<String> invoiceDueDate(
    DateTime invoiceDate, {
    int minDays = 0,
    int? maxDays,
    String dateFormat = 'yyyy-MM-dd',
    String? errorMessage,
  }) {
    return InvoiceValidator.dueDate(
      invoiceDate,
      minDays: minDays,
      maxDays: maxDays,
      dateFormat: dateFormat,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule for invoice amounts
  static ValidationRule<String> invoiceAmount({
    double minAmount = 0.01,
    double? maxAmount,
    bool allowZero = false,
    String? errorMessage,
  }) {
    return InvoiceValidator.amount(
      minAmount: minAmount,
      maxAmount: maxAmount,
      allowZero: allowZero,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule for tax amounts relative to invoice amounts
  static ValidationRule<String> taxAmount(
    double Function() getInvoiceAmount, {
    double maxPercentage = 100.0,
    String? errorMessage,
  }) {
    return InvoiceValidator.taxAmount(
      getInvoiceAmount,
      maxPercentage: maxPercentage,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validation rule that ensures a vendor has been selected
  static ValidationRule<String> vendorSelected([
    String errorMessage = 'Please select a vendor',
  ]) {
    return InvoiceValidator.vendorSelected(errorMessage);
  }

  /// Creates a validation rule that ensures an account has been selected
  static ValidationRule<String> accountSelected([
    String errorMessage = 'Please select an account',
  ]) {
    return InvoiceValidator.accountSelected(errorMessage);
  }

  // Composition methods

  /// Creates a validation rule that applies all rules in sequence
  static ValidationRule<String> all(List<ValidationRule<String>> rules) {
    return ValidationRule.all(rules);
  }

  /// Creates a Flutter-compatible validator function from a validation rule
  static String? Function(String?) createValidator(ValidationRule<String> rule) {
    return BaseValidator.createValidator(rule);
  }
}
