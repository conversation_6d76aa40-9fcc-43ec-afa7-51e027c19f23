import 'package:intl/intl.dart';
import 'validation_rule.dart';
import 'base_validator.dart';

/// Specialized validator for invoice-related fields
class InvoiceValidator {
  /// Creates a validation rule for invoice numbers
  static ValidationRule<String> invoiceNumber({
    RegExp? pattern,
    String? errorMessage,
    int maxLength = 50,
  }) {
    final invoicePattern = pattern ?? RegExp(r'^[a-zA-Z0-9\-_/\.]+$');

    return ValidationRule.all([
      BaseValidator.required(errorMessage ?? 'Invoice number is required'),
      BaseValidator.maxLength(
        maxLength,
        'Invoice number is too long (maximum $maxLength characters)',
      ),
      BaseValidator.pattern(
        invoicePattern,
        errorMessage ?? 'Invalid invoice number format',
      ),
    ]);
  }

  /// Creates a validation rule for due dates relative to invoice dates
  static ValidationRule<String> dueDate(
    DateTime invoiceDate, {
    int minDays = 0,
    int? maxDays,
    String dateFormat = 'yyyy-MM-dd',
    String? errorMessage,
  }) {
    final formatter = DateFormat(dateFormat);
    // Normalize invoice date to start of day
    final normalizedInvoiceDate = DateTime(
      invoiceDate.year,
      invoiceDate.month,
      invoiceDate.day,
    );

    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }

        try {
          final dueDate = formatter.parseStrict(value);
          // Normalize due date to start of day
          final normalizedDueDate = DateTime(
            dueDate.year,
            dueDate.month,
            dueDate.day,
          );

          // Calculate days difference
          final difference =
              normalizedDueDate.difference(normalizedInvoiceDate).inDays;

          if (difference < minDays) {
            return errorMessage ??
                'Due date must be at least $minDays day${minDays == 1 ? '' : 's'} after invoice date';
          }

          if (maxDays != null && difference > maxDays) {
            return errorMessage ??
                'Due date cannot be more than $maxDays day${maxDays == 1 ? '' : 's'} after invoice date';
          }

          return null;
        } catch (e) {
          return 'Please enter a valid date in $dateFormat format';
        }
      },
      name: 'dueDate(minDays: $minDays, maxDays: $maxDays)',
    );
  }

  /// Creates a validation rule for invoice amounts
  static ValidationRule<String> amount({
    double minAmount = 0.01,
    double? maxAmount,
    bool allowZero = false,
    String? errorMessage,
  }) {
    final actualMinAmount = allowZero ? 0.0 : minAmount;

    return ValidationRule.all([
      BaseValidator.required('Amount is required'),
      // Use moneyAmount for parsing and range checks, always disallow negative for invoices.
      BaseValidator.moneyAmount(
        minValue: actualMinAmount,
        maxValue: maxAmount,
        allowNegative: false, // Invoices typically should not have negative amounts
        errorMessage: errorMessage ?? 'Please enter a valid invoice amount',
      ),
    ]);
  }

  /// Creates a validation rule for tax amounts relative to invoice amounts
  static ValidationRule<String> taxAmount(
    double Function() getInvoiceAmount, {
    double maxPercentage = 100.0,
    String? errorMessage,
  }) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }

        // Clean the input (remove currency symbols, thousand separators, and whitespace)
        final cleanValue = value.replaceAll(RegExp(r'[$,\s]'), '');
        final taxAmount = double.tryParse(cleanValue);

        if (taxAmount == null) {
          return 'Please enter a valid tax amount';
        }

        if (taxAmount < 0) {
          return 'Tax amount cannot be negative';
        }

        final invoiceAmount = getInvoiceAmount();
        if (invoiceAmount <= 0) {
          return null; // Can't validate against invalid invoice amount
        }

        final percentage = (taxAmount / invoiceAmount) * 100;
        if (percentage > maxPercentage) {
          return errorMessage ??
              'Tax amount cannot exceed $maxPercentage% of invoice amount';
        }

        return null;
      },
      name: 'taxAmount(maxPercentage: $maxPercentage)',
    );
  }

  /// Creates a validation rule that ensures a vendor has been selected
  static ValidationRule<String> vendorSelected([
    String errorMessage = 'Please select a vendor',
  ]) {
    return BaseValidator.required(errorMessage);
  }

  /// Creates a validation rule that ensures an account has been selected
  static ValidationRule<String> accountSelected([
    String errorMessage = 'Please select an account',
  ]) {
    return BaseValidator.required(errorMessage);
  }

  /// Creates a Flutter-compatible validator function from a validation rule
  static String? Function(String?) createValidator(
    ValidationRule<String> rule,
  ) {
    return BaseValidator.createValidator(rule);
  }
}
