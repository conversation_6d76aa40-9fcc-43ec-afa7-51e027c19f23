import 'package:intl/intl.dart';
import 'validation_rule.dart';

/// Base validator class that provides common validation functions
class BaseValidator {
  /// Creates a validation rule that ensures a value is not null or empty
  static ValidationRule<String> required([String errorMessage = 'This field is required']) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return errorMessage;
        }
        return null;
      },
      name: 'required',
    );
  }

  /// Creates a validation rule that ensures a value matches a pattern
  static ValidationRule<String> pattern(
    RegExp pattern, [
    String errorMessage = 'Invalid format',
  ]) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        if (!pattern.hasMatch(value)) {
          return errorMessage;
        }
        return null;
      },
      name: 'pattern',
    );
  }

  /// Creates a validation rule that ensures a value has a minimum length
  static ValidationRule<String> minLength(
    int minLength, [
    String? errorMessage,
  ]) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        if (value.length < minLength) {
          return errorMessage ?? 'Minimum length is $minLength characters';
        }
        return null;
      },
      name: 'minLength($minLength)',
    );
  }

  /// Creates a validation rule that ensures a value has a maximum length
  static ValidationRule<String> maxLength(
    int maxLength, [
    String? errorMessage,
  ]) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        if (value.length > maxLength) {
          return errorMessage ?? 'Maximum length is $maxLength characters';
        }
        return null;
      },
      name: 'maxLength($maxLength)',
    );
  }

  /// Creates a validation rule that ensures a value is a valid email address
  static ValidationRule<String> email([
    String errorMessage = 'Please enter a valid email address',
  ]) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    return pattern(emailRegex, errorMessage);
  }

  /// Creates a validation rule that ensures a value is a valid numeric value
  static ValidationRule<String> numeric([
    String errorMessage = 'Please enter a valid number',
  ]) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        if (double.tryParse(value) == null) {
          return errorMessage;
        }
        return null;
      },
      name: 'numeric',
    );
  }

  /// Creates a validation rule that ensures a numeric value is within a range
  static ValidationRule<String> numericRange({
    double? min,
    double? max,
    bool allowNegative = true,
    String? errorMessage,
  }) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        
        final numValue = double.tryParse(value);
        if (numValue == null) {
          return 'Please enter a valid number';
        }
        
        if (!allowNegative && numValue < 0) {
          return 'Value cannot be negative';
        }
        
        if (min != null && numValue < min) {
          return errorMessage ?? 'Value must be at least $min';
        }
        
        if (max != null && numValue > max) {
          return errorMessage ?? 'Value must be at most $max';
        }
        
        return null;
      },
      name: 'numericRange(min: $min, max: $max, allowNegative: $allowNegative)',
    );
  }

  /// Creates a validation rule that ensures two values match
  static ValidationRule<String> matches(
    String Function() getCompareValue, [
    String errorMessage = 'Values do not match',
  ]) {
    return ValidationRule<String>(
      validate: (value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this case
        }
        
        final compareValue = getCompareValue();
        if (value != compareValue) {
          return errorMessage;
        }
        
        return null;
      },
      name: 'matches',
    );
  }

  /// Creates a validator function compatible with Flutter's TextFormField
  static String? Function(String?) createValidator(ValidationRule<String> rule) {
    return (String? value) => rule.call(value);
  }

  /// Creates a validator for password fields with customizable requirements
  static ValidationRule<String> password({
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = true,
    String? defaultErrorMessage,
  }) {
    return ValidationRule<String>(
      validate: (String? value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this if combined
        }

        final List<String> requirements = [];

        if (value.length < minLength) {
          requirements.add('at least $minLength characters');
        }
        if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
          requirements.add('an uppercase letter');
        }
        if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
          requirements.add('a lowercase letter');
        }
        if (requireNumbers && !RegExp(r'[0-9]').hasMatch(value)) {
          requirements.add('a number');
        }
        if (requireSpecialChars &&
            !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
          requirements.add('a special character');
        }

        if (requirements.isNotEmpty) {
          if (defaultErrorMessage != null) {
            return defaultErrorMessage;
          } else {
            return 'Password must contain ${requirements.join(', ')}';
          }
        }
        return null;
      },
      name:
          'password(minLength: $minLength, uppercase: $requireUppercase, lowercase: $requireLowercase, numbers: $requireNumbers, special: $requireSpecialChars)',
    );
  }

  /// Creates a validator for money/currency input
  static ValidationRule<String> moneyAmount({
    double? minValue,
    double? maxValue,
    bool allowNegative = false,
    String? errorMessage,
  }) {
    return ValidationRule<String>(
      validate: (String? value) {
        if (value == null || value.isEmpty) {
          return null; // Let required validator handle this if combined
        }

        // Clean the input (remove currency symbols and thousand separators)
        final cleanValue = value.replaceAll(RegExp(r'[$,\s]'), '');

        // Try to parse the cleaned value
        final number = double.tryParse(cleanValue);

        if (number == null) {
          return errorMessage ?? 'Please enter a valid amount';
        }

        // Check if negative values are allowed
        if (!allowNegative && number < 0) {
          return errorMessage ?? 'Amount cannot be negative';
        }

        // Check minimum value
        if (minValue != null && number < minValue) {
          return errorMessage ??
              'Amount must be at least ${_formatMoney(minValue)}';
        }

        // Check maximum value
        if (maxValue != null && number > maxValue) {
          return errorMessage ??
              'Amount cannot exceed ${_formatMoney(maxValue)}';
        }

        return null;
      },
      name:
          'moneyAmount(minValue: $minValue, maxValue: $maxValue, allowNegative: $allowNegative)',
    );
  }

  // Helper method to format money values
  static String _formatMoney(double value) {
    // Assuming default US locale for symbol, can be parameterized if needed
    final formatter = NumberFormat.currency(symbol: '\$');
    return formatter.format(value);
  }
}
