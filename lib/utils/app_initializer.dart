import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/config/app_config.dart';

/// Helper class to initialize app components, handle environment settings,
/// and ensure proper mock data configuration.
@singleton
class AppInitializer {
  final DatabaseService _databaseService;
  final AppConfig _appConfig;

  AppInitializer(this._databaseService, this._appConfig);

  /// Initialize the app with proper environment settings and mock mode if needed
  Future<void> initialize() async {
    try {
      debugPrint('Initializing application...');

      final mockMode = _shouldUseMockMode();

      if (mockMode) {
        debugPrint(
          'App running in MOCK MODE - using mock data for all operations',
        );
        _initializeMockMode();
      } else {
        debugPrint('App running in PRODUCTION MODE - using live database');
        _initializeProductionMode();
      }

      debugPrint('Application initialization complete');
    } catch (e, stackTrace) {
      debugPrint('Error during app initialization: $e');
      debugPrint('Stack trace: $stackTrace');

      // Fallback to mock mode if initialization fails in debug builds
      if (kDebugMode) {
        debugPrint('Falling back to mock mode due to initialization error');
        _initializeMockMode();
      } else {
        rethrow;
      }
    }
  }

  /// Determine if we should use mock mode based on app configuration and build mode
  bool _shouldUseMockMode() {
    // Use the app config to determine mock mode
    return _appConfig.shouldUseMockMode();
  }

  /// Initialize the app for mock mode
  void _initializeMockMode() {
    _databaseService.setMockMode(true);
    // Add any additional mock mode initializations here
  }

  /// Initialize the app for production mode
  void _initializeProductionMode() {
    _databaseService.setMockMode(false);
    // Add any additional production mode initializations here
  }
}
