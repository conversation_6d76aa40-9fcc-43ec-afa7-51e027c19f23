import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/services/logger.dart';

/// A centralized error handler for the application.
///
/// This class is responsible for:
/// - Logging errors with appropriate context
/// - Transforming technical errors into user-friendly messages
/// - Handling specific error types (Supabase, Network, etc.)
/// - Providing different messages for debug vs. production environments
@singleton
class ErrorHandler {
  final Logger _logger;

  ErrorHandler(this._logger);

  /// Handles any error and returns a user-friendly message.
  ///
  /// This method:
  /// - Logs the error with stack trace
  /// - Identifies the error type
  /// - Returns an appropriate user-friendly message
  ///
  /// In debug mode, it returns more technical details.
  /// In production mode, it returns generic user-friendly messages.
  ///
  /// @param error The error to handle
  /// @return A user-friendly error message
  String handleError(dynamic error) {
    // Log the error
    _logger.error('Error occurred', error, StackTrace.current);

    // Handle specific error types
    if (error is PostgrestException) {
      return _handleSupabaseError(error);
    } else if (error is NetworkException) {
      return 'Network error. Please check your connection and try again.';
    } else if (error is BusinessException) {
      return error.message;
    } else if (error is UnsupportedError) {
      throw error; // Re-throw UnsupportedError without transformation
    } else {
      // Generic error handling
      return kDebugMode
          ? error.toString()
          : 'An unexpected error occurred. Please try again later.';
    }
  }

  /// Handles Supabase specific errors.
  ///
  /// This method maps Supabase error codes to user-friendly messages.
  ///
  /// @param error The Supabase error to handle
  /// @return A user-friendly error message
  String _handleSupabaseError(PostgrestException error) {
    // Handle specific Supabase error codes
    if (error.code == 'PGRST116') {
      return 'Resource not found.';
    } else if (error.code == 'PGRST109') {
      return 'Database conflict. The operation could not be completed.';
    } else if (error.code == '23505') {
      return 'A record with this information already exists.';
    } else if (error.code == '23503') {
      return 'This operation would violate database constraints.';
    } else if (error.message.contains('JWT')) {
      return 'Your session has expired. Please log in again.';
    } else {
      // Generic Supabase error
      return kDebugMode
          ? 'Database error: ${error.message} (Code: ${error.code})'
          : 'A database error occurred. Please try again later.';
    }
  }
}

/// Custom exception for network-related errors.
///
/// Use this exception for:
/// - Connection issues
/// - Timeouts
/// - Other network-related problems
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);

  @override
  String toString() => message;
}
