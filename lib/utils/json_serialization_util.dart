/// Utility class to provide consistent JSON serialization helpers
class JsonSerializationUtil {
  /// Converts a camelCase key to snake_case
  static String camelToSnake(String input) {
    return input.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// Converts a snake_case key to camelCase
  static String snakeToCamel(String input) {
    return input.replaceAllMapped(
      RegExp(r'_([a-z])'),
      (match) => match.group(1)!.toUpperCase(),
    );
  }

  /// Converts an entire Map from camelCase keys to snake_case keys
  static Map<String, dynamic> convertMapKeysToSnakeCase(
    Map<String, dynamic> input,
  ) {
    final result = <String, dynamic>{};

    input.forEach((key, value) {
      final snakeKey = camelToSnake(key);

      // Handle nested maps
      if (value is Map<String, dynamic>) {
        result[snakeKey] = convertMapKeysToSnakeCase(value);
      }
      // Handle lists that might contain maps
      else if (value is List) {
        result[snakeKey] =
            value.map((item) {
              if (item is Map<String, dynamic>) {
                return convertMapKeysToSnakeCase(item);
              }
              return item;
            }).toList();
      }
      // Handle DateTime objects
      else if (value is DateTime) {
        result[snakeKey] = value.toIso8601String();
      }
      // Pass through other values
      else {
        result[snakeKey] = value;
      }
    });

    return result;
  }

  /// Converts an entire Map from snake_case keys to camelCase keys
  static Map<String, dynamic> convertMapKeysToCamelCase(
    Map<String, dynamic> input,
  ) {
    final result = <String, dynamic>{};

    input.forEach((key, value) {
      final camelKey = snakeToCamel(key);

      // Handle nested maps
      if (value is Map<String, dynamic>) {
        result[camelKey] = convertMapKeysToCamelCase(value);
      }
      // Handle lists that might contain maps
      else if (value is List) {
        result[camelKey] =
            value.map((item) {
              if (item is Map<String, dynamic>) {
                return convertMapKeysToCamelCase(item);
              }
              return item;
            }).toList();
      }
      // Parse ISO date strings to DateTime
      else if (value is String && _isIsoDateString(value)) {
        try {
          result[camelKey] = DateTime.parse(value);
        } catch (_) {
          result[camelKey] = value;
        }
      }
      // Pass through other values
      else {
        result[camelKey] = value;
      }
    });

    return result;
  }

  /// Check if a string appears to be an ISO date string
  static bool _isIsoDateString(String input) {
    // Simple check for ISO 8601 date string format
    return RegExp(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}').hasMatch(input);
  }
}
