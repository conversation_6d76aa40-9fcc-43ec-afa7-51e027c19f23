import 'dart:async';

/// A utility class to add debounce functionality to repeated operations.
///
/// This is useful for operations like search where we want to wait for the user
/// to finish typing before executing a potentially expensive operation.
class Debouncer {
  /// The duration to wait before executing the callback
  final int milliseconds;

  /// The timer that will be reset on each call
  Timer? _timer;

  /// Creates a new debouncer with the specified delay in milliseconds
  Debouncer({required this.milliseconds});

  /// Run the callback after the debounce period
  ///
  /// If this method is called again before the debounce period ends,
  /// the timer is reset and the previous callback will not be executed.
  void run(VoidCallback callback) {
    // Cancel the previous timer if it exists
    _timer?.cancel();

    // Set a new timer
    _timer = Timer(Duration(milliseconds: milliseconds), callback);
  }

  /// Cancel the current debounce timer if it exists
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }
}

/// Callback function type alias
typedef VoidCallback = void Function();
