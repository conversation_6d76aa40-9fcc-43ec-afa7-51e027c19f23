import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:we_like_money/config/supabase_config.dart';
import 'package:we_like_money/config/app_config.dart';

import 'injection.config.dart';

final getIt = GetIt.instance;

/// Configures all dependencies for the application
@InjectableInit(
  initializerName: 'init', // default
  preferRelativeImports: true, // default
  asExtension: false, // default
)
Future<void> configureDependencies({
  SupabaseClientProvider? testProvider,
  Map<Type, Object>? testProviders,
  bool isTest = false,
}) async {
  if (isTest) {
    // Reset GetIt instance
    await GetIt.instance.reset();
  }

  // Initialize dependencies
  init(GetIt.instance);

  // Register enableMockCreation value
  if (!GetIt.instance.isRegistered<bool>()) {
    GetIt.instance.registerSingleton<bool>(false);
  }

  if (isTest && testProvider != null) {
    // Replace the default Supabase provider with the test provider
    if (GetIt.instance.isRegistered<SupabaseClientProvider>()) {
      GetIt.instance.unregister<SupabaseClientProvider>();
    }
    GetIt.instance.registerSingleton<SupabaseClientProvider>(testProvider);

    // Register test providers if provided
    if (testProviders != null) {
      for (final entry in testProviders.entries) {
        final value = entry.value;
        if (GetIt.instance.isRegistered<AppConfig>()) {
          GetIt.instance.unregister<AppConfig>();
        }
        GetIt.instance.registerSingleton<AppConfig>(value as AppConfig);
      }
    }
  }
}
