// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../config/app_config.dart' as _i650;
import '../config/supabase_config.dart' as _i1061;
import '../services/database_service.dart' as _i665;
import '../services/general_ledger/expense_ledger_service.dart' as _i414;
import '../services/general_ledger/vendor_invoice_ledger_service.dart'
    as _i1038;
import '../services/logger.dart' as _i432;
import '../services/supabase/supabase_database_facade.dart' as _i212;
import '../utils/app_initializer.dart' as _i348;
import '../utils/error_handler.dart' as _i383;
import '../viewmodels/account_viewmodel.dart' as _i72;
import '../viewmodels/company_viewmodel.dart' as _i158;
import '../viewmodels/currency_viewmodel.dart' as _i532;
import '../viewmodels/expense_viewmodel.dart' as _i330;
import '../viewmodels/general_ledger_viewmodel.dart' as _i228;
import '../viewmodels/project_viewmodel.dart' as _i95;
import '../viewmodels/staff_member_viewmodel.dart' as _i571;
import '../viewmodels/vendor_invoice_viewmodel.dart' as _i366;
import '../viewmodels/vendor_viewmodel.dart' as _i463;

// initializes the registration of main-scope dependencies inside of GetIt
_i174.GetIt init(
  _i174.GetIt getIt, {
  String? environment,
  _i526.EnvironmentFilter? environmentFilter,
}) {
  final gh = _i526.GetItHelper(getIt, environment, environmentFilter);
  gh.singleton<_i1061.SupabaseClientProvider>(
    () => _i1061.SupabaseClientProvider(),
  );
  gh.singleton<_i650.AppConfig>(() => _i650.AppConfig());
  gh.factory<_i432.Logger>(
    () => _i432.Logger(print: gh<_i432.PrintFunction>(), debugMode: gh<bool>()),
  );
  gh.singleton<_i383.ErrorHandler>(
    () => _i383.ErrorHandler(gh<_i432.Logger>()),
  );
  gh.factory<_i665.DatabaseService>(
    () => _i212.SupabaseDatabaseFacade(
      gh<_i1061.SupabaseClientProvider>(),
      gh<_i383.ErrorHandler>(),
      useMockData: gh<bool>(),
    ),
  );
  gh.singleton<_i348.AppInitializer>(
    () => _i348.AppInitializer(
      gh<_i665.DatabaseService>(),
      gh<_i650.AppConfig>(),
    ),
  );
  gh.factory<_i1038.VendorInvoiceLedgerService>(
    () => _i1038.VendorInvoiceLedgerService(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
      enableMockCreation: gh<bool>(),
    ),
  );
  gh.factory<_i158.CompanyViewModel>(
    () => _i158.CompanyViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i532.CurrencyViewModel>(
    () => _i532.CurrencyViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i228.GeneralLedgerViewModel>(
    () => _i228.GeneralLedgerViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i95.ProjectViewModel>(
    () => _i95.ProjectViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i72.AccountViewModel>(
    () => _i72.AccountViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i463.VendorViewModel>(
    () => _i463.VendorViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i571.StaffMemberViewModel>(
    () => _i571.StaffMemberViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i414.ExpenseLedgerService>(
    () => _i414.ExpenseLedgerService(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  gh.factory<_i330.ExpenseViewModel>(
    () => _i330.ExpenseViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i383.ErrorHandler>(),
      gh<_i414.ExpenseLedgerService>(),
    ),
  );
  gh.factory<_i366.VendorInvoiceViewModel>(
    () => _i366.VendorInvoiceViewModel(
      gh<_i665.DatabaseService>(),
      gh<_i1038.VendorInvoiceLedgerService>(),
      gh<_i383.ErrorHandler>(),
    ),
  );
  return getIt;
}
