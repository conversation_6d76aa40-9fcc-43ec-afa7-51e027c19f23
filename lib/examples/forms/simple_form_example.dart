import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/index.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A simple example of using the form component library
class SimpleFormExample extends StatefulWidget {
  const SimpleFormExample({super.key});

  @override
  State<SimpleFormExample> createState() => _SimpleFormExampleState();
}

class _SimpleFormExampleState extends State<SimpleFormExample> {
  // Form state
  final formStateManager = FormStateManager();
  
  // Form values
  String name = '';
  String email = '';
  DateTime birthDate = DateTime.now().subtract(const Duration(days: 365 * 30));
  String? selectedCountry;
  
  // List of countries for dropdown
  final List<String> countries = [
    'United States',
    'Canada',
    'United Kingdom',
    'Australia',
    'Germany',
    'France',
    'Japan',
    'Brazil',
    'India',
  ];
  
  @override
  void initState() {
    super.initState();
    
    // Set original values
    formStateManager.setOriginalValue('name', name);
    formStateManager.setOriginalValue('email', email);
    formStateManager.setOriginalValue('birthDate', birthDate);
    formStateManager.setOriginalValue('country', selectedCountry);
  }
  
  @override
  void dispose() {
    formStateManager.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Simple Form Example'),
      ),
      body: _buildForm(),
    );
  }
  
  Widget _buildForm() {
    // Create a form builder
    final formBuilder = FormBuilder(formKey: formStateManager.formKey);
    
    // Build the form
    return formBuilder
      .addSection(_buildPersonalInfoSection())
      .addSection(_buildContactInfoSection())
      .addSection(_buildSubmitSection())
      .buildScrollable(context);
  }
  
  Widget _buildPersonalInfoSection() {
    return _PersonalInfoSection(
      name: name,
      birthDate: birthDate,
      onNameChanged: (value) {
        setState(() {
          name = value;
          formStateManager.setCurrentValue('name', value);
        });
      },
      onBirthDateChanged: (value) {
        setState(() {
          birthDate = value;
          formStateManager.setCurrentValue('birthDate', value);
        });
      },
    );
  }
  
  Widget _buildContactInfoSection() {
    return _ContactInfoSection(
      email: email,
      selectedCountry: selectedCountry,
      countries: countries,
      onEmailChanged: (value) {
        setState(() {
          email = value;
          formStateManager.setCurrentValue('email', value);
        });
      },
      onCountryChanged: (value) {
        setState(() {
          selectedCountry = value;
          formStateManager.setCurrentValue('country', value);
        });
      },
    );
  }
  
  Widget _buildSubmitSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Form status
          ValueListenableBuilder(
            valueListenable: formStateManager.isDirty,
            builder: (context, isDirty, _) {
              return ValueListenableBuilder(
                valueListenable: formStateManager.errorMessage,
                builder: (context, errorMessage, _) {
                  return ValueListenableBuilder(
                    valueListenable: formStateManager.successMessage,
                    builder: (context, successMessage, _) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (isDirty)
                            const Text(
                              'You have unsaved changes',
                              style: TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          if (errorMessage != null)
                            Text(
                              errorMessage,
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          if (successMessage != null)
                            Text(
                              successMessage,
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                        ],
                      );
                    },
                  );
                },
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Submit and reset buttons
          Row(
            children: [
              Expanded(
                child: ValueListenableBuilder(
                  valueListenable: formStateManager.isLoading,
                  builder: (context, isLoading, _) {
                    return ElevatedButton(
                      onPressed: isLoading ? null : _submitForm,
                      child: isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Submit'),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetForm,
                  child: const Text('Reset'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Future<void> _submitForm() async {
    await formStateManager.submit(() async {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Print form values
      debugPrint('Form submitted with values:');
      debugPrint('Name: $name');
      debugPrint('Email: $email');
      debugPrint('Birth Date: $birthDate');
      debugPrint('Country: $selectedCountry');
    });
  }
  
  void _resetForm() {
    formStateManager.reset();
    
    setState(() {
      name = formStateManager.getCurrentValue('name') ?? '';
      email = formStateManager.getCurrentValue('email') ?? '';
      birthDate = formStateManager.getCurrentValue('birthDate') ?? DateTime.now();
      selectedCountry = formStateManager.getCurrentValue('country');
    });
  }
}

/// A form section for personal information
class _PersonalInfoSection extends FormSection {
  final String name;
  final DateTime birthDate;
  final ValueChanged<String> onNameChanged;
  final ValueChanged<DateTime> onBirthDateChanged;
  
  const _PersonalInfoSection({
    required this.name,
    required this.birthDate,
    required this.onNameChanged,
    required this.onBirthDateChanged,
  }) : super(
          title: 'Personal Information',
          helpText: 'Enter your personal details',
          headerIcon: Icons.person,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      children: [
        AppTextFormField(
          labelText: 'Name',
          value: name,
          onChanged: onNameChanged,
          isRequired: true,
          validationRule: Validator.required('Name is required'),
          prefixIcon: Icons.person,
        ),
        const SizedBox(height: 16),
        AppDateFormField(
          labelText: 'Birth Date',
          value: birthDate,
          onChanged: onBirthDateChanged,
          isRequired: true,
          validationRule: Validator.all([
            Validator.required('Birth date is required'),
            Validator.dateBefore(
              DateTime.now(),
              'yyyy-MM-dd',
              'Birth date must be in the past',
            ),
          ]),
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
        ),
      ],
    );
  }
}

/// A form section for contact information
class _ContactInfoSection extends FormSection {
  final String email;
  final String? selectedCountry;
  final List<String> countries;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<String?> onCountryChanged;
  
  const _ContactInfoSection({
    required this.email,
    required this.selectedCountry,
    required this.countries,
    required this.onEmailChanged,
    required this.onCountryChanged,
  }) : super(
          title: 'Contact Information',
          helpText: 'Enter your contact details',
          headerIcon: Icons.contact_mail,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      children: [
        AppTextFormField.email(
          value: email,
          onChanged: onEmailChanged,
        ),
        const SizedBox(height: 16),
        AppDropdownFormField.optional(
          value: selectedCountry,
          onChanged: onCountryChanged,
          items: countries,
          displayStringBuilder: (country) => country,
          labelText: 'Country',
          prefixIcon: Icons.flag,
        ),
      ],
    );
  }
}
