import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_entry_screen.dart';

/// Example screen to demonstrate the refactored vendor invoice form
class VendorInvoiceFormExample extends StatelessWidget {
  /// Constructor
  const VendorInvoiceFormExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Vendor Invoice Form Example')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This example demonstrates the refactored vendor invoice form using the new form component library.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder:
                          (context) => const ProviderScope(
                            child: VendorInvoiceEntryScreen(),
                          ),
                    ),
                  );
                },
                child: const Text('Open Refactored Form'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
