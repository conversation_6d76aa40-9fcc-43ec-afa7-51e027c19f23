import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/ui/features/expenses/expense_entry_screen_refactored.dart';

/// Example screen to demonstrate the refactored expense form
class ExpenseFormExample extends StatelessWidget {
  /// Constructor
  const ExpenseFormExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Expense Form Example')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'This example demonstrates the refactored expense form using the new form component library.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder:
                          (context) => const ProviderScope(
                            child: ExpenseEntryScreenRefactored(),
                          ),
                    ),
                  );
                },
                child: const Text('Open Refactored Form'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
