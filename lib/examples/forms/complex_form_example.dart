import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/index.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A complex example of using the form component library with conditional fields and validation
class ComplexFormExample extends StatefulWidget {
  const ComplexFormExample({super.key});

  @override
  State<ComplexFormExample> createState() => _ComplexFormExampleState();
}

class _ComplexFormExampleState extends State<ComplexFormExample> {
  // Form state
  final formStateManager = FormStateManager();
  
  // Invoice details
  String invoiceNumber = '';
  DateTime invoiceDate = DateTime.now();
  DateTime dueDate = DateTime.now().add(const Duration(days: 30));
  String amount = '';
  String taxAmount = '';
  
  // Vendor details
  String? selectedVendorId;
  bool createNewVendor = false;
  String newVendorName = '';
  String newVendorEmail = '';
  
  // Payment details
  String? selectedPaymentMethod;
  String creditCardNumber = '';
  
  // Lists for dropdowns
  final List<Map<String, dynamic>> vendors = [
    {'id': '1', 'name': 'Acme Corp'},
    {'id': '2', 'name': 'Globex Inc'},
    {'id': '3', 'name': 'Initech'},
  ];
  
  final List<String> paymentMethods = [
    'Cash',
    'Check',
    'Credit Card',
    'Bank Transfer',
  ];
  
  @override
  void initState() {
    super.initState();
    
    // Set original values
    formStateManager.setOriginalValue('invoiceNumber', invoiceNumber);
    formStateManager.setOriginalValue('invoiceDate', invoiceDate);
    formStateManager.setOriginalValue('dueDate', dueDate);
    formStateManager.setOriginalValue('amount', amount);
    formStateManager.setOriginalValue('taxAmount', taxAmount);
    formStateManager.setOriginalValue('selectedVendorId', selectedVendorId);
    formStateManager.setOriginalValue('createNewVendor', createNewVendor);
    formStateManager.setOriginalValue('newVendorName', newVendorName);
    formStateManager.setOriginalValue('newVendorEmail', newVendorEmail);
    formStateManager.setOriginalValue('selectedPaymentMethod', selectedPaymentMethod);
    formStateManager.setOriginalValue('creditCardNumber', creditCardNumber);
    
    // Add field dependencies
    formStateManager.addFieldDependency('dueDate', 'invoiceDate');
    formStateManager.addFieldDependency('newVendorName', 'createNewVendor');
    formStateManager.addFieldDependency('newVendorEmail', 'createNewVendor');
    formStateManager.addFieldDependency('creditCardNumber', 'selectedPaymentMethod');
    
    // Add field listeners
    formStateManager.addFieldListener('invoiceDate', _updateDueDate);
    formStateManager.addFieldListener('createNewVendor', _handleVendorTypeChange);
    formStateManager.addFieldListener('selectedPaymentMethod', _handlePaymentMethodChange);
  }
  
  void _updateDueDate() {
    final newInvoiceDate = formStateManager.getCurrentValue<DateTime>('invoiceDate');
    if (newInvoiceDate != null) {
      setState(() {
        dueDate = newInvoiceDate.add(const Duration(days: 30));
        formStateManager.setCurrentValue('dueDate', dueDate);
      });
    }
  }
  
  void _handleVendorTypeChange() {
    final newCreateNewVendor = formStateManager.getCurrentValue<bool>('createNewVendor') ?? false;
    if (newCreateNewVendor) {
      setState(() {
        selectedVendorId = null;
        formStateManager.setCurrentValue('selectedVendorId', null);
      });
    } else {
      setState(() {
        newVendorName = '';
        newVendorEmail = '';
        formStateManager.setCurrentValue('newVendorName', '');
        formStateManager.setCurrentValue('newVendorEmail', '');
      });
    }
  }
  
  void _handlePaymentMethodChange() {
    final newPaymentMethod = formStateManager.getCurrentValue<String>('selectedPaymentMethod');
    if (newPaymentMethod != 'Credit Card') {
      setState(() {
        creditCardNumber = '';
        formStateManager.setCurrentValue('creditCardNumber', '');
      });
    }
  }
  
  @override
  void dispose() {
    formStateManager.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complex Form Example'),
      ),
      body: _buildForm(),
    );
  }
  
  Widget _buildForm() {
    // Create a form builder
    final formBuilder = FormBuilder(formKey: formStateManager.formKey);
    
    // Build the form
    return formBuilder
      .addSection(_buildInvoiceDetailsSection())
      .addSection(_buildVendorSection())
      .addSection(_buildPaymentSection())
      .addSection(_buildSubmitSection())
      .buildScrollable(context);
  }
  
  Widget _buildInvoiceDetailsSection() {
    return _InvoiceDetailsSection(
      invoiceNumber: invoiceNumber,
      invoiceDate: invoiceDate,
      dueDate: dueDate,
      amount: amount,
      taxAmount: taxAmount,
      onInvoiceNumberChanged: (value) {
        setState(() {
          invoiceNumber = value;
          formStateManager.setCurrentValue('invoiceNumber', value);
        });
      },
      onInvoiceDateChanged: (value) {
        setState(() {
          invoiceDate = value;
          formStateManager.setCurrentValue('invoiceDate', value);
        });
      },
      onDueDateChanged: (value) {
        setState(() {
          dueDate = value;
          formStateManager.setCurrentValue('dueDate', value);
        });
      },
      onAmountChanged: (value) {
        setState(() {
          amount = value;
          formStateManager.setCurrentValue('amount', value);
        });
      },
      onTaxAmountChanged: (value) {
        setState(() {
          taxAmount = value;
          formStateManager.setCurrentValue('taxAmount', value);
        });
      },
    );
  }
  
  Widget _buildVendorSection() {
    return _VendorSection(
      selectedVendorId: selectedVendorId,
      vendors: vendors,
      createNewVendor: createNewVendor,
      newVendorName: newVendorName,
      newVendorEmail: newVendorEmail,
      onVendorChanged: (value) {
        setState(() {
          selectedVendorId = value;
          formStateManager.setCurrentValue('selectedVendorId', value);
        });
      },
      onCreateNewVendorChanged: (value) {
        setState(() {
          createNewVendor = value;
          formStateManager.setCurrentValue('createNewVendor', value);
        });
      },
      onNewVendorNameChanged: (value) {
        setState(() {
          newVendorName = value;
          formStateManager.setCurrentValue('newVendorName', value);
        });
      },
      onNewVendorEmailChanged: (value) {
        setState(() {
          newVendorEmail = value;
          formStateManager.setCurrentValue('newVendorEmail', value);
        });
      },
    );
  }
  
  Widget _buildPaymentSection() {
    return _PaymentSection(
      selectedPaymentMethod: selectedPaymentMethod,
      paymentMethods: paymentMethods,
      creditCardNumber: creditCardNumber,
      onPaymentMethodChanged: (value) {
        setState(() {
          selectedPaymentMethod = value;
          formStateManager.setCurrentValue('selectedPaymentMethod', value);
        });
      },
      onCreditCardNumberChanged: (value) {
        setState(() {
          creditCardNumber = value;
          formStateManager.setCurrentValue('creditCardNumber', value);
        });
      },
    );
  }
  
  Widget _buildSubmitSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Form status
          ValueListenableBuilder(
            valueListenable: formStateManager.isDirty,
            builder: (context, isDirty, _) {
              return ValueListenableBuilder(
                valueListenable: formStateManager.errorMessage,
                builder: (context, errorMessage, _) {
                  return ValueListenableBuilder(
                    valueListenable: formStateManager.successMessage,
                    builder: (context, successMessage, _) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (isDirty)
                            const Text(
                              'You have unsaved changes',
                              style: TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          if (errorMessage != null)
                            Text(
                              errorMessage,
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          if (successMessage != null)
                            Text(
                              successMessage,
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                        ],
                      );
                    },
                  );
                },
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Submit and reset buttons
          Row(
            children: [
              Expanded(
                child: ValueListenableBuilder(
                  valueListenable: formStateManager.isLoading,
                  builder: (context, isLoading, _) {
                    return ElevatedButton(
                      onPressed: isLoading ? null : _submitForm,
                      child: isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Submit Invoice'),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetForm,
                  child: const Text('Reset'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Future<void> _submitForm() async {
    await formStateManager.submit(() async {
      // Validate vendor selection
      if (!createNewVendor && selectedVendorId == null) {
        throw Exception('Please select a vendor or create a new one');
      }
      
      if (createNewVendor && (newVendorName.isEmpty || newVendorEmail.isEmpty)) {
        throw Exception('Please enter vendor name and email');
      }
      
      // Validate payment method
      if (selectedPaymentMethod == null) {
        throw Exception('Please select a payment method');
      }
      
      if (selectedPaymentMethod == 'Credit Card' && creditCardNumber.isEmpty) {
        throw Exception('Please enter a credit card number');
      }
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Print form values
      debugPrint('Invoice submitted with values:');
      debugPrint('Invoice Number: $invoiceNumber');
      debugPrint('Invoice Date: $invoiceDate');
      debugPrint('Due Date: $dueDate');
      debugPrint('Amount: $amount');
      debugPrint('Tax Amount: $taxAmount');
      
      if (createNewVendor) {
        debugPrint('New Vendor: $newVendorName ($newVendorEmail)');
      } else {
        final vendor = vendors.firstWhere((v) => v['id'] == selectedVendorId);
        debugPrint('Vendor: ${vendor['name']} (ID: ${vendor['id']})');
      }
      
      debugPrint('Payment Method: $selectedPaymentMethod');
      if (selectedPaymentMethod == 'Credit Card') {
        debugPrint('Credit Card Number: $creditCardNumber');
      }
    });
  }
  
  void _resetForm() {
    formStateManager.reset();
    
    setState(() {
      invoiceNumber = formStateManager.getCurrentValue('invoiceNumber') ?? '';
      invoiceDate = formStateManager.getCurrentValue('invoiceDate') ?? DateTime.now();
      dueDate = formStateManager.getCurrentValue('dueDate') ?? DateTime.now().add(const Duration(days: 30));
      amount = formStateManager.getCurrentValue('amount') ?? '';
      taxAmount = formStateManager.getCurrentValue('taxAmount') ?? '';
      selectedVendorId = formStateManager.getCurrentValue('selectedVendorId');
      createNewVendor = formStateManager.getCurrentValue('createNewVendor') ?? false;
      newVendorName = formStateManager.getCurrentValue('newVendorName') ?? '';
      newVendorEmail = formStateManager.getCurrentValue('newVendorEmail') ?? '';
      selectedPaymentMethod = formStateManager.getCurrentValue('selectedPaymentMethod');
      creditCardNumber = formStateManager.getCurrentValue('creditCardNumber') ?? '';
    });
  }
}

/// A form section for invoice details
class _InvoiceDetailsSection extends FormSection {
  final String invoiceNumber;
  final DateTime invoiceDate;
  final DateTime dueDate;
  final String amount;
  final String taxAmount;
  final ValueChanged<String> onInvoiceNumberChanged;
  final ValueChanged<DateTime> onInvoiceDateChanged;
  final ValueChanged<DateTime> onDueDateChanged;
  final ValueChanged<String> onAmountChanged;
  final ValueChanged<String> onTaxAmountChanged;
  
  const _InvoiceDetailsSection({
    required this.invoiceNumber,
    required this.invoiceDate,
    required this.dueDate,
    required this.amount,
    required this.taxAmount,
    required this.onInvoiceNumberChanged,
    required this.onInvoiceDateChanged,
    required this.onDueDateChanged,
    required this.onAmountChanged,
    required this.onTaxAmountChanged,
  }) : super(
          title: 'Invoice Details',
          helpText: 'Enter the invoice details',
          headerIcon: Icons.receipt,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      children: [
        // Invoice number
        AppTextFormField(
          labelText: 'Invoice Number',
          value: invoiceNumber,
          onChanged: onInvoiceNumberChanged,
          isRequired: true,
          validationRule: Validator.invoiceNumber(),
          prefixIcon: Icons.numbers,
        ),
        const SizedBox(height: 16),
        
        // Invoice date and due date
        Row(
          children: [
            Expanded(
              child: AppDateFormField(
                labelText: 'Invoice Date',
                value: invoiceDate,
                onChanged: onInvoiceDateChanged,
                isRequired: true,
                validationRule: Validator.required('Invoice date is required'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AppDateFormField.withMinDate(
                labelText: 'Due Date',
                value: dueDate,
                onChanged: onDueDateChanged,
                minDate: invoiceDate,
                isRequired: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Amount and tax amount
        Row(
          children: [
            Expanded(
              child: AppCurrencyFormField.invoiceAmount(
                labelText: 'Amount',
                value: amount,
                onChanged: onAmountChanged,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AppCurrencyFormField.taxAmount(
                labelText: 'Tax Amount',
                value: taxAmount,
                onChanged: onTaxAmountChanged,
                invoiceAmount: amount,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// A form section for vendor selection
class _VendorSection extends FormSection {
  final String? selectedVendorId;
  final List<Map<String, dynamic>> vendors;
  final bool createNewVendor;
  final String newVendorName;
  final String newVendorEmail;
  final ValueChanged<String?> onVendorChanged;
  final ValueChanged<bool> onCreateNewVendorChanged;
  final ValueChanged<String> onNewVendorNameChanged;
  final ValueChanged<String> onNewVendorEmailChanged;
  
  const _VendorSection({
    required this.selectedVendorId,
    required this.vendors,
    required this.createNewVendor,
    required this.newVendorName,
    required this.newVendorEmail,
    required this.onVendorChanged,
    required this.onCreateNewVendorChanged,
    required this.onNewVendorNameChanged,
    required this.onNewVendorEmailChanged,
  }) : super(
          title: 'Vendor',
          helpText: 'Select a vendor or create a new one',
          headerIcon: Icons.business,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Vendor type selection
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('Select Existing Vendor'),
                value: false,
                groupValue: createNewVendor,
                onChanged: (value) {
                  if (value != null) {
                    onCreateNewVendorChanged(value);
                  }
                },
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('Create New Vendor'),
                value: true,
                groupValue: createNewVendor,
                onChanged: (value) {
                  if (value != null) {
                    onCreateNewVendorChanged(value);
                  }
                },
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Conditional fields based on vendor type
        if (!createNewVendor)
          AppDropdownFormField<String>(
            labelText: 'Vendor',
            value: selectedVendorId,
            onChanged: onVendorChanged,
            items: vendors.map((v) => v['id'] as String).toList(),
            displayStringBuilder: (id) => vendors
                .firstWhere((v) => v['id'] == id)['name'] as String,
            isRequired: true,
            validationRule: Validator.required('Please select a vendor'),
            prefixIcon: Icons.business,
          )
        else
          Column(
            children: [
              AppTextFormField(
                labelText: 'Vendor Name',
                value: newVendorName,
                onChanged: onNewVendorNameChanged,
                isRequired: true,
                validationRule: Validator.required('Vendor name is required'),
                prefixIcon: Icons.business,
              ),
              const SizedBox(height: 16),
              AppTextFormField.email(
                value: newVendorEmail,
                onChanged: onNewVendorEmailChanged,
                labelText: 'Vendor Email',
              ),
            ],
          ),
      ],
    );
  }
}

/// A form section for payment details
class _PaymentSection extends FormSection {
  final String? selectedPaymentMethod;
  final List<String> paymentMethods;
  final String creditCardNumber;
  final ValueChanged<String?> onPaymentMethodChanged;
  final ValueChanged<String> onCreditCardNumberChanged;
  
  const _PaymentSection({
    required this.selectedPaymentMethod,
    required this.paymentMethods,
    required this.creditCardNumber,
    required this.onPaymentMethodChanged,
    required this.onCreditCardNumberChanged,
  }) : super(
          title: 'Payment Details',
          helpText: 'Enter payment information',
          headerIcon: Icons.payment,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      children: [
        // Payment method
        AppDropdownFormField<String>(
          labelText: 'Payment Method',
          value: selectedPaymentMethod,
          onChanged: onPaymentMethodChanged,
          items: paymentMethods,
          displayStringBuilder: (method) => method,
          isRequired: true,
          validationRule: Validator.required('Please select a payment method'),
          prefixIcon: Icons.payment,
        ),
        
        // Conditional credit card field
        if (selectedPaymentMethod == 'Credit Card') ...[
          const SizedBox(height: 16),
          AppTextFormField(
            labelText: 'Credit Card Number',
            value: creditCardNumber,
            onChanged: onCreditCardNumberChanged,
            isRequired: true,
            validationRule: Validator.all([
              Validator.required('Credit card number is required'),
              Validator.pattern(
                RegExp(r'^\d{13,19}$'),
                'Please enter a valid credit card number',
              ),
            ]),
            prefixIcon: Icons.credit_card,
            keyboardType: TextInputType.number,
          ),
        ],
      ],
    );
  }
}
