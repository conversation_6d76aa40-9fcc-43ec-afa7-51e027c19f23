import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/providers/account_provider.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/providers/staff_member_provider.dart';
import 'package:we_like_money/providers/vendor_invoice_provider.dart';
import 'package:we_like_money/providers/vendor_provider.dart'
    as vendor_provider;
import 'package:we_like_money/utils/debouncer.dart';

/// View model for the vendor invoice entry screen
class VendorInvoiceViewModel {
  /// The reference to the provider scope
  final WidgetRef ref;

  /// The invoice being edited, or null if creating a new invoice
  final VendorInvoice? vendorInvoice;

  /// Form key for validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  /// Form field controllers
  final TextEditingController vendorSearchController = TextEditingController();
  final TextEditingController invoiceNumberController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController taxAmountController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  /// State values
  final ValueNotifier<Vendor?> selectedVendor = ValueNotifier(null);
  final ValueNotifier<Account?> selectedAccount = ValueNotifier(null);
  final ValueNotifier<Project?> selectedProject = ValueNotifier(null);
  final ValueNotifier<StaffMember?> selectedStaffMember = ValueNotifier(null);
  final ValueNotifier<DateTime> invoiceDate = ValueNotifier(DateTime.now());
  final ValueNotifier<DateTime> dueDate = ValueNotifier(
    DateTime.now().add(const Duration(days: 30)),
  );
  final ValueNotifier<String> currency = ValueNotifier('USD');

  /// Loading and error state
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<bool> isError = ValueNotifier(false);
  final ValueNotifier<String> errorMessage = ValueNotifier('');

  /// Search state
  final ValueNotifier<String> vendorSearchQuery = ValueNotifier('');
  final ValueNotifier<bool> isSearching = ValueNotifier(false);

  /// Debouncer for vendor search
  final Debouncer debouncer = Debouncer(milliseconds: 500);

  /// Whether this screen is in edit mode
  bool get isEditMode => vendorInvoice != null;

  /// Constructor
  VendorInvoiceViewModel(this.ref, this.vendorInvoice) {
    _initializeForm();
    _setupListeners();
  }

  /// Initialize the form with existing data if in edit mode
  void _initializeForm() {
    if (isEditMode && vendorInvoice != null) {
      // Set invoice number
      invoiceNumberController.text = vendorInvoice!.invoiceNumber;

      // Set amount
      amountController.text = vendorInvoice!.amount.toString();

      // Set tax amount if available
      if (vendorInvoice!.taxAmount != null) {
        taxAmountController.text = vendorInvoice!.taxAmount.toString();
      }

      // Set dates
      invoiceDate.value = vendorInvoice!.invoiceDate;
      dueDate.value = vendorInvoice!.dueDate;

      // Set currency
      currency.value = vendorInvoice!.currencyCode;

      // Load vendor data if available
      if (vendorInvoice!.vendorId.isNotEmpty) {
        ref
            .read(vendor_provider.vendorByIdProvider(vendorInvoice!.vendorId))
            .whenData((vendor) {
              if (vendor != null) {
                selectedVendor.value = vendor;
                vendorSearchController.text = vendor.vendorName;
              }
            });
      }

      // Load account data if available
      ref
          .read(accountByNumberProvider(vendorInvoice!.expenseAccountNumber))
          .whenData((account) {
            if (account != null) {
              selectedAccount.value = account;
            }
          });

      // Load project data if available
      if (vendorInvoice!.projectId != null) {
        ref.read(projectByIdProvider(vendorInvoice!.projectId!)).whenData((
          project,
        ) {
          if (project != null) {
            selectedProject.value = project;
          }
        });
      }

      // Load staff member data if available
      if (vendorInvoice!.staffId != null) {
        ref.read(staffMemberByIdProvider(vendorInvoice!.staffId!)).whenData((
          staff,
        ) {
          if (staff != null) {
            selectedStaffMember.value = staff;
          }
        });
      }
    }
  }

  /// Setup listeners for form fields
  void _setupListeners() {
    // When invoice date changes, update due date to be 30 days later
    invoiceDate.addListener(() {
      dueDate.value = invoiceDate.value.add(const Duration(days: 30));
    });
  }

  /// Search for vendors
  void searchVendors(String query) {
    if (query.isNotEmpty) {
      isSearching.value = true;
      debouncer.run(() {
        vendorSearchQuery.value = query;
      });
    } else {
      isSearching.value = false;
      vendorSearchQuery.value = '';
    }
  }

  /// Handle vendor selection
  void selectVendor(Vendor vendor) {
    selectedVendor.value = vendor;
    vendorSearchController.text = vendor.vendorName;
    isSearching.value = false;
  }

  /// Clear vendor selection
  void clearVendorSelection() {
    vendorSearchController.clear();
    selectedVendor.value = null;
    vendorSearchQuery.value = '';
    isSearching.value = false;
  }

  /// Save the invoice
  Future<bool> saveInvoice(BuildContext context) async {
    if (formKey.currentState?.validate() != true) {
      return false;
    }

    if (selectedVendor.value == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a vendor')));
      return false;
    }

    if (selectedAccount.value == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an expense account')),
      );
      return false;
    }

    try {
      isLoading.value = true;
      isError.value = false;

      final amount = double.parse(amountController.text);
      final taxAmount =
          taxAmountController.text.isEmpty
              ? null
              : double.parse(taxAmountController.text);

      final invoiceData = VendorInvoice(
        // If editing, use existing ID; otherwise, use 0 to be assigned by DB
        invoiceId: isEditMode ? vendorInvoice!.invoiceId : 0,
        vendorId: selectedVendor.value!.vendorId,
        invoiceNumber: invoiceNumberController.text,
        invoiceDate: invoiceDate.value,
        dueDate: dueDate.value,
        amount: amount,
        currencyCode: currency.value,
        expenseAccountNumber: selectedAccount.value!.accountNumber,
        taxAmount: taxAmount,
        projectId: selectedProject.value?.projectId,
        staffId: selectedStaffMember.value?.staffId,
        companyId: ref.read(selectedCompanyIdProvider),
        isPaid: isEditMode ? vendorInvoice!.isPaid : false,
      );

      if (isEditMode) {
        final updateVendorInvoice = ref.read(vendorInvoiceUpdateProvider);
        await updateVendorInvoice(invoiceData);
        if (!context.mounted) return false;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        final createVendorInvoice = ref.read(vendorInvoiceCreationProvider);
        await createVendorInvoice(invoiceData);
        if (!context.mounted) return false;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      return true;
    } catch (e) {
      isError.value = true;
      errorMessage.value =
          'Error ${isEditMode ? 'updating' : 'creating'} invoice: $e';
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(errorMessage.value)));
      }
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Dispose of resources
  void dispose() {
    vendorSearchController.dispose();
    invoiceNumberController.dispose();
    amountController.dispose();
    taxAmountController.dispose();
    descriptionController.dispose();

    selectedVendor.dispose();
    selectedAccount.dispose();
    selectedProject.dispose();
    selectedStaffMember.dispose();
    invoiceDate.dispose();
    dueDate.dispose();
    currency.dispose();

    isLoading.dispose();
    isError.dispose();
    errorMessage.dispose();

    vendorSearchQuery.dispose();
    isSearching.dispose();
  }
}
