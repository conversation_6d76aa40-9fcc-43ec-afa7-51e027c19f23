import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/form_state_manager.dart';

/// Form state manager for the vendor invoice form
class VendorInvoiceFormState extends FormStateManager {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Constructor
  VendorInvoiceFormState(this.viewModel) : super(formKey: viewModel.formKey) {
    // Initialize form state with values from the view model
    _initializeFormState();

    // Setup field dependencies
    _setupFieldDependencies();
  }

  /// Initialize form state with values from the view model
  void _initializeFormState() {
    // Set original values
    setOriginalValue('vendorId', viewModel.selectedVendor.value?.vendorId);
    setOriginalValue('vendorName', viewModel.vendorSearchController.text);
    setOriginalValue('invoiceNumber', viewModel.invoiceNumberController.text);
    setOriginalValue('invoiceDate', viewModel.invoiceDate.value);
    setOriginalValue('dueDate', viewModel.dueDate.value);
    setOriginalValue('amount', viewModel.amountController.text);
    setOriginalValue('taxAmount', viewModel.taxAmountController.text);
    setOriginalValue(
      'accountNumber',
      viewModel.selectedAccount.value?.accountNumber,
    );
    setOriginalValue('projectId', viewModel.selectedProject.value?.projectId);
    setOriginalValue('staffId', viewModel.selectedStaffMember.value?.staffId);
    setOriginalValue('currency', viewModel.currency.value);

    // Setup listeners for view model value notifiers
    viewModel.selectedVendor.addListener(_onVendorChanged);
    viewModel.invoiceDate.addListener(_onInvoiceDateChanged);
    viewModel.dueDate.addListener(_onDueDateChanged);
    viewModel.selectedAccount.addListener(_onAccountChanged);
    viewModel.selectedProject.addListener(_onProjectChanged);
    viewModel.selectedStaffMember.addListener(_onStaffMemberChanged);
    viewModel.currency.addListener(_onCurrencyChanged);

    // Setup listeners for text controllers
    viewModel.vendorSearchController.addListener(_onVendorNameChanged);
    viewModel.invoiceNumberController.addListener(_onInvoiceNumberChanged);
    viewModel.amountController.addListener(_onAmountChanged);
    viewModel.taxAmountController.addListener(_onTaxAmountChanged);
  }

  /// Setup field dependencies
  void _setupFieldDependencies() {
    // Due date depends on invoice date
    addFieldDependency('dueDate', 'invoiceDate');

    // Tax amount validation depends on invoice amount
    addFieldDependency('taxAmount', 'amount');
  }

  // Listener callbacks

  void _onVendorChanged() {
    setCurrentValue('vendorId', viewModel.selectedVendor.value?.vendorId);
  }

  void _onVendorNameChanged() {
    setCurrentValue('vendorName', viewModel.vendorSearchController.text);
  }

  void _onInvoiceNumberChanged() {
    setCurrentValue('invoiceNumber', viewModel.invoiceNumberController.text);
  }

  void _onInvoiceDateChanged() {
    setCurrentValue('invoiceDate', viewModel.invoiceDate.value);
  }

  void _onDueDateChanged() {
    setCurrentValue('dueDate', viewModel.dueDate.value);
  }

  void _onAmountChanged() {
    setCurrentValue('amount', viewModel.amountController.text);
  }

  void _onTaxAmountChanged() {
    setCurrentValue('taxAmount', viewModel.taxAmountController.text);
  }

  void _onAccountChanged() {
    setCurrentValue(
      'accountNumber',
      viewModel.selectedAccount.value?.accountNumber,
    );
  }

  void _onProjectChanged() {
    setCurrentValue('projectId', viewModel.selectedProject.value?.projectId);
  }

  void _onStaffMemberChanged() {
    setCurrentValue('staffId', viewModel.selectedStaffMember.value?.staffId);
  }

  void _onCurrencyChanged() {
    setCurrentValue('currency', viewModel.currency.value);
  }

  @override
  void dispose() {
    // Remove listeners from view model value notifiers
    viewModel.selectedVendor.removeListener(_onVendorChanged);
    viewModel.invoiceDate.removeListener(_onInvoiceDateChanged);
    viewModel.dueDate.removeListener(_onDueDateChanged);
    viewModel.selectedAccount.removeListener(_onAccountChanged);
    viewModel.selectedProject.removeListener(_onProjectChanged);
    viewModel.selectedStaffMember.removeListener(_onStaffMemberChanged);
    viewModel.currency.removeListener(_onCurrencyChanged);

    // Remove listeners from text controllers
    viewModel.vendorSearchController.removeListener(_onVendorNameChanged);
    viewModel.invoiceNumberController.removeListener(_onInvoiceNumberChanged);
    viewModel.amountController.removeListener(_onAmountChanged);
    viewModel.taxAmountController.removeListener(_onTaxAmountChanged);

    super.dispose();
  }
}
