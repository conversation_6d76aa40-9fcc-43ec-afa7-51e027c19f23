import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/ui/features/vendor_invoices/components/accounting_section.dart';
import 'package:we_like_money/ui/features/vendor_invoices/components/invoice_details_section.dart';
import 'package:we_like_money/ui/features/vendor_invoices/components/optional_fields_section.dart';
import 'package:we_like_money/ui/features/vendor_invoices/components/vendor_selection_section.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_form_state.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/index.dart';
import 'package:we_like_money/ui/common/widgets/loading_display.dart';

/// Screen for creating or editing a vendor invoice with general ledger entries
class VendorInvoiceEntryScreen extends HookConsumerWidget {
  /// The invoice to edit, null if creating a new invoice
  final VendorInvoice? vendorInvoice;

  /// Constructor
  const VendorInvoiceEntryScreen({this.vendorInvoice, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create a view model instance
    final viewModel = useMemoized(
      () => VendorInvoiceViewModel(ref, vendorInvoice),
      [vendorInvoice],
    );

    // Create a form state manager
    final formStateManager = useMemoized(
      () => VendorInvoiceFormState(viewModel),
      [viewModel],
    );

    // Dispose resources when the widget is disposed
    useEffect(() {
      return () {
        viewModel.dispose();
        formStateManager.dispose();
      };
    }, [viewModel, formStateManager]);

    // Handle vendor creation
    void onVendorCreated(Vendor vendor) {
      viewModel.selectVendor(vendor);
    }

    // Handle form submission
    Future<void> onSubmit() async {
      final success = await viewModel.saveInvoice(context);
      if (success && context.mounted) {
        Navigator.of(context).pop();
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          viewModel.isEditMode ? 'Edit Vendor Invoice' : 'New Vendor Invoice',
        ),
      ),
      body: ValueListenableBuilder(
        valueListenable: viewModel.isLoading,
        builder: (context, isLoading, _) {
          if (isLoading) {
            return const LoadingDisplay(message: 'Processing invoice...');
          }

          return _buildForm(
            context,
            viewModel,
            formStateManager,
            onVendorCreated,
            onSubmit,
          );
        },
      ),
    );
  }

  /// Build the form using FormBuilder
  Widget _buildForm(
    BuildContext context,
    VendorInvoiceViewModel viewModel,
    VendorInvoiceFormState formState,
    Function(Vendor) onVendorCreated,
    VoidCallback onSubmit,
  ) {
    // Create a form builder with the form key from the view model
    final formBuilder = FormBuilder(formKey: viewModel.formKey);

    // Build the form with all sections
    return formBuilder
        // Error message section
        .addSection(_buildErrorSection(viewModel))
        // Vendor selection section
        .addSection(
          VendorSelectionSection(
            viewModel: viewModel,
            onVendorCreated: onVendorCreated,
          ),
        )
        // Invoice details section
        .addSection(InvoiceDetailsSection(viewModel: viewModel))
        // Accounting section
        .addSection(AccountingSection(viewModel: viewModel))
        // Optional fields section
        .addSection(OptionalFieldsSection(viewModel: viewModel))
        // Submit button
        .addSubmitButton(
          text: viewModel.isEditMode ? 'Update Invoice' : 'Create Invoice',
          onPressed: onSubmit,
          isLoading: viewModel.isLoading.value,
          backgroundColor: Theme.of(context).primaryColor,
          textColor: Colors.white,
          height: 50,
        )
        // Build the form inside a scrollable container
        .buildScrollable(context);
  }

  /// Build the error display section
  Widget _buildErrorSection(VendorInvoiceViewModel viewModel) {
    return ValueListenableBuilder(
      valueListenable: viewModel.isError,
      builder: (context, isError, _) {
        if (!isError) {
          return const SizedBox.shrink();
        }

        return ValueListenableBuilder(
          valueListenable: viewModel.errorMessage,
          builder: (context, errorMessage, _) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Card(
                color: Colors.red.shade100,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade900),
                          const SizedBox(width: 8),
                          Text(
                            'Error',
                            style: TextStyle(
                              color: Colors.red.shade900,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        errorMessage,
                        style: TextStyle(color: Colors.red.shade900),
                      ),
                      const SizedBox(height: 8),
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            viewModel.isError.value = false;
                          },
                          child: const Text('Dismiss'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
