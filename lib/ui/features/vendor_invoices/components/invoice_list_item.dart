import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_detail_screen.dart';

/// A list item for displaying a vendor invoice
class InvoiceListItem extends StatelessWidget {
  /// The invoice to display
  final VendorInvoice invoice;

  /// Date format for displaying dates
  final DateFormat dateFormat;

  /// Currency format for displaying amounts
  final NumberFormat currencyFormat;

  /// Constructor
  const InvoiceListItem({
    required this.invoice,
    required this.dateFormat,
    required this.currencyFormat,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isPastDue =
        invoice.dueDate.isBefore(DateTime.now()) && !(invoice.isPaid ?? false);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Invoice #${invoice.invoiceNumber}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            _buildStatusChip(),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Vendor: ${invoice.vendorId}'),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  currencyFormat.format(invoice.amount),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  'Due: ${dateFormat.format(invoice.dueDate)}',
                  style: TextStyle(color: isPastDue ? Colors.red : null),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) =>
                      VendorInvoiceDetailScreen(invoiceId: invoice.invoiceId),
            ),
          );
        },
      ),
    );
  }

  /// Build the status chip
  Widget _buildStatusChip() {
    final isPaid = invoice.isPaid ?? false;

    return Chip(
      backgroundColor: isPaid ? Colors.green : Colors.orange,
      label: Text(
        isPaid ? 'Paid' : 'Unpaid',
        style: const TextStyle(color: Colors.white),
      ),
    );
  }
}
