import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/vendor_provider.dart';

/// A dialog for filtering invoices
class InvoiceFilterDialog extends ConsumerWidget {
  /// Current filter state: show paid invoices
  final bool showPaidOnly;
  
  /// Current filter state: show unpaid invoices
  final bool showUnpaidOnly;
  
  /// Current filter state: selected vendor
  final Vendor? selectedVendor;
  
  /// Callback when filters are applied
  final Function(bool, bool, Vendor?) onApplyFilters;
  
  /// Constructor
  const InvoiceFilterDialog({
    required this.showPaidOnly,
    required this.showUnpaidOnly,
    required this.selectedVendor,
    required this.onApplyFilters,
    super.key,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Local state for the dialog
    bool localShowPaidOnly = showPaidOnly;
    bool localShowUnpaidOnly = showUnpaidOnly;
    Vendor? localSelectedVendor = selectedVendor;
    
    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: const Text('Filter Invoices'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Payment status options
                const Text(
                  'Payment Status',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                CheckboxListTile(
                  title: const Text('Show Paid Invoices'),
                  value: localShowPaidOnly,
                  onChanged: (value) {
                    setState(() {
                      localShowPaidOnly = value ?? false;
                      // Avoid mutually exclusive filters
                      if (localShowPaidOnly && localShowUnpaidOnly) {
                        localShowUnpaidOnly = false;
                      }
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text('Show Unpaid Invoices'),
                  value: localShowUnpaidOnly,
                  onChanged: (value) {
                    setState(() {
                      localShowUnpaidOnly = value ?? false;
                      // Avoid mutually exclusive filters
                      if (localShowUnpaidOnly && localShowPaidOnly) {
                        localShowPaidOnly = false;
                      }
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Vendor filter
                const Text(
                  'Vendor',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                
                _buildVendorDropdown(ref, setState, localSelectedVendor, (vendor) {
                  setState(() {
                    localSelectedVendor = vendor;
                  });
                }),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Apply filters and close dialog
                onApplyFilters(
                  localShowPaidOnly,
                  localShowUnpaidOnly,
                  localSelectedVendor,
                );
                Navigator.of(context).pop();
              },
              child: const Text('Apply'),
            ),
          ],
        );
      },
    );
  }
  
  /// Build the vendor dropdown
  Widget _buildVendorDropdown(
    WidgetRef ref,
    StateSetter setState,
    Vendor? currentVendor,
    Function(Vendor?) onVendorChanged,
  ) {
    final vendorsAsync = ref.watch(vendorsProvider);
    
    return vendorsAsync.when(
      data: (vendors) {
        return DropdownButtonFormField<Vendor?>(
          decoration: const InputDecoration(
            labelText: 'Filter by Vendor',
            border: OutlineInputBorder(),
          ),
          value: currentVendor,
          hint: const Text('All Vendors'),
          items: [
            const DropdownMenuItem<Vendor?>(
              value: null,
              child: Text('All Vendors'),
            ),
            ...vendors.map((vendor) {
              return DropdownMenuItem(
                value: vendor,
                child: Text(vendor.vendorName),
              );
            }),
          ],
          onChanged: onVendorChanged,
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.0),
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, _) => Text('Error loading vendors: $error'),
    );
  }
}
