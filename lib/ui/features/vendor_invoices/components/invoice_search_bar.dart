import 'package:flutter/material.dart';

/// A search bar for filtering invoices
class InvoiceSearchBar extends StatelessWidget {
  /// The current search query
  final String searchQuery;
  
  /// Callback when the search query changes
  final Function(String) onSearchChanged;
  
  /// Constructor
  const InvoiceSearchBar({
    required this.searchQuery,
    required this.onSearchChanged,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: TextEditingController(text: searchQuery)
          ..selection = TextSelection.fromPosition(
            TextPosition(offset: searchQuery.length),
          ),
        decoration: InputDecoration(
          hintText: 'Search by invoice number or vendor',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onSearchChanged(''),
                )
              : null,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
        ),
        onChanged: onSearchChanged,
      ),
    );
  }
}
