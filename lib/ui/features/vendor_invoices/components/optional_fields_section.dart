import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/providers/staff_member_provider.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/fields/dropdown_form_field.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/common/widgets/error_display.dart';

/// A form section for optional fields
class OptionalFieldsSection extends ConsumerWidget {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Constructor
  const OptionalFieldsSection({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _OptionalFieldsSectionContent(viewModel: viewModel, ref: ref);
  }
}

/// Content widget for the optional fields section
class _OptionalFieldsSectionContent extends FormSection {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// The provider reference
  final WidgetRef ref;

  /// Constructor
  const _OptionalFieldsSectionContent({
    required this.viewModel,
    required this.ref,
  }) : super(
         title: 'Optional Fields',
         helpText: 'Additional information for the invoice',
         headerIcon: Icons.more_horiz,
       );

  @override
  Widget buildSectionContent(BuildContext context) {
    // Watch projects and staff members
    final projectsAsyncValue = ref.watch(projectsProvider);
    final staffMembersAsyncValue = ref.watch(staffMembersProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Project dropdown
        projectsAsyncValue.when(
          data: (projects) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedProject,
              builder: (context, selectedProject, _) {
                return AppDropdownFormField<Project>.optional(
                  value: selectedProject,
                  onChanged: (project) {
                    viewModel.selectedProject.value = project;
                  },
                  items: projects,
                  displayStringBuilder:
                      (project) =>
                          '${project.projectCode} - ${project.projectName}',
                  labelText: 'Project',
                  prefixIcon: Icons.work,
                );
              },
            );
          },
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          error:
              (error, stackTrace) => ErrorDisplay(
                errorMessage: 'Error loading projects: $error',
                onRetry: () => ref.refresh(projectsProvider),
              ),
        ),

        const SizedBox(height: 16),

        // Staff member dropdown
        staffMembersAsyncValue.when(
          data: (staffMembers) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedStaffMember,
              builder: (context, selectedStaffMember, _) {
                return AppDropdownFormField<StaffMember>.optional(
                  value: selectedStaffMember,
                  onChanged: (staffMember) {
                    viewModel.selectedStaffMember.value = staffMember;
                  },
                  items: staffMembers,
                  displayStringBuilder: (staffMember) => staffMember.staffName,
                  labelText: 'Staff Member',
                  prefixIcon: Icons.person,
                );
              },
            );
          },
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          error:
              (error, stackTrace) => ErrorDisplay(
                errorMessage: 'Error loading staff members: $error',
                onRetry: () => ref.refresh(staffMembersProvider),
              ),
        ),

        const SizedBox(height: 16),

        // Currency dropdown
        ValueListenableBuilder(
          valueListenable: viewModel.currency,
          builder: (context, currency, _) {
            return AppDropdownFormField<String>.required(
              value: currency,
              onChanged: (value) {
                if (value != null) {
                  viewModel.currency.value = value;
                }
              },
              items: const ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
              displayStringBuilder: (code) => code,
              labelText: 'Currency',
              prefixIcon: Icons.attach_money,
            );
          },
        ),
      ],
    );
  }
}
