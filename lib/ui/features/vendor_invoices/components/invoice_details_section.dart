import 'package:flutter/material.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/fields/currency_form_field.dart';
import 'package:we_like_money/ui/forms/fields/date_form_field.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A form section for invoice details
class InvoiceDetailsSection extends FormSection {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Constructor
  const InvoiceDetailsSection({required this.viewModel, super.key})
    : super(
        title: 'Invoice Details',
        helpText:
            'Enter the invoice details including number, dates, and amount',
        headerIcon: Icons.receipt,
      );

  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Invoice number
        AppTextFormField(
          labelText: 'Invoice Number',
          value: viewModel.invoiceNumberController.text,
          onChanged: (value) {
            viewModel.invoiceNumberController.text = value;
          },
          isRequired: true,
          validationRule: Validator.invoiceNumber(),
          prefixIcon: Icons.numbers,
        ),
        const SizedBox(height: 16),

        // Invoice date and due date
        Row(
          children: [
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: viewModel.invoiceDate,
                builder: (context, invoiceDate, _) {
                  return AppDateFormField(
                    labelText: 'Invoice Date',
                    value: invoiceDate,
                    onChanged: (date) {
                      viewModel.invoiceDate.value = date;
                    },
                    isRequired: true,
                    validationRule: Validator.dateFormat('yyyy-MM-dd'),
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: viewModel.dueDate,
                builder: (context, dueDate, _) {
                  return ValueListenableBuilder(
                    valueListenable: viewModel.invoiceDate,
                    builder: (context, invoiceDate, _) {
                      return AppDateFormField.withMinDate(
                        labelText: 'Due Date',
                        value: dueDate,
                        onChanged: (date) {
                          viewModel.dueDate.value = date;
                        },
                        minDate: invoiceDate,
                        isRequired: true,
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Invoice amount and tax
        Row(
          children: [
            Expanded(
              child: AppCurrencyFormField.invoiceAmount(
                labelText: 'Amount',
                value: viewModel.amountController.text,
                onChanged: (value) {
                  viewModel.amountController.text = value;
                },
                isRequired: true,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AppCurrencyFormField.taxAmount(
                labelText: 'Tax Amount',
                value: viewModel.taxAmountController.text,
                onChanged: (value) {
                  viewModel.taxAmountController.text = value;
                },
                invoiceAmount: viewModel.amountController.text,
                isRequired: false,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Description
        AppTextFormField(
          labelText: 'Description',
          value: viewModel.descriptionController.text,
          onChanged: (value) {
            viewModel.descriptionController.text = value;
          },
          isRequired: true,
          validationRule: Validator.required('Description is required'),
          prefixIcon: Icons.description,
          maxLength: 200,
        ),
      ],
    );
  }
}
