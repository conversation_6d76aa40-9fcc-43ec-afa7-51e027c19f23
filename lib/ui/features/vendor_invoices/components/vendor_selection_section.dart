import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/vendor_provider.dart'
    as vendor_provider;
import 'package:we_like_money/ui/features/vendors/vendor_quick_create_dialog.dart';
import 'package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/utils/validation/index.dart';
import 'package:we_like_money/ui/common/widgets/error_display.dart';

/// A form section for vendor selection
class VendorSelectionSection extends ConsumerWidget {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Callback when a new vendor is created
  final Function(Vendor) onVendorCreated;

  /// Constructor
  const VendorSelectionSection({
    required this.viewModel,
    required this.onVendorCreated,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _VendorSelectionContent(
      viewModel: viewModel,
      onVendorCreated: onVendorCreated,
      ref: ref,
    );
  }
}

/// Content widget for the vendor selection section
class _VendorSelectionContent extends FormSection {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Callback when a new vendor is created
  final Function(Vendor) onVendorCreated;

  /// The provider reference
  final WidgetRef ref;

  /// Constructor
  const _VendorSelectionContent({
    required this.viewModel,
    required this.onVendorCreated,
    required this.ref,
  }) : super(
         title: 'Vendor',
         helpText: 'Search for and select a vendor',
         headerIcon: Icons.business,
       );

  @override
  Widget buildSectionContent(BuildContext context) {
    // Watch vendor search results
    final vendorSearchResult = ref.watch(
      vendor_provider.vendorSearchProvider(viewModel.vendorSearchQuery.value),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Vendor search field
        AppTextFormField(
          labelText: 'Vendor',
          value: viewModel.vendorSearchController.text,
          onChanged: viewModel.searchVendors,
          isRequired: true,
          validationRule: Validator.required('Vendor is required'),
          prefixIcon: Icons.search,
          suffixIcon: Icons.add,
          helpText: 'Search for a vendor or click + to create a new one',
          hintText: 'Start typing to search for vendors',
        ),

        // Vendor search results
        const SizedBox(height: 8),

        ValueListenableBuilder(
          valueListenable: viewModel.isSearching,
          builder: (context, isSearching, _) {
            if (!isSearching) {
              return const SizedBox.shrink();
            }

            return vendorSearchResult.when(
              data: (vendors) {
                if (vendors.isEmpty) {
                  return _buildNoVendorsFound(context);
                }

                return _buildVendorList(context, vendors);
              },
              loading:
                  () => const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: CircularProgressIndicator(),
                    ),
                  ),
              error:
                  (error, stackTrace) => ErrorDisplay(
                    errorMessage: 'Error loading vendors: $error',
                    onRetry:
                        () => viewModel.searchVendors(
                          viewModel.vendorSearchController.text,
                        ),
                  ),
            );
          },
        ),

        // Selected vendor display
        ValueListenableBuilder(
          valueListenable: viewModel.selectedVendor,
          builder: (context, selectedVendor, _) {
            if (selectedVendor == null) {
              return const SizedBox.shrink();
            }

            return Card(
              margin: const EdgeInsets.only(top: 16),
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.business, size: 32),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            selectedVendor.vendorName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (selectedVendor.email != null)
                            Text(selectedVendor.email!),
                          Text('ID: ${selectedVendor.vendorId}'),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: viewModel.clearVendorSelection,
                      tooltip: 'Clear selection',
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  /// Build the vendor list
  Widget _buildVendorList(BuildContext context, List<Vendor> vendors) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: vendors.length,
        itemBuilder: (context, index) {
          final vendor = vendors[index];
          return ListTile(
            title: Text(vendor.vendorName),
            subtitle: vendor.email != null ? Text(vendor.email!) : null,
            onTap: () => viewModel.selectVendor(vendor),
          );
        },
      ),
    );
  }

  /// Build the no vendors found widget
  Widget _buildNoVendorsFound(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const Icon(Icons.search_off),
          const SizedBox(width: 16),
          const Expanded(child: Text('No vendors found with that name')),
          TextButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Create New'),
            onPressed: () => _showVendorCreateDialog(context),
          ),
        ],
      ),
    );
  }

  /// Show the vendor create dialog
  Future<void> _showVendorCreateDialog(BuildContext context) async {
    final vendor = await showDialog<Vendor>(
      context: context,
      builder: (context) => const VendorQuickCreateDialog(),
    );

    if (vendor != null) {
      onVendorCreated(vendor);
    }
  }
}
