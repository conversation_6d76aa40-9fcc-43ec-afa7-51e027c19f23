import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/providers/company_provider.dart';

/// A screen that displays and allows editing of a vendor's details.
/// If no vendor is provided, it creates a new vendor.
class VendorDetailScreen extends ConsumerStatefulWidget {
  final Vendor? vendor;

  const VendorDetailScreen({super.key, this.vendor});

  @override
  ConsumerState<VendorDetailScreen> createState() => _VendorDetailScreenState();
}

class _VendorDetailScreenState extends ConsumerState<VendorDetailScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isEditMode = false;

  // Form fields
  late TextEditingController _vendorNameController;
  late TextEditingController _vendorNumberController;
  late TextEditingController _orgNumberController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _zipCodeController;
  late TextEditingController _cityController;
  late TextEditingController _countryController;
  late TextEditingController _bankAccountTypeController;
  late TextEditingController _bankAccountNumberController;
  late TextEditingController _contactPersonController;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();

    final vendor = widget.vendor;
    _isEditMode = vendor != null;

    // Initialize controllers with data from vendor if editing, or empty if creating
    _vendorNameController = TextEditingController(
      text: vendor?.vendorName ?? '',
    );
    _vendorNumberController = TextEditingController(
      text: vendor?.vendorNumber ?? '',
    );
    _orgNumberController = TextEditingController(
      text: vendor?.organizationNumber ?? '',
    );
    _phoneController = TextEditingController(text: vendor?.phone ?? '');
    _emailController = TextEditingController(text: vendor?.email ?? '');
    _addressController = TextEditingController(text: vendor?.address ?? '');
    _zipCodeController = TextEditingController(text: vendor?.zipCode ?? '');
    _cityController = TextEditingController(text: vendor?.city ?? '');
    _countryController = TextEditingController(text: vendor?.country ?? '');
    _bankAccountTypeController = TextEditingController(
      text: vendor?.bankAccountType ?? '',
    );
    _bankAccountNumberController = TextEditingController(
      text: vendor?.bankAccountNumber ?? '',
    );
    _contactPersonController = TextEditingController(
      text: vendor?.contactPerson ?? '',
    );
    _isActive = vendor?.isActive ?? true;
  }

  @override
  void dispose() {
    // Dispose controllers
    _vendorNameController.dispose();
    _vendorNumberController.dispose();
    _orgNumberController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _zipCodeController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _bankAccountTypeController.dispose();
    _bankAccountNumberController.dispose();
    _contactPersonController.dispose();
    super.dispose();
  }

  Future<void> _saveVendor() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final vendor = widget.vendor;
      final selectedCompanyId = ref.read(selectedCompanyIdProvider);

      if (selectedCompanyId == null) {
        throw Exception('No company selected');
      }

      final updatedVendor = Vendor(
        vendorId:
            vendor?.vendorId ?? 'V${DateTime.now().millisecondsSinceEpoch}',
        vendorName: _vendorNameController.text,
        vendorNumber:
            _vendorNumberController.text.isEmpty
                ? null
                : _vendorNumberController.text,
        organizationNumber:
            _orgNumberController.text.isEmpty
                ? null
                : _orgNumberController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        address:
            _addressController.text.isEmpty ? null : _addressController.text,
        zipCode:
            _zipCodeController.text.isEmpty ? null : _zipCodeController.text,
        city: _cityController.text.isEmpty ? null : _cityController.text,
        country:
            _countryController.text.isEmpty ? null : _countryController.text,
        bankAccountType:
            _bankAccountTypeController.text.isEmpty
                ? null
                : _bankAccountTypeController.text,
        bankAccountNumber:
            _bankAccountNumberController.text.isEmpty
                ? null
                : _bankAccountNumberController.text,
        contactPerson:
            _contactPersonController.text.isEmpty
                ? null
                : _contactPersonController.text,
        isActive: _isActive,
        companyId: vendor?.companyId ?? selectedCompanyId,
      );

      if (_isEditMode) {
        await ref.read(vendorUpdateProvider)(updatedVendor);
      } else {
        await ref.read(vendorCreationProvider)(updatedVendor);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditMode
                  ? 'Vendor updated successfully'
                  : 'Vendor created successfully',
            ),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteVendor() async {
    if (widget.vendor == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(vendorDeletionProvider)(widget.vendor!.vendorId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Vendor deleted successfully')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'Edit Vendor' : 'New Vendor'),
        actions: [
          if (_isEditMode)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed:
                  _isLoading
                      ? null
                      : () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                title: const Text('Delete Vendor'),
                                content: const Text(
                                  'Are you sure you want to delete this vendor?',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                      _deleteVendor();
                                    },
                                    child: const Text('Delete'),
                                  ),
                                ],
                              ),
                        );
                      },
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ElevatedButton(
                        onPressed: _isLoading ? null : _saveVendor,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12.0),
                        ),
                        child: Text(
                          _isEditMode ? 'Update Vendor' : 'Create Vendor',
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Active status
                      SwitchListTile(
                        title: const Text('Active'),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Basic Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Vendor name
                      TextFormField(
                        controller: _vendorNameController,
                        decoration: const InputDecoration(
                          labelText: 'Vendor Name*',
                          hintText: 'Enter vendor name',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a vendor name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      // Vendor number
                      TextFormField(
                        controller: _vendorNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Vendor Number',
                          hintText: 'Enter vendor number',
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Organization number
                      TextFormField(
                        controller: _orgNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Organization Number',
                          hintText: 'Enter organization number',
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Contact Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Phone
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'Phone',
                          hintText: 'Enter phone number',
                        ),
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 8),
                      // Email
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email',
                          hintText: 'Enter email address',
                        ),
                        keyboardType: TextInputType.emailAddress,
                      ),
                      const SizedBox(height: 8),
                      // Contact person
                      TextFormField(
                        controller: _contactPersonController,
                        decoration: const InputDecoration(
                          labelText: 'Contact Person',
                          hintText: 'Enter contact person name',
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Address',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Address
                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'Street Address',
                          hintText: 'Enter street address',
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Zip code
                      TextFormField(
                        controller: _zipCodeController,
                        decoration: const InputDecoration(
                          labelText: 'Zip Code',
                          hintText: 'Enter zip code',
                        ),
                      ),
                      const SizedBox(height: 8),
                      // City
                      TextFormField(
                        controller: _cityController,
                        decoration: const InputDecoration(
                          labelText: 'City',
                          hintText: 'Enter city',
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Country
                      TextFormField(
                        controller: _countryController,
                        decoration: const InputDecoration(
                          labelText: 'Country',
                          hintText: 'Enter country',
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Banking Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Bank account type
                      TextFormField(
                        controller: _bankAccountTypeController,
                        decoration: const InputDecoration(
                          labelText: 'Bank Account Type',
                          hintText: 'Enter bank account type',
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Bank account number
                      TextFormField(
                        controller: _bankAccountNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Bank Account Number',
                          hintText: 'Enter bank account number',
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _saveVendor,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12.0),
                        ),
                        child: Text(
                          _isEditMode ? 'Update Vendor' : 'Create Vendor',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }
}
