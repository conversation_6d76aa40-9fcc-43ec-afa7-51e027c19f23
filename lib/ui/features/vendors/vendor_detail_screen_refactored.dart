import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/ui/features/vendors/components/basic_info_section.dart';
import 'package:we_like_money/ui/features/vendors/components/contact_info_section.dart';
import 'package:we_like_money/ui/features/vendors/components/address_section.dart';
import 'package:we_like_money/ui/features/vendors/components/banking_info_section.dart';
import 'package:we_like_money/ui/features/vendors/components/save_button_section.dart';
import 'package:we_like_money/ui/forms/form_builder.dart';

/// A refactored screen that displays and allows editing of a vendor's details.
/// If no vendor is provided, it creates a new vendor.
class VendorDetailScreenRefactored extends ConsumerStatefulWidget {
  /// The vendor to edit, null if creating a new vendor
  final Vendor? vendor;

  /// Constructor
  const VendorDetailScreenRefactored({super.key, this.vendor});

  @override
  ConsumerState<VendorDetailScreenRefactored> createState() =>
      _VendorDetailScreenRefactoredState();
}

class _VendorDetailScreenRefactoredState
    extends ConsumerState<VendorDetailScreenRefactored> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isEditMode = false;

  // Form fields
  late TextEditingController _vendorNameController;
  late TextEditingController _vendorNumberController;
  late TextEditingController _orgNumberController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _zipCodeController;
  late TextEditingController _cityController;
  late TextEditingController _countryController;
  late TextEditingController _bankAccountTypeController;
  late TextEditingController _bankAccountNumberController;
  late TextEditingController _contactPersonController;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();

    final vendor = widget.vendor;
    _isEditMode = vendor != null;

    // Initialize controllers with data from vendor if editing, or empty if creating
    _vendorNameController = TextEditingController(
      text: vendor?.vendorName ?? '',
    );
    _vendorNumberController = TextEditingController(
      text: vendor?.vendorNumber ?? '',
    );
    _orgNumberController = TextEditingController(
      text: vendor?.organizationNumber ?? '',
    );
    _phoneController = TextEditingController(text: vendor?.phone ?? '');
    _emailController = TextEditingController(text: vendor?.email ?? '');
    _addressController = TextEditingController(text: vendor?.address ?? '');
    _zipCodeController = TextEditingController(text: vendor?.zipCode ?? '');
    _cityController = TextEditingController(text: vendor?.city ?? '');
    _countryController = TextEditingController(text: vendor?.country ?? '');
    _bankAccountTypeController = TextEditingController(
      text: vendor?.bankAccountType ?? '',
    );
    _bankAccountNumberController = TextEditingController(
      text: vendor?.bankAccountNumber ?? '',
    );
    _contactPersonController = TextEditingController(
      text: vendor?.contactPerson ?? '',
    );
    _isActive = vendor?.isActive ?? true;
  }

  @override
  void dispose() {
    // Dispose controllers
    _vendorNameController.dispose();
    _vendorNumberController.dispose();
    _orgNumberController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _zipCodeController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _bankAccountTypeController.dispose();
    _bankAccountNumberController.dispose();
    _contactPersonController.dispose();
    super.dispose();
  }

  Future<void> _saveVendor() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final vendor = widget.vendor;
      final selectedCompanyId = ref.read(selectedCompanyIdProvider);

      if (selectedCompanyId == null) {
        throw Exception('No company selected');
      }

      final updatedVendor = Vendor(
        vendorId:
            vendor?.vendorId ?? 'V${DateTime.now().millisecondsSinceEpoch}',
        vendorName: _vendorNameController.text,
        vendorNumber:
            _vendorNumberController.text.isEmpty
                ? null
                : _vendorNumberController.text,
        organizationNumber:
            _orgNumberController.text.isEmpty
                ? null
                : _orgNumberController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        address:
            _addressController.text.isEmpty ? null : _addressController.text,
        zipCode:
            _zipCodeController.text.isEmpty ? null : _zipCodeController.text,
        city: _cityController.text.isEmpty ? null : _cityController.text,
        country:
            _countryController.text.isEmpty ? null : _countryController.text,
        bankAccountType:
            _bankAccountTypeController.text.isEmpty
                ? null
                : _bankAccountTypeController.text,
        bankAccountNumber:
            _bankAccountNumberController.text.isEmpty
                ? null
                : _bankAccountNumberController.text,
        contactPerson:
            _contactPersonController.text.isEmpty
                ? null
                : _contactPersonController.text,
        isActive: _isActive,
        companyId: vendor?.companyId ?? selectedCompanyId,
      );

      if (_isEditMode) {
        await ref.read(vendorUpdateProvider)(updatedVendor);
      } else {
        await ref.read(vendorCreationProvider)(updatedVendor);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditMode
                  ? 'Vendor updated successfully'
                  : 'Vendor created successfully',
            ),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteVendor() async {
    if (widget.vendor == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(vendorDeletionProvider)(widget.vendor!.vendorId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Vendor deleted successfully')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showDeleteConfirmationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Vendor'),
            content: const Text('Are you sure you want to delete this vendor?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteVendor();
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'Edit Vendor' : 'New Vendor'),
        actions: [
          if (_isEditMode)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _isLoading ? null : _showDeleteConfirmationDialog,
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildForm(),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: FormBuilder(formKey: _formKey)
            // Save button at the top
            .addSection(
              SaveButtonSection(
                isEditMode: _isEditMode,
                isLoading: _isLoading,
                onSave: _saveVendor,
              ),
            )
            // Basic information section
            .addSection(
              BasicInfoSection(
                vendorNameController: _vendorNameController,
                vendorNumberController: _vendorNumberController,
                orgNumberController: _orgNumberController,
                isActive: _isActive,
                onActiveChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
              ),
            )
            // Contact information section
            .addSection(
              ContactInfoSection(
                phoneController: _phoneController,
                emailController: _emailController,
                contactPersonController: _contactPersonController,
              ),
            )
            // Address section
            .addSection(
              AddressSection(
                addressController: _addressController,
                zipCodeController: _zipCodeController,
                cityController: _cityController,
                countryController: _countryController,
              ),
            )
            // Banking information section
            .addSection(
              BankingInfoSection(
                bankAccountTypeController: _bankAccountTypeController,
                bankAccountNumberController: _bankAccountNumberController,
              ),
            )
            // Save button at the bottom
            .addSection(
              SaveButtonSection(
                isEditMode: _isEditMode,
                isLoading: _isLoading,
                onSave: _saveVendor,
              ),
            )
            .build(context),
      ),
    );
  }
}
