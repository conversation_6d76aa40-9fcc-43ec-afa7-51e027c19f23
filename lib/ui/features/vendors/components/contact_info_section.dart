import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';

/// A form section for vendor contact information
class ContactInfoSection extends FormSection {
  /// The phone controller
  final TextEditingController phoneController;

  /// The email controller
  final TextEditingController emailController;

  /// The contact person controller
  final TextEditingController contactPersonController;

  /// Constructor
  const ContactInfoSection({
    required this.phoneController,
    required this.emailController,
    required this.contactPersonController,
    super.key,
  }) : super(title: 'Contact Information', headerIcon: Icons.contact_phone);

  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Phone
        AppTextFormField(
          labelText: 'Phone',
          value: phoneController.text,
          onChanged: (value) => phoneController.text = value,
          hintText: 'Enter phone number',
          keyboardType: TextInputType.phone,
          prefixIcon: Icons.phone,
        ),
        const SizedBox(height: 8),

        // Email
        AppTextFormField.email(
          value: emailController.text,
          onChanged: (value) => emailController.text = value,
          isRequired: false,
        ),
        const SizedBox(height: 8),

        // Contact person
        AppTextFormField(
          labelText: 'Contact Person',
          value: contactPersonController.text,
          onChanged: (value) => contactPersonController.text = value,
          hintText: 'Enter contact person name',
          prefixIcon: Icons.person,
        ),
      ],
    );
  }
}
