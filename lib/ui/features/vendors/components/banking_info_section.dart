import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';

/// A form section for vendor banking information
class BankingInfoSection extends FormSection {
  /// The bank account type controller
  final TextEditingController bankAccountTypeController;

  /// The bank account number controller
  final TextEditingController bankAccountNumberController;

  /// Constructor
  const BankingInfoSection({
    required this.bankAccountTypeController,
    required this.bankAccountNumberController,
    super.key,
  }) : super(title: 'Banking Information', headerIcon: Icons.account_balance);

  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Bank account type
        AppTextFormField(
          labelText: 'Bank Account Type',
          value: bankAccountTypeController.text,
          onChanged: (value) => bankAccountTypeController.text = value,
          hintText: 'Enter bank account type',
          prefixIcon: Icons.account_balance,
        ),
        const SizedBox(height: 8),

        // Bank account number
        AppTextFormField(
          labelText: 'Bank Account Number',
          value: bankAccountNumberController.text,
          onChanged: (value) => bankAccountNumberController.text = value,
          hintText: 'Enter bank account number',
          prefixIcon: Icons.credit_card,
        ),
      ],
    );
  }
}
