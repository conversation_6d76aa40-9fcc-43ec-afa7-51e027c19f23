import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';

/// A form section for the save button
class SaveButtonSection extends FormSection {
  /// Whether the form is in edit mode
  final bool isEditMode;

  /// Whether the form is loading
  final bool isLoading;

  /// Callback when the save button is pressed
  final VoidCallback onSave;

  /// Constructor
  const SaveButtonSection({
    required this.isEditMode,
    required this.isLoading,
    required this.onSave,
    super.key,
  }) : super(title: '', showHeader: false);

  @override
  Widget buildSectionContent(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onSave,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      child: Text(
        isEditMode ? 'Update Vendor' : 'Create Vendor',
        style: const TextStyle(fontSize: 16),
      ),
    );
  }
}
