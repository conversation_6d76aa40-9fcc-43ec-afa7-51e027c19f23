import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';

/// A form section for vendor address information
class AddressSection extends FormSection {
  /// The address controller
  final TextEditingController addressController;

  /// The zip code controller
  final TextEditingController zipCodeController;

  /// The city controller
  final TextEditingController cityController;

  /// The country controller
  final TextEditingController countryController;

  /// Constructor
  const AddressSection({
    required this.addressController,
    required this.zipCodeController,
    required this.cityController,
    required this.countryController,
    super.key,
  }) : super(title: 'Address', headerIcon: Icons.location_on);

  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Address
        AppTextFormField(
          labelText: 'Street Address',
          value: addressController.text,
          onChanged: (value) => addressController.text = value,
          hintText: 'Enter street address',
          prefixIcon: Icons.home,
        ),
        const SizedBox(height: 8),

        // Zip code
        AppTextFormField(
          labelText: 'Zip Code',
          value: zipCodeController.text,
          onChanged: (value) => zipCodeController.text = value,
          hintText: 'Enter zip code',
          keyboardType: TextInputType.number,
          prefixIcon: Icons.pin,
        ),
        const SizedBox(height: 8),

        // City
        AppTextFormField(
          labelText: 'City',
          value: cityController.text,
          onChanged: (value) => cityController.text = value,
          hintText: 'Enter city',
          prefixIcon: Icons.location_city,
        ),
        const SizedBox(height: 8),

        // Country
        AppTextFormField(
          labelText: 'Country',
          value: countryController.text,
          onChanged: (value) => countryController.text = value,
          hintText: 'Enter country',
          prefixIcon: Icons.flag,
        ),
      ],
    );
  }
}
