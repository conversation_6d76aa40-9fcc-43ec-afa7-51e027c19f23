import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A dialog that allows quick creation of a new vendor
class VendorQuickCreateDialog extends HookConsumerWidget {
  /// Creates a new vendor quick create dialog
  const VendorQuickCreateDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final vendorNameController = TextEditingController();
    final organizationNumberController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();

    final isLoading = ValueNotifier<bool>(false);
    final errorMessage = ValueNotifier<String?>(null);

    // Get the vendor creation function from provider
    final createVendor = ref.watch(vendorCreationProvider);

    // Get selected company ID
    final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

    // Function to create the vendor
    Future<void> createNewVendor() async {
      if (formKey.currentState?.validate() != true) {
        return;
      }

      try {
        isLoading.value = true;
        errorMessage.value = null;

        final newVendor = Vendor(
          vendorId: '', // Will be assigned by the database
          vendorName: vendorNameController.text,
          organizationNumber:
              organizationNumberController.text.isNotEmpty
                  ? organizationNumberController.text
                  : null,
          email: emailController.text.isNotEmpty ? emailController.text : null,
          phone: phoneController.text.isNotEmpty ? phoneController.text : null,
          isActive: true,
          companyId: selectedCompanyId,
        );

        final createdVendor = await createVendor(newVendor);

        if (context.mounted) {
          Navigator.of(context).pop(createdVendor);
        }
      } catch (e) {
        errorMessage.value = 'Error creating vendor: ${e.toString()}';
      } finally {
        isLoading.value = false;
      }
    }

    return AlertDialog(
      title: const Text('Create New Vendor'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (errorMessage.value != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      errorMessage.value!,
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),

                // Vendor name field
                TextFormField(
                  controller: vendorNameController,
                  decoration: const InputDecoration(
                    labelText: 'Vendor Name *',
                    border: OutlineInputBorder(),
                  ),
                  validator: Validator.createValidator(
                    Validator.required('Vendor name is required'),
                  ),
                ),
                const SizedBox(height: 16),

                // Organization number field
                TextFormField(
                  controller: organizationNumberController,
                  decoration: const InputDecoration(
                    labelText: 'Organization Number',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Email field
                TextFormField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator:
                      emailController.text.isNotEmpty
                          ? Validator.createValidator(Validator.email())
                          : null,
                ),
                const SizedBox(height: 16),

                // Phone field
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Phone',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, child) {
            return loading
                ? const CircularProgressIndicator()
                : ElevatedButton(
                  onPressed: createNewVendor,
                  child: const Text('Create Vendor'),
                );
          },
        ),
      ],
    );
  }
}
