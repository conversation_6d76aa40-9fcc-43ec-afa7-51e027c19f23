import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/providers/company_provider.dart';

class CompaniesScreen extends ConsumerWidget {
  const CompaniesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companiesAsync = ref.watch(companiesProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Companies')),
      body: companiesAsync.when(
        data: (companies) {
          if (companies.isEmpty) {
            return const Center(
              child: Text('No companies found. Add one to get started.'),
            );
          }

          return ListView.builder(
            itemCount: companies.length,
            itemBuilder: (context, index) {
              final company = companies[index];
              return CompanyListItem(company: company);
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, _) => Center(
              child: Text(
                'Error loading companies: ${error.toString().replaceAll('Exception: ', '')}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCompanyDialog(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<void> _showAddCompanyDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final TextEditingController nameController = TextEditingController();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Company'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(labelText: 'Company Name'),
            autofocus: true,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  final newCompany = Company(
                    companyId: 0,
                    companyName: nameController.text.trim(),
                    organizationNumber:
                        'ORG-${DateTime.now().millisecondsSinceEpoch}',
                    phone: '+**********',
                    email: '<EMAIL>',
                    address: 'Company Address',
                    zipCode: '12345',
                    city: 'Company City',
                    country: 'Company Country',
                  );

                  try {
                    final createCompany = ref.read(companyCreationProvider);
                    await createCompany(newCompany);

                    // Use the result of refresh
                    final companies = await ref.refresh(
                      companiesProvider.future,
                    );
                    debugPrint('Refreshed companies: ${companies.length}');

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Company created successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Error creating company: ${e.toString().replaceAll('Exception: ', '')}',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }
}

class CompanyListItem extends ConsumerWidget {
  final Company company;

  const CompanyListItem({super.key, required this.company});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ListTile(
        title: Text(company.companyName),
        subtitle: Text('ID: ${company.companyId}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditCompanyDialog(context, ref),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteCompanyDialog(context, ref),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showEditCompanyDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final TextEditingController nameController = TextEditingController(
      text: company.companyName,
    );

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Edit Company'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(labelText: 'Company Name'),
            autofocus: true,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  final updatedCompany = company.copyWith(
                    companyName: nameController.text.trim(),
                  );

                  try {
                    final updateCompany = ref.read(companyUpdateProvider);
                    await updateCompany(updatedCompany);

                    // Use the result of refresh
                    final companies = await ref.refresh(
                      companiesProvider.future,
                    );
                    debugPrint('Refreshed companies: ${companies.length}');

                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Company updated successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Error updating company: ${e.toString().replaceAll('Exception: ', '')}',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showDeleteCompanyDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Company'),
          content: Text(
            'Are you sure you want to delete ${company.companyName}? This action cannot be undone.',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  final deleteCompany = ref.read(companyDeletionProvider);
                  await deleteCompany(company.companyId);

                  // Use the result of refresh
                  final companies = await ref.refresh(companiesProvider.future);
                  debugPrint('Refreshed companies: ${companies.length}');

                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Company deleted successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Error deleting company: ${e.toString().replaceAll('Exception: ', '')}',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
