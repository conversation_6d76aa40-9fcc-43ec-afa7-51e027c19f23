import 'package:flutter/material.dart';

/// A widget that displays a loading indicator
class LoadingView extends StatelessWidget {
  /// Constructor
  const LoadingView({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          Sized<PERSON><PERSON>(height: 16),
          Text('Loading accounts...'),
        ],
      ),
    );
  }
}
