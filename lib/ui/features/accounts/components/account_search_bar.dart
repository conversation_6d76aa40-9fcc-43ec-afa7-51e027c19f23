import 'package:flutter/material.dart';

/// A search bar for filtering accounts
class AccountSearchBar extends StatelessWidget {
  /// The current search query
  final String searchQuery;
  
  /// Callback when the search query changes
  final ValueChanged<String> onSearchChanged;
  
  /// Constructor
  const AccountSearchBar({
    required this.searchQuery,
    required this.onSearchChanged,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: TextEditingController(text: searchQuery)
          ..selection = TextSelection.fromPosition(
            TextPosition(offset: searchQuery.length),
          ),
        decoration: InputDecoration(
          hintText: 'Search accounts',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onSearchChanged(''),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: onSearchChanged,
      ),
    );
  }
}
