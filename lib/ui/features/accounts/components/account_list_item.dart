import 'package:flutter/material.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/ui/features/accounts/account_register_screen.dart';

/// A list item that displays an account with its balance
class AccountListItem extends StatelessWidget {
  /// The account to display
  final Account account;
  
  /// The account balance
  final double balance;
  
  /// Constructor
  const AccountListItem({
    required this.account,
    required this.balance,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    final isPositive = balance >= 0;
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          account.accountName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('Account #: ${account.accountNumber}'),
        trailing: Text(
          '\$${balance.abs().toStringAsFixed(2)}',
          style: TextStyle(
            color: isPositive ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AccountRegisterScreen(account: account),
            ),
          );
        },
      ),
    );
  }
}
