import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/ui/features/accounts/account_register_screen.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

/// A screen that displays a list of accounts and allows the user to select one
/// to view its register.
class AccountListScreen extends StatefulWidget {
  const AccountListScreen({super.key});

  @override
  State<AccountListScreen> createState() => _AccountListScreenState();
}

class _AccountListScreenState extends State<AccountListScreen> {
  final AccountViewModel _viewModel = GetIt.instance<AccountViewModel>();
  bool _isLoading = true;
  List<Account> _accounts = [];
  Map<String, double> _accountBalances = {};
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final accounts = await _viewModel.getAccounts();

      // Load account balances one by one
      final balances = <String, double>{};
      for (final account in accounts) {
        balances[account.accountNumber] = await _viewModel
            .calculateAccountBalance(account.accountNumber);
      }

      setState(() {
        _accounts = accounts;
        _accountBalances = balances;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Accounts')),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Implement account creation
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_errorMessage',
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAccounts,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_accounts.isEmpty) {
      return const Center(
        child: Text('No accounts found. Create an account to get started.'),
      );
    }

    return ListView.builder(
      itemCount: _accounts.length,
      itemBuilder: (context, index) {
        final account = _accounts[index];
        return _buildAccountListItem(account);
      },
    );
  }

  Widget _buildAccountListItem(Account account) {
    final balance = _accountBalances[account.accountNumber] ?? 0.0;
    final isPositive = balance >= 0;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          account.accountName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('Account #: ${account.accountNumber}'),
        trailing: Text(
          '\$${balance.abs().toStringAsFixed(2)}',
          style: TextStyle(
            color: isPositive ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AccountRegisterScreen(account: account),
            ),
          );
        },
      ),
    );
  }
}
