import 'package:flutter/material.dart';

/// A widget that displays a message when no ledger entries are found
class EmptyLedgerView extends StatelessWidget {
  /// Whether filters are applied
  final bool hasFilters;
  
  /// Constructor
  const EmptyLedgerView({
    this.hasFilters = false,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.book,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            hasFilters ? 'No matching entries found' : 'No ledger entries found',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            hasFilters
                ? 'Try adjusting your filters'
                : 'Create transactions to see entries here',
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
