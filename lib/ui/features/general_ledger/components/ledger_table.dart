import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/ledger_entry.dart';

/// A table that displays ledger entries
class LedgerTable extends StatelessWidget {
  /// The ledger entries to display
  final List<LedgerEntry> entries;

  /// Date format for displaying dates
  final DateFormat dateFormat;

  /// Constructor
  const LedgerTable({
    required this.entries,
    required this.dateFormat,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(label: Text('Date')),
            DataColumn(label: Text('Account')),
            DataColumn(label: Text('Description')),
            DataColumn(label: Text('Debit'), numeric: true),
            DataColumn(label: Text('Credit'), numeric: true),
            DataColumn(label: Text('Balance'), numeric: true),
          ],
          rows: entries.map((entry) => _buildDataRow(entry)).toList(),
        ),
      ),
    );
  }

  /// Build a data row for a ledger entry
  DataRow _buildDataRow(LedgerEntry entry) {
    final isDebit = entry.amount > 0;
    final runningBalance = entry.runningBalance ?? 0.0;

    return DataRow(
      cells: [
        DataCell(Text(dateFormat.format(entry.date))),
        DataCell(Text(entry.accountNumber)),
        DataCell(Text(entry.description)),
        DataCell(
          Text(isDebit ? '\$${entry.amount.abs().toStringAsFixed(2)}' : ''),
        ),
        DataCell(
          Text(!isDebit ? '\$${entry.amount.abs().toStringAsFixed(2)}' : ''),
        ),
        DataCell(
          Text(
            '\$${runningBalance.abs().toStringAsFixed(2)}',
            style: TextStyle(
              color: runningBalance >= 0 ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
