import 'package:flutter/material.dart';
import 'package:we_like_money/config/app_config.dart';

/// Settings screen for the application
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AppConfig _appConfig = AppConfig();
  DatabaseMode _selectedMode = DatabaseMode.auto;

  @override
  void initState() {
    super.initState();
    _selectedMode = _appConfig.databaseMode;
  }

  /// Shows a confirmation dialog before changing database mode
  void _confirmModeChange(DatabaseMode newMode) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Database Mode'),
            content: Text(
              'Changing the database mode will restart any active database connections. '
              'Are you sure you want to change to ${_getModeLabel(newMode)} mode?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _changeMode(newMode);
                },
                child: const Text('Change'),
              ),
            ],
          ),
    );
  }

  /// Changes the database mode and updates the UI
  Future<void> _changeMode(DatabaseMode newMode) async {
    // Store the mode label before the async operation
    final modeLabel = _getModeLabel(newMode);

    // Perform the async operation
    await _appConfig.setDatabaseMode(newMode);

    // Check if the widget is still mounted before using BuildContext
    if (!mounted) return;

    setState(() {
      _selectedMode = newMode;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Database mode changed to $modeLabel'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Returns a user-friendly label for each database mode
  String _getModeLabel(DatabaseMode mode) {
    switch (mode) {
      case DatabaseMode.auto:
        return 'Automatic';
      case DatabaseMode.mockOnly:
        return 'Mock Data Only';
      case DatabaseMode.remoteOnly:
        return 'Remote Database Only';
    }
  }

  /// Returns a description for each database mode
  String _getModeDescription(DatabaseMode mode) {
    switch (mode) {
      case DatabaseMode.auto:
        return 'Uses mock data in debug mode and remote database in release mode.';
      case DatabaseMode.mockOnly:
        return 'Always uses mock data, never connects to remote database.';
      case DatabaseMode.remoteOnly:
        return 'Always tries to connect to remote database.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        children: [
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Database Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Column(
              children: [
                RadioListTile<DatabaseMode>(
                  title: const Text('Automatic Mode'),
                  subtitle: Text(_getModeDescription(DatabaseMode.auto)),
                  value: DatabaseMode.auto,
                  groupValue: _selectedMode,
                  onChanged: (value) {
                    if (value != null) _confirmModeChange(value);
                  },
                ),
                RadioListTile<DatabaseMode>(
                  title: const Text('Mock Data Only'),
                  subtitle: Text(_getModeDescription(DatabaseMode.mockOnly)),
                  value: DatabaseMode.mockOnly,
                  groupValue: _selectedMode,
                  onChanged: (value) {
                    if (value != null) _confirmModeChange(value);
                  },
                ),
                RadioListTile<DatabaseMode>(
                  title: const Text('Remote Database Only'),
                  subtitle: Text(_getModeDescription(DatabaseMode.remoteOnly)),
                  value: DatabaseMode.remoteOnly,
                  groupValue: _selectedMode,
                  onChanged: (value) {
                    if (value != null) _confirmModeChange(value);
                  },
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _selectedMode == DatabaseMode.mockOnly
                            ? Icons.cloud_off
                            : Icons.cloud,
                        color:
                            _selectedMode == DatabaseMode.mockOnly
                                ? Colors.grey
                                : Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Current Mode: ${_getModeLabel(_selectedMode)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Note: Changes to database mode will take effect immediately '
              'for new connections, but existing screens may need to be reloaded.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
