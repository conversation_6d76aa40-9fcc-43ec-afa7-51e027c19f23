import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/ui/features/expenses/components/expense_details_section.dart';
import 'package:we_like_money/ui/features/expenses/components/payment_details_section.dart';
import 'package:we_like_money/ui/features/expenses/components/project_staff_section.dart';
import 'package:we_like_money/ui/features/expenses/components/save_button.dart';
import 'package:we_like_money/ui/features/expenses/components/vendor_selection_section.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';

/// Screen for entering and editing expense details
class ExpenseEntryScreen extends ConsumerStatefulWidget {
  /// ID of the expense to edit, or null for a new expense
  final int? expenseId;

  const ExpenseEntryScreen({super.key, this.expenseId});

  @override
  ConsumerState<ExpenseEntryScreen> createState() => _ExpenseEntryScreenState();
}

class _ExpenseEntryScreenState extends ConsumerState<ExpenseEntryScreen> {
  late final ExpenseViewModel _viewModel;
  
  @override
  void initState() {
    super.initState();
    _viewModel = ExpenseViewModel(ref, widget.expenseId);
  }
  
  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.expenseId != null ? 'Edit Expense' : 'New Expense'),
      ),
      body: ValueListenableBuilder(
        valueListenable: _viewModel.isLoading,
        builder: (context, isLoading, _) {
          if (isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _viewModel.formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Error message
                    ValueListenableBuilder(
                      valueListenable: _viewModel.errorMessage,
                      builder: (context, errorMessage, _) {
                        if (errorMessage != null) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Card(
                              color: Colors.red.shade100,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  errorMessage,
                                  style: TextStyle(color: Colors.red.shade900),
                                ),
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    
                    // Vendor selection section
                    VendorSelectionSection(viewModel: _viewModel),
                    
                    const SizedBox(height: 24),
                    
                    // Expense details section
                    ExpenseDetailsSection(viewModel: _viewModel),
                    
                    const SizedBox(height: 24),
                    
                    // Project and staff section
                    ProjectStaffSection(viewModel: _viewModel),
                    
                    const SizedBox(height: 24),
                    
                    // Payment details section
                    PaymentDetailsSection(viewModel: _viewModel),
                    
                    const SizedBox(height: 32),
                    
                    // Save button
                    SaveButton(
                      viewModel: _viewModel,
                      onSaveSuccess: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
