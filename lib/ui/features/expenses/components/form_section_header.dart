import 'package:flutter/material.dart';

/// A reusable header for form sections
class FormSectionHeader extends StatelessWidget {
  /// The title of the section
  final String title;
  
  /// Optional padding to apply
  final EdgeInsets padding;
  
  /// Constructor
  const FormSectionHeader({
    required this.title,
    this.padding = const EdgeInsets.only(bottom: 8.0),
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }
}
