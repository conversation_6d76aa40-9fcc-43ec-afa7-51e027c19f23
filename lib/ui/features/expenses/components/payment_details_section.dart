import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/ui/features/expenses/components/form_section_header.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/ui/common/widgets/form_fields.dart';

/// A form section for payment details
class PaymentDetailsSection extends StatelessWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const PaymentDetailsSection({
    required this.viewModel,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const FormSectionHeader(title: 'Payment Details'),
        
        // Payment method field
        ValueListenableBuilder(
          valueListenable: viewModel.selectedPaymentMethod,
          builder: (context, selectedPaymentMethod, _) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownFormField<PaymentMethod>(
                  value: selectedPaymentMethod,
                  items: PaymentMethod.values,
                  onChanged: (value) {
                    if (value != null) {
                      viewModel.selectedPaymentMethod.value = value;
                    }
                  },
                  label: 'Payment Method *',
                  displayString: (value) => value.displayName,
                ),
                
                // Credit card number field (only show if payment method is credit card)
                if (selectedPaymentMethod == PaymentMethod.creditCard) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: viewModel.creditCardNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Credit Card Number *',
                      hintText: 'Enter credit card number',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(16),
                    ],
                    validator: (value) {
                      if (selectedPaymentMethod == PaymentMethod.creditCard) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a credit card number';
                        }
                        if (value.length < 13 || value.length > 16) {
                          return 'Credit card number must be 13-16 digits';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ],
            );
          },
        ),
      ],
    );
  }
}
