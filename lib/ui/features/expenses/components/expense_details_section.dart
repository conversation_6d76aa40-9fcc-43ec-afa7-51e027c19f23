import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/currency_provider.dart';
import 'package:we_like_money/ui/features/expenses/components/form_section_header.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/ui/common/widgets/form_fields.dart';

/// A form section for expense details
class ExpenseDetailsSection extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const ExpenseDetailsSection({
    required this.viewModel,
    super.key,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currenciesAsync = ref.watch(currenciesProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const FormSectionHeader(title: 'Expense Details'),
        
        // Expense date field
        ValueListenableBuilder(
          valueListenable: viewModel.expenseDate,
          builder: (context, expenseDate, _) {
            return DatePickerFormField(
              value: expenseDate,
              onChanged: (date) {
                viewModel.expenseDate.value = date;
              },
              label: 'Expense Date *',
            );
          },
        ),
        
        const SizedBox(height: 16),
        
        // Amount field
        CurrencyAmountFormField(
          controller: viewModel.amountController,
          label: 'Amount *',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an amount';
            }
            try {
              final amount = double.parse(value);
              if (amount <= 0) {
                return 'Amount must be greater than zero';
              }
            } catch (e) {
              return 'Please enter a valid number';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // Currency field
        currenciesAsync.when(
          data: (currencies) {
            if (currencies.isEmpty) {
              return ValueListenableBuilder(
                valueListenable: viewModel.selectedCurrencyCode,
                builder: (context, selectedCurrencyCode, _) {
                  return TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Currency Code *',
                      hintText: 'e.g., USD',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: selectedCurrencyCode,
                    onChanged: (value) {
                      viewModel.selectedCurrencyCode.value = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a currency code';
                      }
                      return null;
                    },
                  );
                },
              );
            }
            
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedCurrencyCode,
              builder: (context, selectedCurrencyCode, _) {
                return DropdownFormField<String>(
                  value: selectedCurrencyCode,
                  items: currencies.map((c) => c.currencyCode).toList(),
                  onChanged: (value) {
                    viewModel.selectedCurrencyCode.value = value;
                  },
                  label: 'Currency *',
                  displayString: (value) => currencies
                      .firstWhere(
                        (c) => c.currencyCode == value,
                      )
                      .currencyName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a currency';
                    }
                    return null;
                  },
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, _) => ValueListenableBuilder(
            valueListenable: viewModel.selectedCurrencyCode,
            builder: (context, selectedCurrencyCode, _) {
              return TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Currency Code *',
                  hintText: 'e.g., USD',
                  border: OutlineInputBorder(),
                ),
                initialValue: selectedCurrencyCode,
                onChanged: (value) {
                  viewModel.selectedCurrencyCode.value = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a currency code';
                  }
                  return null;
                },
              );
            },
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Tax amount field
        CurrencyAmountFormField(
          controller: viewModel.taxAmountController,
          label: 'Tax Amount (Optional)',
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              try {
                final taxAmount = double.parse(value);
                if (taxAmount < 0) {
                  return 'Tax amount cannot be negative';
                }
              } catch (e) {
                return 'Please enter a valid number';
              }
            }
            return null;
          },
        ),
      ],
    );
  }
}
