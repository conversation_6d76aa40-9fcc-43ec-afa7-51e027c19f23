import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/providers/staff_member_provider.dart';
import 'package:we_like_money/ui/forms/fields/dropdown_form_field.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/ui/common/widgets/error_display.dart';

/// A refactored form section for project and staff selection
class ProjectStaffSectionRefactored extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;

  /// Constructor
  const ProjectStaffSectionRefactored({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _ProjectStaffSectionContent(viewModel: viewModel, ref: ref);
  }
}

/// Content widget for the project and staff section
class _ProjectStaffSectionContent extends FormSection {
  /// The view model
  final ExpenseViewModel viewModel;

  /// The provider reference
  final WidgetRef ref;

  /// Constructor
  const _ProjectStaffSectionContent({
    required this.viewModel,
    required this.ref,
  }) : super(
         title: 'Project and Staff',
         helpText:
             'Optionally assign this expense to a project and staff member',
         headerIcon: Icons.work,
       );

  @override
  Widget buildSectionContent(BuildContext context) {
    // Watch projects and staff members
    final projectsAsync = ref.watch(projectsProvider);
    final staffMembersAsync = ref.watch(staffMembersProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Project dropdown
        projectsAsync.when(
          data: (projects) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedProjectId,
              builder: (context, selectedProjectId, _) {
                return AppDropdownFormField<int>.optional(
                  value: selectedProjectId,
                  onChanged: (value) {
                    viewModel.selectedProjectId.value = value;
                  },
                  items: projects.map((p) => p.projectId).toList(),
                  displayStringBuilder: (id) {
                    try {
                      final project = projects.firstWhere(
                        (p) => p.projectId == id,
                      );
                      return '${project.projectCode} - ${project.projectName}';
                    } catch (e) {
                      return 'Project $id';
                    }
                  },
                  labelText: 'Project',
                  prefixIcon: Icons.work,
                );
              },
            );
          },
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          error:
              (error, _) => ErrorDisplay(
                errorMessage: 'Error loading projects: $error',
                onRetry: () => ref.refresh(projectsProvider),
              ),
        ),

        const SizedBox(height: 16),

        // Staff member dropdown
        staffMembersAsync.when(
          data: (staffMembers) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedStaffId,
              builder: (context, selectedStaffId, _) {
                return AppDropdownFormField<int>.optional(
                  value: selectedStaffId,
                  onChanged: (value) {
                    viewModel.selectedStaffId.value = value;
                  },
                  items: staffMembers.map((s) => s.staffId).toList(),
                  displayStringBuilder: (id) {
                    try {
                      final staffMember = staffMembers.firstWhere(
                        (s) => s.staffId == id,
                      );
                      return staffMember.staffName;
                    } catch (e) {
                      return 'Staff $id';
                    }
                  },
                  labelText: 'Staff Member',
                  prefixIcon: Icons.person,
                );
              },
            );
          },
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          error:
              (error, _) => ErrorDisplay(
                errorMessage: 'Error loading staff members: $error',
                onRetry: () => ref.refresh(staffMembersProvider),
              ),
        ),
      ],
    );
  }
}
