import 'package:flutter/material.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/ui/forms/fields/dropdown_form_field.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/ui/forms/sections/form_section.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A refactored form section for payment details
class PaymentDetailsSectionRefactored extends FormSection {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const PaymentDetailsSectionRefactored({
    required this.viewModel,
    super.key,
  }) : super(
          title: 'Payment Details',
          helpText: 'Enter the payment method and related details',
          headerIcon: Icons.payment,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Payment method dropdown
        ValueListenableBuilder(
          valueListenable: viewModel.selectedPaymentMethod,
          builder: (context, selectedPaymentMethod, _) {
            return AppDropdownFormField<PaymentMethod>.required(
              value: selectedPaymentMethod,
              onChanged: (value) {
                if (value != null) {
                  viewModel.selectedPaymentMethod.value = value;
                }
              },
              items: PaymentMethod.values,
              displayStringBuilder: (method) => method.displayName,
              labelText: 'Payment Method',
              prefixIcon: Icons.payment,
              errorMessage: 'Please select a payment method',
            );
          },
        ),
        
        // Conditional credit card number field
        ValueListenableBuilder(
          valueListenable: viewModel.selectedPaymentMethod,
          builder: (context, selectedPaymentMethod, _) {
            if (selectedPaymentMethod != PaymentMethod.creditCard) {
              return const SizedBox.shrink();
            }
            
            return Padding(
              padding: const EdgeInsets.only(top: 16),
              child: AppTextFormField(
                labelText: 'Credit Card Number',
                value: viewModel.creditCardNumberController.text,
                onChanged: (value) {
                  viewModel.creditCardNumberController.text = value;
                },
                isRequired: true,
                validationRule: Validator.all([
                  Validator.required('Credit card number is required'),
                  Validator.pattern(
                    RegExp(r'^\d{13,19}$'),
                    'Please enter a valid credit card number',
                  ),
                ]),
                prefixIcon: Icons.credit_card,
                keyboardType: TextInputType.number,
              ),
            );
          },
        ),
      ],
    );
  }
}
