import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/ui/features/expenses/components/form_section_header.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/ui/common/widgets/form_fields.dart';

/// A form section for selecting a vendor
class VendorSelectionSection extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const VendorSelectionSection({
    required this.viewModel,
    super.key,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vendorsAsync = ref.watch(vendorsProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const FormSectionHeader(title: 'Vendor Information'),
        
        vendorsAsync.when(
          data: (vendors) {
            if (vendors.isEmpty) {
              return TextFormField(
                controller: viewModel.vendorController,
                decoration: const InputDecoration(
                  labelText: 'Vendor ID *',
                  hintText: 'Enter vendor ID',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a vendor ID';
                  }
                  return null;
                },
              );
            }
            
            return DropdownFormField<String>(
              value: vendors.any(
                (v) => v.vendorId == viewModel.vendorController.text,
              )
                  ? viewModel.vendorController.text
                  : null,
              items: vendors.map((v) => v.vendorId).toList(),
              onChanged: (value) {
                if (value != null) {
                  viewModel.vendorController.text = value;
                }
              },
              label: 'Vendor *',
              displayString: (value) => vendors
                  .firstWhere(
                    (v) => v.vendorId == value,
                  )
                  .vendorName,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a vendor';
                }
                return null;
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, _) => TextFormField(
            controller: viewModel.vendorController,
            decoration: const InputDecoration(
              labelText: 'Vendor ID *',
              hintText: 'Enter vendor ID',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a vendor ID';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}
