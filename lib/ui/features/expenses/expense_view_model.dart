import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/providers/expense_provider.dart';

/// View model for the expense entry screen
class ExpenseViewModel {
  /// The reference to the provider scope
  final WidgetRef ref;
  
  /// The ID of the expense being edited, or null if creating a new expense
  final int? expenseId;
  
  /// Form key for validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  
  /// Controllers for form fields
  final TextEditingController vendorController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController taxAmountController = TextEditingController();
  final TextEditingController creditCardNumberController = TextEditingController();
  
  /// State values
  final ValueNotifier<DateTime> expenseDate = ValueNotifier(DateTime.now());
  final ValueNotifier<String?> selectedCurrencyCode = ValueNotifier('USD');
  final ValueNotifier<int?> selectedProjectId = ValueNotifier(null);
  final ValueNotifier<int?> selectedStaffId = ValueNotifier(null);
  final ValueNotifier<PaymentMethod> selectedPaymentMethod = ValueNotifier(PaymentMethod.cash);
  
  /// Loading and error state
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<String?> errorMessage = ValueNotifier(null);
  
  /// Whether this screen is in edit mode
  bool get isEditMode => expenseId != null;
  
  /// Constructor
  ExpenseViewModel(this.ref, this.expenseId) {
    if (isEditMode) {
      _loadExpense();
    }
  }
  
  /// Loads expense data for editing
  Future<void> _loadExpense() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      final expense = await ref.read(
        expenseByIdProvider(expenseId!).future,
      );
      
      if (expense != null) {
        vendorController.text = expense.vendorId;
        expenseDate.value = expense.expenseDate;
        amountController.text = expense.amount.toString();
        selectedCurrencyCode.value = expense.currencyCode;
        selectedProjectId.value = expense.projectId;
        selectedStaffId.value = expense.staffId;
        
        if (expense.taxAmount != null) {
          taxAmountController.text = expense.taxAmount.toString();
        }
        
        selectedPaymentMethod.value = expense.paymentMethod;
        
        if (expense.creditCardNumber != null) {
          creditCardNumberController.text = expense.creditCardNumber!;
        }
      }
    } catch (e) {
      errorMessage.value = 'Error loading expense: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  /// Saves the expense
  Future<bool> saveExpense(BuildContext context) async {
    if (!formKey.currentState!.validate()) {
      return false;
    }
    
    // Validate vendor ID
    if (vendorController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vendor ID is required'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Parse form values
      final double amount = double.parse(amountController.text);
      final double? taxAmount =
          taxAmountController.text.isNotEmpty
              ? double.parse(taxAmountController.text)
              : null;
      
      final String? creditCardNumber =
          selectedPaymentMethod.value == PaymentMethod.creditCard
              ? creditCardNumberController.text
              : null;
      
      // Create expense object
      final expense = Expense(
        expenseId: expenseId ?? 0, // 0 for new expense
        transactionId: const Uuid().v4(), // Add required transactionId
        vendorId: vendorController.text.trim(), // We've validated this is not empty
        expenseDate: expenseDate.value,
        amount: amount,
        currencyCode: selectedCurrencyCode.value ?? 'USD', // Provide default value
        projectId: selectedProjectId.value,
        staffId: selectedStaffId.value,
        taxAmount: taxAmount,
        companyId: ref.read(selectedCompanyIdProvider.notifier).state ?? 0, // Handle nullable state
        paymentMethod: selectedPaymentMethod.value,
        creditCardNumber: creditCardNumber,
      );
      
      // Save expense
      if (isEditMode) {
        await ref.read(expenseUpdateProvider)(expense);
      } else {
        await ref.read(expenseCreationProvider)(expense);
      }
      
      // Refresh expenses list
      final _ = await ref.refresh(expensesProvider.future);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEditMode ? 'Expense updated' : 'Expense created',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      return true;
    } catch (e) {
      errorMessage.value = isEditMode
          ? 'Error updating expense: $e'
          : 'Error creating expense: $e';
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage.value!),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// Dispose of resources
  void dispose() {
    vendorController.dispose();
    amountController.dispose();
    taxAmountController.dispose();
    creditCardNumberController.dispose();
    
    expenseDate.dispose();
    selectedCurrencyCode.dispose();
    selectedProjectId.dispose();
    selectedStaffId.dispose();
    selectedPaymentMethod.dispose();
    
    isLoading.dispose();
    errorMessage.dispose();
  }
}
