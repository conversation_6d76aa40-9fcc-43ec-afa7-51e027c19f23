import 'package:flutter/material.dart';

/// A class for managing form state including validation, dirty tracking, and submission
class FormStateManager {
  /// The form key for validation
  final GlobalKey<FormState> formKey;
  
  /// Whether the form is currently loading
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  
  /// Whether the form has been submitted
  final ValueNotifier<bool> isSubmitted = ValueNotifier(false);
  
  /// Whether the form has been modified
  final ValueNotifier<bool> isDirty = ValueNotifier(false);
  
  /// Error message if submission fails
  final ValueNotifier<String?> errorMessage = ValueNotifier(null);
  
  /// Success message if submission succeeds
  final ValueNotifier<String?> successMessage = ValueNotifier(null);
  
  /// The original values of the form fields
  final Map<String, dynamic> _originalValues = {};
  
  /// The current values of the form fields
  final Map<String, dynamic> _currentValues = {};
  
  /// Constructor
  FormStateManager({
    GlobalKey<FormState>? formKey,
  }) : formKey = formKey ?? GlobalKey<FormState>();
  
  /// Set the original value of a field
  void setOriginalValue(String fieldName, dynamic value) {
    _originalValues[fieldName] = value;
    _currentValues[fieldName] = value;
  }
  
  /// Set the current value of a field
  void setCurrentValue(String fieldName, dynamic value) {
    final oldValue = _currentValues[fieldName];
    _currentValues[fieldName] = value;
    
    // Check if the form is dirty
    if (!mapEquals(_originalValues, _currentValues)) {
      isDirty.value = true;
    } else {
      isDirty.value = false;
    }
    
    // Notify listeners if the value has changed
    if (oldValue != value) {
      _notifyFieldListeners(fieldName);
    }
  }
  
  /// Get the current value of a field
  T? getCurrentValue<T>(String fieldName) {
    return _currentValues[fieldName] as T?;
  }
  
  /// Reset the form to its original values
  void reset() {
    _currentValues.clear();
    _currentValues.addAll(_originalValues);
    isDirty.value = false;
    isSubmitted.value = false;
    errorMessage.value = null;
    successMessage.value = null;
    formKey.currentState?.reset();
  }
  
  /// Validate the form
  bool validate() {
    isSubmitted.value = true;
    return formKey.currentState?.validate() ?? false;
  }
  
  /// Submit the form
  Future<bool> submit(Future<void> Function() onSubmit) async {
    if (!validate()) {
      return false;
    }
    
    isLoading.value = true;
    errorMessage.value = null;
    successMessage.value = null;
    
    try {
      await onSubmit();
      successMessage.value = 'Form submitted successfully';
      isDirty.value = false;
      return true;
    } catch (e) {
      errorMessage.value = 'Error submitting form: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// Field dependency tracking
  final Map<String, List<String>> _fieldDependencies = {};
  final Map<String, List<VoidCallback>> _fieldListeners = {};
  
  /// Add a dependency between fields
  void addFieldDependency(String dependentField, String sourceField) {
    _fieldDependencies.putIfAbsent(sourceField, () => []).add(dependentField);
  }
  
  /// Add a listener for a field
  void addFieldListener(String fieldName, VoidCallback listener) {
    _fieldListeners.putIfAbsent(fieldName, () => []).add(listener);
  }
  
  /// Remove a listener for a field
  void removeFieldListener(String fieldName, VoidCallback listener) {
    _fieldListeners[fieldName]?.remove(listener);
  }
  
  /// Notify listeners when a field changes
  void _notifyFieldListeners(String fieldName) {
    // Notify direct listeners
    _fieldListeners[fieldName]?.forEach((listener) => listener());
    
    // Notify dependent fields
    _fieldDependencies[fieldName]?.forEach((dependentField) {
      _fieldListeners[dependentField]?.forEach((listener) => listener());
    });
  }
  
  /// Dispose resources
  void dispose() {
    isLoading.dispose();
    isSubmitted.dispose();
    isDirty.dispose();
    errorMessage.dispose();
    successMessage.dispose();
  }
}

/// Helper function to compare maps
bool mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
  if (a == null || b == null) {
    return a == b;
  }
  if (a.length != b.length) {
    return false;
  }
  for (final key in a.keys) {
    if (!b.containsKey(key) || b[key] != a[key]) {
      return false;
    }
  }
  return true;
}
