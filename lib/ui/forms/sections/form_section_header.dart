import 'package:flutter/material.dart';

/// A reusable header for form sections
class FormSectionHeader extends StatelessWidget {
  /// The title of the section
  final String title;
  
  /// Optional padding to apply
  final EdgeInsets padding;
  
  /// Optional help text to display
  final String? helpText;
  
  /// Optional icon to display
  final IconData? icon;
  
  /// Text style for the title
  final TextStyle? titleStyle;
  
  /// Constructor
  const FormSectionHeader({
    required this.title,
    this.padding = const EdgeInsets.only(bottom: 8.0),
    this.helpText,
    this.icon,
    this.titleStyle,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultTitleStyle = theme.textTheme.titleMedium?.copyWith(
      fontWeight: FontWeight.bold,
    );
    
    return Padding(
      padding: padding,
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, size: 20),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              title,
              style: titleStyle ?? defaultTitleStyle,
            ),
          ),
          if (helpText != null)
            Tooltip(
              message: helpText!,
              child: const Icon(
                Icons.help_outline,
                size: 16,
              ),
            ),
        ],
      ),
    );
  }
}
