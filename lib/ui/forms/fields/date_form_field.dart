import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/ui/forms/fields/form_field_base.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A reusable date form field with consistent styling and validation
class AppDateFormField extends FormFieldBase<DateTime> {
  /// The date format to display
  final String dateFormat;
  
  /// The first date that can be selected
  final DateTime? firstDate;
  
  /// The last date that can be selected
  final DateTime? lastDate;
  
  /// Constructor
  const AppDateFormField({
    required super.labelText,
    required super.value,
    required super.onChanged,
    super.hintText,
    super.validationRule,
    super.isRequired,
    super.isEnabled,
    super.showValidationError,
    super.helpText,
    this.dateFormat = 'yyyy-MM-dd',
    this.firstDate,
    this.lastDate,
    super.key,
  }) : super(
          prefixIcon: Icons.calendar_today,
        );
  
  @override
  Widget buildFormField(BuildContext context) {
    final controller = TextEditingController(
      text: DateFormat(dateFormat).format(value),
    );
    
    return TextFormField(
      controller: controller,
      decoration: getInputDecoration(context),
      readOnly: true,
      enabled: isEnabled,
      validator: validator,
      onTap: isEnabled ? () => _showDatePicker(context) : null,
    );
  }
  
  /// Show the date picker
  Future<void> _showDatePicker(BuildContext context) async {
    // Close the keyboard if it's open
    FocusScope.of(context).requestFocus(FocusNode());
    
    // Calculate first and last dates
    final actualFirstDate = firstDate ?? DateTime(2000);
    final actualLastDate = lastDate ?? DateTime.now().add(const Duration(days: 365 * 10));
    
    // Show date picker
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: value,
      firstDate: actualFirstDate,
      lastDate: actualLastDate,
    );
    
    if (pickedDate != null) {
      onChanged(pickedDate);
    }
  }
  
  /// Create a date form field for a business day
  factory AppDateFormField.businessDay({
    required DateTime value,
    required ValueChanged<DateTime> onChanged,
    String labelText = 'Date',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    String? helpText,
    String dateFormat = 'yyyy-MM-dd',
    DateTime? firstDate,
    DateTime? lastDate,
    List<DateTime> holidays = const [],
  }) {
    return AppDateFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Select date',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('Date is required'),
              Validator.dateFormat(dateFormat),
              Validator.businessDay(
                dateFormat,
                holidays: holidays,
                errorMessage: 'Please select a business day',
              ),
            ])
          : Validator.all([
              Validator.dateFormat(dateFormat),
              Validator.businessDay(
                dateFormat,
                holidays: holidays,
                errorMessage: 'Please select a business day',
              ),
            ]),
      isRequired: isRequired,
      isEnabled: isEnabled,
      helpText: helpText,
      dateFormat: dateFormat,
      firstDate: firstDate,
      lastDate: lastDate,
    );
  }
  
  /// Create a date form field with a minimum date
  factory AppDateFormField.withMinDate({
    required DateTime value,
    required ValueChanged<DateTime> onChanged,
    required DateTime minDate,
    String labelText = 'Date',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    String? helpText,
    String dateFormat = 'yyyy-MM-dd',
    DateTime? lastDate,
  }) {
    return AppDateFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Select date',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('Date is required'),
              Validator.dateFormat(dateFormat),
              Validator.dateAfter(
                minDate,
                dateFormat,
                'Date must be after ${DateFormat(dateFormat).format(minDate)}',
              ),
            ])
          : Validator.all([
              Validator.dateFormat(dateFormat),
              Validator.dateAfter(
                minDate,
                dateFormat,
                'Date must be after ${DateFormat(dateFormat).format(minDate)}',
              ),
            ]),
      isRequired: isRequired,
      isEnabled: isEnabled,
      helpText: helpText,
      dateFormat: dateFormat,
      firstDate: minDate,
      lastDate: lastDate,
    );
  }
}
