import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:we_like_money/ui/forms/fields/form_field_base.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A reusable currency form field with consistent styling and validation
class AppCurrencyFormField extends FormFieldBase<String> {
  /// The controller for the text field
  final TextEditingController? controller;
  
  /// The currency symbol to display
  final String currencySymbol;
  
  /// The focus node for the field
  final FocusNode? focusNode;
  
  /// The next focus node to move to when the user presses the next button
  final FocusNode? nextFocusNode;
  
  /// The minimum amount allowed
  final double? minAmount;
  
  /// The maximum amount allowed
  final double? maxAmount;
  
  /// Whether to allow zero as a valid amount
  final bool allowZero;
  
  /// Constructor
  const AppCurrencyFormField({
    required super.labelText,
    required super.value,
    required super.onChanged,
    super.hintText,
    super.validationRule,
    super.isRequired,
    super.isEnabled,
    super.showValidationError,
    super.helpText,
    this.controller,
    this.currencySymbol = '\$',
    this.focusNode,
    this.nextFocusNode,
    this.minAmount,
    this.maxAmount,
    this.allowZero = false,
    super.key,
  });
  
  @override
  Widget buildFormField(BuildContext context) {
    final textController = controller ?? TextEditingController(text: value);
    
    if (controller == null) {
      textController.text = value;
      // Position cursor at the end of the text
      textController.selection = TextSelection.fromPosition(
        TextPosition(offset: textController.text.length),
      );
    }
    
    return TextFormField(
      controller: textController,
      decoration: getInputDecoration(context).copyWith(
        prefixText: currencySymbol,
      ),
      keyboardType: const TextInputType.numberWithOptions(
        decimal: true,
      ),
      textInputAction: TextInputAction.next,
      focusNode: focusNode,
      enabled: isEnabled,
      validator: validator,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
      ],
      onChanged: (value) {
        onChanged(value);
      },
      onFieldSubmitted: (value) {
        if (nextFocusNode != null) {
          FocusScope.of(context).requestFocus(nextFocusNode);
        }
      },
    );
  }
  
  /// Create a currency form field for invoice amounts
  factory AppCurrencyFormField.invoiceAmount({
    required String value,
    required ValueChanged<String> onChanged,
    String labelText = 'Amount',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    TextEditingController? controller,
    String currencySymbol = '\$',
    FocusNode? focusNode,
    FocusNode? nextFocusNode,
    double minAmount = 0.01,
    double? maxAmount,
    bool allowZero = false,
  }) {
    return AppCurrencyFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Enter amount',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('Amount is required'),
              Validator.invoiceAmount(
                minAmount: minAmount,
                maxAmount: maxAmount,
                allowZero: allowZero,
              ),
            ])
          : Validator.invoiceAmount(
              minAmount: minAmount,
              maxAmount: maxAmount,
              allowZero: allowZero,
            ),
      isRequired: isRequired,
      isEnabled: isEnabled,
      controller: controller,
      currencySymbol: currencySymbol,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      minAmount: minAmount,
      maxAmount: maxAmount,
      allowZero: allowZero,
    );
  }
  
  /// Create a currency form field for tax amounts
  factory AppCurrencyFormField.taxAmount({
    required String value,
    required ValueChanged<String> onChanged,
    required String invoiceAmount,
    String labelText = 'Tax Amount',
    String? hintText,
    bool isRequired = false,
    bool isEnabled = true,
    TextEditingController? controller,
    String currencySymbol = '\$',
    FocusNode? focusNode,
    FocusNode? nextFocusNode,
  }) {
    return AppCurrencyFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Enter tax amount',
      validationRule: Validator.all([
        if (isRequired) Validator.required('Tax amount is required'),
        Validator.numeric('Please enter a valid number'),
        ValidationRule<String>(
          validate: (value) {
            if (value == null || value.isEmpty) {
              return null; // Let required validator handle this case
            }
            
            final taxValue = double.tryParse(value);
            if (taxValue == null) {
              return 'Please enter a valid number';
            }
            
            final invoiceValue = double.tryParse(invoiceAmount);
            if (invoiceValue == null) {
              return null; // Can't validate against invalid invoice amount
            }
            
            if (taxValue > invoiceValue) {
              return 'Tax amount cannot exceed invoice amount';
            }
            
            return null;
          },
          name: 'taxAmount',
        ),
      ]),
      isRequired: isRequired,
      isEnabled: isEnabled,
      controller: controller,
      currencySymbol: currencySymbol,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      allowZero: true,
    );
  }
}
