import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:we_like_money/ui/forms/fields/form_field_base.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A reusable text form field with consistent styling and validation
class AppTextFormField extends FormFieldBase<String> {
  /// The controller for the text field
  final TextEditingController? controller;
  
  /// The keyboard type for the field
  final TextInputType keyboardType;
  
  /// The text input action for the field
  final TextInputAction textInputAction;
  
  /// The focus node for the field
  final FocusNode? focusNode;
  
  /// The next focus node to move to when the user presses the next button
  final FocusNode? nextFocusNode;
  
  /// The maximum length of the field
  final int? maxLength;
  
  /// Whether to obscure the text (for passwords)
  final bool obscureText;
  
  /// Input formatters for the field
  final List<TextInputFormatter>? inputFormatters;
  
  /// Callback when the field is submitted
  final ValueChanged<String>? onFieldSubmitted;
  
  /// Constructor
  const AppTextFormField({
    required super.labelText,
    required super.value,
    required super.onChanged,
    super.hintText,
    super.validationRule,
    super.isRequired,
    super.isEnabled,
    super.showValidationError,
    super.prefixIcon,
    super.suffixIcon,
    super.helpText,
    this.controller,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.focusNode,
    this.nextFocusNode,
    this.maxLength,
    this.obscureText = false,
    this.inputFormatters,
    this.onFieldSubmitted,
    super.key,
  });
  
  @override
  Widget buildFormField(BuildContext context) {
    final textController = controller ?? TextEditingController(text: value);
    
    if (controller == null) {
      textController.text = value;
      // Position cursor at the end of the text
      textController.selection = TextSelection.fromPosition(
        TextPosition(offset: textController.text.length),
      );
    }
    
    return TextFormField(
      controller: textController,
      decoration: getInputDecoration(context),
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      focusNode: focusNode,
      maxLength: maxLength,
      obscureText: obscureText,
      enabled: isEnabled,
      validator: validator,
      inputFormatters: inputFormatters,
      onChanged: (value) {
        onChanged(value);
      },
      onFieldSubmitted: (value) {
        if (onFieldSubmitted != null) {
          onFieldSubmitted!(value);
        }
        
        if (nextFocusNode != null) {
          FocusScope.of(context).requestFocus(nextFocusNode);
        }
      },
    );
  }
  
  /// Create a text form field for email input
  factory AppTextFormField.email({
    required String value,
    required ValueChanged<String> onChanged,
    String labelText = 'Email',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    TextEditingController? controller,
    FocusNode? focusNode,
    FocusNode? nextFocusNode,
    ValueChanged<String>? onFieldSubmitted,
  }) {
    return AppTextFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Enter email address',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('Email is required'),
              Validator.email('Please enter a valid email address'),
            ])
          : Validator.email('Please enter a valid email address'),
      isRequired: isRequired,
      isEnabled: isEnabled,
      prefixIcon: Icons.email,
      keyboardType: TextInputType.emailAddress,
      controller: controller,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      onFieldSubmitted: onFieldSubmitted,
    );
  }
  
  /// Create a text form field for password input
  factory AppTextFormField.password({
    required String value,
    required ValueChanged<String> onChanged,
    String labelText = 'Password',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    TextEditingController? controller,
    FocusNode? focusNode,
    FocusNode? nextFocusNode,
    ValueChanged<String>? onFieldSubmitted,
    int minLength = 8,
  }) {
    return AppTextFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Enter password',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('Password is required'),
              Validator.minLength(
                minLength,
                'Password must be at least $minLength characters',
              ),
            ])
          : Validator.minLength(
              minLength,
              'Password must be at least $minLength characters',
            ),
      isRequired: isRequired,
      isEnabled: isEnabled,
      prefixIcon: Icons.lock,
      obscureText: true,
      controller: controller,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      onFieldSubmitted: onFieldSubmitted,
    );
  }
  
  /// Create a text form field for numeric input
  factory AppTextFormField.numeric({
    required String value,
    required ValueChanged<String> onChanged,
    String labelText = 'Number',
    String? hintText,
    bool isRequired = true,
    bool isEnabled = true,
    TextEditingController? controller,
    FocusNode? focusNode,
    FocusNode? nextFocusNode,
    ValueChanged<String>? onFieldSubmitted,
    double? min,
    double? max,
    bool allowNegative = false,
  }) {
    return AppTextFormField(
      labelText: labelText,
      value: value,
      onChanged: onChanged,
      hintText: hintText ?? 'Enter a number',
      validationRule: isRequired
          ? Validator.all([
              Validator.required('This field is required'),
              Validator.numericRange(
                min: min,
                max: max,
                allowNegative: allowNegative,
              ),
            ])
          : Validator.numericRange(
              min: min,
              max: max,
              allowNegative: allowNegative,
            ),
      isRequired: isRequired,
      isEnabled: isEnabled,
      keyboardType: const TextInputType.numberWithOptions(
        decimal: true,
        signed: true,
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          allowNegative
              ? RegExp(r'^-?\d*\.?\d*$')
              : RegExp(r'^\d*\.?\d*$'),
        ),
      ],
      controller: controller,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      onFieldSubmitted: onFieldSubmitted,
    );
  }
}
