import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/company_provider.dart';

class CompanySelector extends ConsumerWidget {
  const CompanySelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companiesAsync = ref.watch(companiesProvider);
    final selectedCompanyIdNotifier = ref.watch(
      selectedCompanyIdProvider.notifier,
    );
    final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

    return companiesAsync.when(
      data: (companies) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: DropdownButtonFormField<int?>(
            decoration: const InputDecoration(
              labelText: 'Company',
              border: OutlineInputBorder(),
            ),
            value: selectedCompanyId,
            items: [
              const DropdownMenuItem<int?>(
                value: null,
                child: Text('All Companies'),
              ),
              ...companies.map(
                (company) => DropdownMenuItem<int?>(
                  value: company.companyId,
                  child: Text(company.companyName),
                ),
              ),
            ],
            onChanged: (value) {
              selectedCompanyIdNotifier.state = value;
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stackTrace) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Error loading companies: $error',
              style: const TextStyle(color: Colors.red),
            ),
          ),
    );
  }
}
