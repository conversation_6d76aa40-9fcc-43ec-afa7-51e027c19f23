import 'package:flutter/material.dart';

/// Widget that displays a loading indicator with an optional message
class LoadingDisplay extends StatelessWidget {
  /// Optional message to display
  final String? message;

  /// Creates a new loading display
  const LoadingDisplay({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
