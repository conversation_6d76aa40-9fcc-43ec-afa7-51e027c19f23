import 'package:flutter/material.dart';

/// A custom dropdown widget that allows for more customization than the standard DropdownButton
class CustomDropdown<T> extends StatelessWidget {
  /// The list of items to display in the dropdown
  final List<T> items;

  /// A function to build the text display for each item
  final String Function(T) displayTextBuilder;

  /// The currently selected value
  final T? value;

  /// Called when the user selects an item
  final ValueChanged<T?>? onChanged;

  /// Optional hint text when no item is selected
  final String? hint;

  /// Optional label for the field
  final String? labelText;

  /// Whether the dropdown is enabled
  final bool isEnabled;

  /// Optional validator function for form validation
  final String? Function(T?)? validator;

  /// Creates a custom dropdown
  const CustomDropdown({
    super.key,
    required this.items,
    required this.displayTextBuilder,
    required this.value,
    this.onChanged,
    this.hint,
    this.labelText,
    this.isEnabled = true,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: labelText,
        border: const OutlineInputBorder(),
      ),
      hint: hint != null ? Text(hint!) : null,
      isExpanded: true,
      isDense: false,
      onChanged: isEnabled ? onChanged : null,
      validator: validator,
      items:
          items.map<DropdownMenuItem<T>>((T item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(displayTextBuilder(item)),
            );
          }).toList(),
    );
  }
}
