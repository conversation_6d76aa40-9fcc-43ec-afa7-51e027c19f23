import 'package:flutter/material.dart';
import 'package:we_like_money/config/app_config.dart';

/// A widget that displays the current database connection status
class ConnectionStatusWidget extends StatelessWidget {
  final AppConfig _appConfig = AppConfig();

  ConnectionStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final isUsingMock = _appConfig.shouldUseMockMode();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: isUsingMock ? Colors.amber.shade100 : Colors.green.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUsingMock ? Colors.amber.shade700 : Colors.green.shade700,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isUsingMock ? Icons.cloud_off : Icons.cloud_done,
            color: isUsingMock ? Colors.amber.shade800 : Colors.green.shade800,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            isUsingMock ? 'Mock Data' : 'Remote Database',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color:
                  isUsingMock ? Colors.amber.shade800 : Colors.green.shade800,
            ),
          ),
        ],
      ),
    );
  }
}
