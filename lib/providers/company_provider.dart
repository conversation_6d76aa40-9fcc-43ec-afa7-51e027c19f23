import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/viewmodels/company_viewmodel.dart';

/// Provider for the CompanyViewModel
final companyViewModelProvider = Provider<CompanyViewModel>((ref) {
  return getIt<CompanyViewModel>();
});

/// StateProvider for the currently selected company ID
final selectedCompanyIdProvider = StateProvider<int?>((ref) => null);

/// Provider that exposes the currently selected company
final selectedCompanyProvider = FutureProvider<Company?>((ref) async {
  final companyId = ref.watch(selectedCompanyIdProvider);
  if (companyId == null) return null;

  final viewModel = ref.watch(companyViewModelProvider);
  return viewModel.getCompanyById(companyId);
});

/// FutureProvider for fetching all companies
final companiesProvider = FutureProvider<List<Company>>((ref) async {
  final viewModel = ref.watch(companyViewModelProvider);
  return viewModel.getCompanies();
});

/// Provider for company creation
final companyCreationProvider = Provider<Future<Company> Function(Company)>((
  ref,
) {
  final viewModel = ref.watch(companyViewModelProvider);
  return (Company company) => viewModel.createCompany(company);
});

/// Provider for company updates
final companyUpdateProvider = Provider<Future<Company> Function(Company)>((
  ref,
) {
  final viewModel = ref.watch(companyViewModelProvider);
  return (Company company) => viewModel.updateCompany(company);
});

/// Provider for company deletion
final companyDeletionProvider = Provider<Future<void> Function(int)>((ref) {
  final viewModel = ref.watch(companyViewModelProvider);
  return (int id) => viewModel.deleteCompany(id);
});
