import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/vendor_viewmodel.dart';

/// Provider for the VendorViewModel
final vendorViewModelProvider = Provider<VendorViewModel>((ref) {
  return getIt<VendorViewModel>();
});

/// FutureProvider for fetching all vendors
/// Filters vendors by the selected company if one is selected
final vendorsProvider = FutureProvider<List<Vendor>>((ref) async {
  final viewModel = ref.watch(vendorViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final vendors = await viewModel.getVendors();

  // If no company is selected, return all vendors
  if (selectedCompanyId == null) {
    return vendors;
  }

  // Filter vendors by the selected company
  return vendors
      .where((vendor) => vendor.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider family for fetching a vendor by ID
final vendorByIdProvider = FutureProvider.family<Vendor?, String>((
  ref,
  id,
) async {
  final viewModel = ref.watch(vendorViewModelProvider);
  final vendor = await viewModel.getVendorById(id);

  if (vendor == null) {
    return null;
  }

  // Check if a company is selected and if the vendor belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && vendor.companyId != selectedCompanyId) {
    return null;
  }

  return vendor;
});

/// Provider for vendor creation
final vendorCreationProvider = Provider<Future<Vendor> Function(Vendor)>((ref) {
  final viewModel = ref.watch(vendorViewModelProvider);

  return (Vendor vendor) async {
    // If a company is selected, make sure the vendor is associated with it
    final selectedCompanyId = ref.read(selectedCompanyIdProvider);
    if (selectedCompanyId != null && vendor.companyId != selectedCompanyId) {
      final updatedVendor = vendor.copyWith(companyId: selectedCompanyId);
      return await viewModel.createVendor(updatedVendor);
    }

    return await viewModel.createVendor(vendor);
  };
});

/// Provider for vendor updates
final vendorUpdateProvider = Provider<Future<Vendor> Function(Vendor)>((ref) {
  final viewModel = ref.watch(vendorViewModelProvider);

  return (Vendor vendor) async {
    return await viewModel.updateVendor(vendor);
  };
});

/// Provider for vendor deletion
final vendorDeletionProvider = Provider<Future<void> Function(String)>((ref) {
  final viewModel = ref.watch(vendorViewModelProvider);

  return (String id) async {
    return await viewModel.deleteVendor(id);
  };
});

/// Provider for searching vendors
final vendorSearchProvider = FutureProvider.family<List<Vendor>, String>((
  ref,
  query,
) async {
  final viewModel = ref.watch(vendorViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final vendors = await viewModel.searchVendors(query);

  // If no company is selected, return all matching vendors
  if (selectedCompanyId == null) {
    return vendors;
  }

  // Filter vendors by the selected company
  return vendors
      .where((vendor) => vendor.companyId == selectedCompanyId)
      .toList();
});
