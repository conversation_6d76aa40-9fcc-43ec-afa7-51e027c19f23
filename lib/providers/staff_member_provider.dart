import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/staff_member_viewmodel.dart';

/// Provider for the StaffMemberViewModel
final staffMemberViewModelProvider = Provider<StaffMemberViewModel>((ref) {
  return getIt<StaffMemberViewModel>();
});

/// FutureProvider for fetching all staff members
/// Filters staff members by the selected company if one is selected
final staffMembersProvider = FutureProvider<List<StaffMember>>((ref) async {
  final viewModel = ref.watch(staffMemberViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final staffMembers = await viewModel.getStaffMembers();

  // If no company is selected, return all staff members
  if (selectedCompanyId == null) {
    return staffMembers;
  }

  // Filter staff members by the selected company
  return staffMembers
      .where((staffMember) => staffMember.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider family for fetching a staff member by ID
final staffMemberByIdProvider = FutureProvider.family<StaffMember?, int>((
  ref,
  id,
) async {
  final viewModel = ref.watch(staffMemberViewModelProvider);
  final staffMember = await viewModel.getStaffMemberById(id);

  if (staffMember == null) {
    return null;
  }

  // Check if a company is selected and if the staff member belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && staffMember.companyId != selectedCompanyId) {
    return null;
  }

  return staffMember;
});

/// Provider for staff member creation
final staffMemberCreationProvider = Provider<
  Future<StaffMember> Function(StaffMember)
>((ref) {
  final viewModel = ref.watch(staffMemberViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  return (StaffMember staffMember) {
    // If a company is selected and the staff member doesn't have a company ID,
    // associate it with the selected company
    if (selectedCompanyId != null && staffMember.companyId == null) {
      staffMember = staffMember.copyWith(companyId: selectedCompanyId);
    }

    return viewModel.createStaffMember(staffMember);
  };
});

/// Provider for staff member updates
final staffMemberUpdateProvider =
    Provider<Future<StaffMember> Function(StaffMember)>((ref) {
      final viewModel = ref.watch(staffMemberViewModelProvider);
      return (StaffMember staffMember) =>
          viewModel.updateStaffMember(staffMember);
    });

/// Provider for staff member deletion
final staffMemberDeletionProvider = Provider<Future<void> Function(int)>((ref) {
  final viewModel = ref.watch(staffMemberViewModelProvider);
  return (int id) => viewModel.deleteStaffMember(id);
});
