import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

/// Provider for the AccountViewModel
final accountViewModelProvider = Provider<AccountViewModel>((ref) {
  return getIt<AccountViewModel>();
});

/// FutureProvider for fetching all accounts
/// Filters accounts by the selected company if one is selected
final accountsProvider = FutureProvider<List<Account>>((ref) async {
  final viewModel = ref.watch(accountViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  // Get accounts filtered by company ID if one is selected
  return await viewModel.getAccounts(selectedCompanyId);
});

/// FutureProvider family for fetching an account by number
final accountByNumberProvider = FutureProvider.family<Account?, String>((
  ref,
  accountNumber,
) async {
  final viewModel = ref.watch(accountViewModelProvider);
  final account = await viewModel.getAccountByNumber(accountNumber);

  if (account == null) {
    return null;
  }

  // Check if a company is selected and if the account belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && account.companyId != selectedCompanyId) {
    return null;
  }

  return account;
});

/// FutureProvider for fetching only expense accounts (5000-5999)
final expenseAccountsProvider = FutureProvider<List<Account>>((ref) async {
  final accounts = await ref.watch(accountsProvider.future);

  return accounts.where((account) {
    final accountNum = int.tryParse(account.accountNumber);
    return accountNum != null && accountNum >= 5000 && accountNum < 6000;
  }).toList();
});

/// FutureProvider for fetching only asset accounts (1000-1999)
final assetAccountsProvider = FutureProvider<List<Account>>((ref) async {
  final accounts = await ref.watch(accountsProvider.future);

  return accounts.where((account) {
    final accountNum = int.tryParse(account.accountNumber);
    return accountNum != null && accountNum >= 1000 && accountNum < 2000;
  }).toList();
});

/// FutureProvider for fetching only liability accounts (2000-2999)
final liabilityAccountsProvider = FutureProvider<List<Account>>((ref) async {
  final accounts = await ref.watch(accountsProvider.future);

  return accounts.where((account) {
    final accountNum = int.tryParse(account.accountNumber);
    return accountNum != null && accountNum >= 2000 && accountNum < 3000;
  }).toList();
});
