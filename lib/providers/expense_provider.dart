import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/expense_viewmodel.dart';

/// Provider for the ExpenseViewModel
final expenseViewModelProvider = Provider<ExpenseViewModel>((ref) {
  return getIt<ExpenseViewModel>();
});

/// FutureProvider for fetching all expenses
/// Filters expenses by the selected company if one is selected
final expensesProvider = FutureProvider<List<Expense>>((ref) async {
  final viewModel = ref.watch(expenseViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final expenses = await viewModel.getExpenses();

  // If no company is selected, return all expenses
  if (selectedCompanyId == null) {
    return expenses;
  }

  // Filter expenses by the selected company
  return expenses
      .where((expense) => expense.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider family for fetching an expense by ID
final expenseByIdProvider = FutureProvider.family<Expense?, int>((
  ref,
  id,
) async {
  final viewModel = ref.watch(expenseViewModelProvider);
  final expense = await viewModel.getExpenseById(id);

  if (expense == null) {
    return null;
  }

  // Check if a company is selected and if the expense belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && expense.companyId != selectedCompanyId) {
    return null;
  }

  return expense;
});

/// Provider for expense creation
final expenseCreationProvider = Provider<Future<Expense> Function(Expense)>((
  ref,
) {
  final viewModel = ref.watch(expenseViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  return (Expense expense) {
    // If a company is selected and the expense doesn't have a company ID,
    // associate it with the selected company
    if (selectedCompanyId != null && expense.companyId != selectedCompanyId) {
      expense = expense.copyWith(companyId: selectedCompanyId);
    }

    return viewModel.createExpense(expense);
  };
});

/// Provider for expense updates
final expenseUpdateProvider = Provider<Future<Expense> Function(Expense)>((
  ref,
) {
  final viewModel = ref.watch(expenseViewModelProvider);
  return (Expense expense) => viewModel.updateExpense(expense);
});

/// Provider for expense deletion
final expenseDeletionProvider = Provider<Future<void> Function(int)>((ref) {
  final viewModel = ref.watch(expenseViewModelProvider);
  return (int id) => viewModel.deleteExpense(id);
});
