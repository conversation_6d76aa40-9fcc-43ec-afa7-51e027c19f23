import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/currency.dart';
import 'package:we_like_money/viewmodels/currency_viewmodel.dart';

/// Provider for the CurrencyViewModel
final currencyViewModelProvider = Provider<CurrencyViewModel>((ref) {
  return getIt<CurrencyViewModel>();
});

/// FutureProvider for fetching all currencies
final currenciesProvider = FutureProvider<List<Currency>>((ref) async {
  final viewModel = ref.watch(currencyViewModelProvider);
  return viewModel.getCurrencies();
});

/// FutureProvider family for fetching a currency by code
final currencyByCodeProvider = FutureProvider.family<Currency?, String>((
  ref,
  code,
) async {
  final viewModel = ref.watch(currencyViewModelProvider);
  return viewModel.getCurrencyByCode(code);
});

/// Provider for currency creation
final currencyCreationProvider = Provider<Future<Currency> Function(Currency)>((
  ref,
) {
  final viewModel = ref.watch(currencyViewModelProvider);
  return (Currency currency) => viewModel.createCurrency(currency);
});

/// Provider for currency updates
final currencyUpdateProvider = Provider<Future<Currency> Function(Currency)>((
  ref,
) {
  final viewModel = ref.watch(currencyViewModelProvider);
  return (Currency currency) => viewModel.updateCurrency(currency);
});

/// Provider for currency deletion
final currencyDeletionProvider = Provider<Future<void> Function(String)>((ref) {
  final viewModel = ref.watch(currencyViewModelProvider);
  return (String code) => viewModel.deleteCurrency(code);
});
