import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/project_viewmodel.dart';

/// Provider for the ProjectViewModel
final projectViewModelProvider = Provider<ProjectViewModel>((ref) {
  return getIt<ProjectViewModel>();
});

/// FutureProvider for fetching all projects
///
/// This provider:
/// - Returns a Future&lt;List&lt;Project&gt;&gt; that will resolve to the list of projects
/// - Automatically handles loading, error, and data states
/// - Can be used with AsyncValue pattern in the UI
/// - Filters projects by the selected company if one is selected
final projectsProvider = FutureProvider<List<Project>>((ref) async {
  final viewModel = ref.watch(projectViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final projects = await viewModel.getProjects();

  // If no company is selected, return all projects
  if (selectedCompanyId == null) {
    return projects;
  }

  // Filter projects by the selected company
  return projects
      .where((project) => project.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider family for fetching a project by ID
///
/// This provider:
/// - Takes a project ID as a parameter
/// - Returns a Future&lt;Project?&gt; that will resolve to the project if found
/// - Automatically handles loading, error, and data states
/// - Can be used with AsyncValue pattern in the UI
final projectByIdProvider = FutureProvider.family<Project?, int>((
  ref,
  id,
) async {
  final viewModel = ref.watch(projectViewModelProvider);
  final project = await viewModel.getProjectById(id);

  // If no project found, return null
  if (project == null) {
    return null;
  }

  // Check if a company is selected and if the project belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && project.companyId != selectedCompanyId) {
    // Project doesn't belong to the selected company
    return null;
  }

  return project;
});

/// Provider for project creation
///
/// This provider exposes a function to create a new project
/// and returns a Future&lt;Project&gt; with the created project
/// If a company is selected, the project will be associated with that company
final projectCreationProvider = Provider<Future<Project> Function(Project)>((
  ref,
) {
  final viewModel = ref.watch(projectViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  return (Project project) {
    // If a company is selected and the project doesn't have a company ID,
    // associate it with the selected company
    if (selectedCompanyId != null && project.companyId == null) {
      project = project.copyWith(companyId: selectedCompanyId);
    }

    return viewModel.createProject(project);
  };
});

/// Provider for project updates
///
/// This provider exposes a function to update an existing project
/// and returns a Future&lt;Project&gt; with the updated project
final projectUpdateProvider = Provider<Future<Project> Function(Project)>((
  ref,
) {
  final viewModel = ref.watch(projectViewModelProvider);
  return (Project project) => viewModel.updateProject(project);
});

/// Provider for project deletion
///
/// This provider exposes a function to delete a project by ID
/// and returns a Future&lt;void&gt;
final projectDeletionProvider = Provider<Future<void> Function(int)>((ref) {
  final viewModel = ref.watch(projectViewModelProvider);
  return (int id) => viewModel.deleteProject(id);
});
