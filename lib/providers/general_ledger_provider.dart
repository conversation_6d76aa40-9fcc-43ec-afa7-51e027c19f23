import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/general_ledger_viewmodel.dart';

/// Provider for the GeneralLedgerViewModel
final generalLedgerViewModelProvider = Provider<GeneralLedgerViewModel>((ref) {
  return getIt<GeneralLedgerViewModel>();
});

/// FutureProvider for fetching all general ledger entries
/// Filters entries by the selected company if one is selected
final generalLedgerEntriesProvider = FutureProvider<List<GeneralLedger>>((
  ref,
) async {
  final viewModel = ref.watch(generalLedgerViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final entries = await viewModel.getAllGeneralLedgerEntries();

  // If no company is selected, return all entries
  if (selectedCompanyId == null) {
    return entries;
  }

  // Filter entries by the selected company
  return entries
      .where((entry) => entry.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider for fetching general ledger entries by account number
final generalLedgerEntriesByAccountProvider =
    FutureProvider.family<List<GeneralLedger>, String>((
      ref,
      accountNumber,
    ) async {
      final viewModel = ref.watch(generalLedgerViewModelProvider);
      final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

      final entries = await viewModel.getGeneralLedgerEntriesByAccount(
        accountNumber,
      );

      // If no company is selected, return all entries for the account
      if (selectedCompanyId == null) {
        return entries;
      }

      // Filter entries by the selected company
      return entries
          .where((entry) => entry.companyId == selectedCompanyId)
          .toList();
    });

/// FutureProvider for fetching general ledger entries by date range
final generalLedgerEntriesByDateRangeProvider =
    FutureProvider.family<List<GeneralLedger>, DateRange>((
      ref,
      dateRange,
    ) async {
      final viewModel = ref.watch(generalLedgerViewModelProvider);
      final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

      final entries = await viewModel.getGeneralLedgerEntriesByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      // If no company is selected, return all entries for the date range
      if (selectedCompanyId == null) {
        return entries;
      }

      // Filter entries by the selected company
      return entries
          .where((entry) => entry.companyId == selectedCompanyId)
          .toList();
    });

/// FutureProvider for fetching general ledger entries by description
/// Useful for finding entries related to a specific invoice
final generalLedgerEntriesByDescriptionProvider =
    FutureProvider.family<List<GeneralLedger>, String>((
      ref,
      description,
    ) async {
      final viewModel = ref.watch(generalLedgerViewModelProvider);
      final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

      final entries = await viewModel.getAllGeneralLedgerEntries();

      // Filter entries by description
      final filteredEntries =
          entries
              .where((entry) => entry.description.contains(description))
              .toList();

      // If no company is selected, return all matching entries
      if (selectedCompanyId == null) {
        return filteredEntries;
      }

      // Filter entries by the selected company
      return filteredEntries
          .where((entry) => entry.companyId == selectedCompanyId)
          .toList();
    });

/// Helper class for date range parameters
class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  DateRange(this.startDate, this.endDate);
}
