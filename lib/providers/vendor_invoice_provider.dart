import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/providers/company_provider.dart';
import 'package:we_like_money/viewmodels/vendor_invoice_viewmodel.dart';

/// Provider for the VendorInvoiceViewModel
final vendorInvoiceViewModelProvider = Provider<VendorInvoiceViewModel>((ref) {
  return getIt<VendorInvoiceViewModel>();
});

/// FutureProvider for fetching all vendor invoices
/// Filters vendor invoices by the selected company if one is selected
final vendorInvoicesProvider = FutureProvider<List<VendorInvoice>>((ref) async {
  final viewModel = ref.watch(vendorInvoiceViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final invoices = await viewModel.getVendorInvoices();

  // If no company is selected, return all invoices
  if (selectedCompanyId == null) {
    return invoices;
  }

  // Filter invoices by the selected company
  return invoices
      .where((invoice) => invoice.companyId == selectedCompanyId)
      .toList();
});

/// FutureProvider family for fetching vendor invoices by vendor ID
final vendorInvoicesByVendorIdProvider =
    FutureProvider.family<List<VendorInvoice>, String>((ref, vendorId) async {
      final viewModel = ref.watch(vendorInvoiceViewModelProvider);
      final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

      final invoices = await viewModel.getVendorInvoicesByVendorId(vendorId);

      // If no company is selected, return all vendor's invoices
      if (selectedCompanyId == null) {
        return invoices;
      }

      // Filter invoices by the selected company
      return invoices
          .where((invoice) => invoice.companyId == selectedCompanyId)
          .toList();
    });

/// FutureProvider family for fetching a vendor invoice by ID
final vendorInvoiceByIdProvider = FutureProvider.family<VendorInvoice?, int>((
  ref,
  id,
) async {
  final viewModel = ref.watch(vendorInvoiceViewModelProvider);
  final invoice = await viewModel.getVendorInvoiceById(id);

  if (invoice == null) {
    return null;
  }

  // Check if a company is selected and if the invoice belongs to that company
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);
  if (selectedCompanyId != null && invoice.companyId != selectedCompanyId) {
    return null;
  }

  return invoice;
});

/// Provider for vendor invoice creation
final vendorInvoiceCreationProvider =
    Provider<Future<VendorInvoice> Function(VendorInvoice)>((ref) {
      final viewModel = ref.watch(vendorInvoiceViewModelProvider);

      return (VendorInvoice invoice) async {
        // If a company is selected, make sure the invoice is associated with it
        final selectedCompanyId = ref.read(selectedCompanyIdProvider);
        if (selectedCompanyId != null &&
            invoice.companyId != selectedCompanyId) {
          final updatedInvoice = invoice.copyWith(companyId: selectedCompanyId);
          return await viewModel.createVendorInvoice(updatedInvoice);
        }

        return await viewModel.createVendorInvoice(invoice);
      };
    });

/// Provider for vendor invoice updates
final vendorInvoiceUpdateProvider =
    Provider<Future<VendorInvoice> Function(VendorInvoice)>((ref) {
      final viewModel = ref.watch(vendorInvoiceViewModelProvider);

      return (VendorInvoice invoice) async {
        return await viewModel.updateVendorInvoice(invoice);
      };
    });

/// Provider for vendor invoice deletion
final vendorInvoiceDeletionProvider = Provider<Future<void> Function(int)>((
  ref,
) {
  final viewModel = ref.watch(vendorInvoiceViewModelProvider);

  return (int id) async {
    return await viewModel.deleteVendorInvoice(id);
  };
});

/// Provider for searching vendors
final vendorSearchProvider = FutureProvider.family<List<Vendor>, String>((
  ref,
  query,
) async {
  final viewModel = ref.watch(vendorInvoiceViewModelProvider);
  final selectedCompanyId = ref.watch(selectedCompanyIdProvider);

  final vendors = await viewModel.searchVendors(query);

  // If no company is selected, return all vendors
  if (selectedCompanyId == null) {
    return vendors;
  }

  // Filter vendors by the selected company
  return vendors
      .where((vendor) => vendor.companyId == selectedCompanyId)
      .toList();
});
