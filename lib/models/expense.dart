// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:we_like_money/models/payment_method.dart';

part 'expense.freezed.dart';
part 'expense.g.dart';

@freezed
abstract class Expense with _$Expense {
  const factory Expense({
    required int expenseId,
    required String transactionId,
    required String vendorId,
    required DateTime expenseDate,
    required double amount,
    required String currencyCode,
    int? projectId,
    int? staffId,
    double? taxAmount,
    required int companyId,
    required PaymentMethod paymentMethod,
    String? creditCardNumber,
  }) = _Expense;

  factory Expense.fromJson(Map<String, dynamic> json) =>
      _$ExpenseFromJson(json);
}
