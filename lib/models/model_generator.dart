// This file contains helper utilities for model generation
// It is used by the build_runner to generate freezed and json_serializable files

void main() {
  // This is just a placeholder
  // The actual code generation is handled by build_runner
}

/// This file serves as a template for generating model classes
/// with consistent serialization behavior.
/// 
/// How to use:
/// 1. Create a new model file (e.g., my_model.dart)
/// 2. Copy the structure below, replacing:
///    - ModelName with your model name
///    - Properties with your model properties
///    - Adjust constructors and serialization as needed
/// 
/// ```dart
/// // ignore_for_file: invalid_annotation_target
/// 
/// import 'package:freezed_annotation/freezed_annotation.dart';
/// import 'package:we_like_money/utils/json_serialization_util.dart';
/// 
/// part 'my_model.freezed.dart';
/// part 'my_model.g.dart';
/// 
/// @freezed
/// abstract class MyModel with _$MyModel {
///   const factory MyModel({
///     @Json<PERSON>ey(name: 'id') required int id,
///     @Json<PERSON>ey(name: 'name') required String name,
///     String? description,
///   }) = _MyModel;
/// 
///   // Custom factory for handling both snake_case and camelCase keys
///   factory MyModel.fromJson(Map<String, dynamic> json) {
///     // Create a working copy of the json
///     final workingJson = Map<String, dynamic>.from(json);
///     
///     // Handle required fields with defaults if needed
///     if (workingJson['id'] == null && workingJson['id'] != null) {
///       workingJson['id'] = workingJson['id'];
///     } else if (workingJson['id'] == null) {
///       workingJson['id'] = 0; // Default value
///     }
///     
///     // Handle other fields as needed
///     
///     // Use the generated fromJson
///     return _$MyModelFromJson(workingJson);
///   }
///   
///   /// Custom toJson method to ensure consistent serialization
///   static Map<String, dynamic> toJson(MyModel model) {
///     // Ensure all keys are in snake_case
///     final result = <String, dynamic>{};
///     
///     // Add all fields in snake_case format
///     result['id'] = model.id;
///     result['name'] = model.name;
///     if (model.description != null) {
///       result['description'] = model.description;
///     }
///     
///     return result;
///   }
/// }
/// ```
/// 
/// After creating your model file:
/// 1. Run `dart run build_runner build --delete-conflicting-outputs`
/// 2. Use the model in your application

/// For general case serialization, you can use JsonSerializationUtil:
/// 
/// ```dart
/// // Converting from camelCase to snake_case
/// final snakeCase = JsonSerializationUtil.convertMapKeysToSnakeCase(camelCaseMap);
/// 
/// // Converting from snake_case to camelCase
/// final camelCase = JsonSerializationUtil.convertMapKeysToCamelCase(snakeCaseMap);
/// ``` 