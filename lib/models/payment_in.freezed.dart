// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_in.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PaymentIn {

 int get paymentInId; int get invoiceId; DateTime get paymentDate; double get amount; String get currencyCode; int? get companyId;
/// Create a copy of PaymentIn
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentInCopyWith<PaymentIn> get copyWith => _$PaymentInCopyWithImpl<PaymentIn>(this as PaymentIn, _$identity);

  /// Serializes this PaymentIn to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentIn&&(identical(other.paymentInId, paymentInId) || other.paymentInId == paymentInId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentInId,invoiceId,paymentDate,amount,currencyCode,companyId);

@override
String toString() {
  return 'PaymentIn(paymentInId: $paymentInId, invoiceId: $invoiceId, paymentDate: $paymentDate, amount: $amount, currencyCode: $currencyCode, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $PaymentInCopyWith<$Res>  {
  factory $PaymentInCopyWith(PaymentIn value, $Res Function(PaymentIn) _then) = _$PaymentInCopyWithImpl;
@useResult
$Res call({
 int paymentInId, int invoiceId, DateTime paymentDate, double amount, String currencyCode, int? companyId
});




}
/// @nodoc
class _$PaymentInCopyWithImpl<$Res>
    implements $PaymentInCopyWith<$Res> {
  _$PaymentInCopyWithImpl(this._self, this._then);

  final PaymentIn _self;
  final $Res Function(PaymentIn) _then;

/// Create a copy of PaymentIn
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentInId = null,Object? invoiceId = null,Object? paymentDate = null,Object? amount = null,Object? currencyCode = null,Object? companyId = freezed,}) {
  return _then(_self.copyWith(
paymentInId: null == paymentInId ? _self.paymentInId : paymentInId // ignore: cast_nullable_to_non_nullable
as int,invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,paymentDate: null == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PaymentIn implements PaymentIn {
  const _PaymentIn({required this.paymentInId, required this.invoiceId, required this.paymentDate, required this.amount, required this.currencyCode, this.companyId});
  factory _PaymentIn.fromJson(Map<String, dynamic> json) => _$PaymentInFromJson(json);

@override final  int paymentInId;
@override final  int invoiceId;
@override final  DateTime paymentDate;
@override final  double amount;
@override final  String currencyCode;
@override final  int? companyId;

/// Create a copy of PaymentIn
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentInCopyWith<_PaymentIn> get copyWith => __$PaymentInCopyWithImpl<_PaymentIn>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentInToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentIn&&(identical(other.paymentInId, paymentInId) || other.paymentInId == paymentInId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentInId,invoiceId,paymentDate,amount,currencyCode,companyId);

@override
String toString() {
  return 'PaymentIn(paymentInId: $paymentInId, invoiceId: $invoiceId, paymentDate: $paymentDate, amount: $amount, currencyCode: $currencyCode, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$PaymentInCopyWith<$Res> implements $PaymentInCopyWith<$Res> {
  factory _$PaymentInCopyWith(_PaymentIn value, $Res Function(_PaymentIn) _then) = __$PaymentInCopyWithImpl;
@override @useResult
$Res call({
 int paymentInId, int invoiceId, DateTime paymentDate, double amount, String currencyCode, int? companyId
});




}
/// @nodoc
class __$PaymentInCopyWithImpl<$Res>
    implements _$PaymentInCopyWith<$Res> {
  __$PaymentInCopyWithImpl(this._self, this._then);

  final _PaymentIn _self;
  final $Res Function(_PaymentIn) _then;

/// Create a copy of PaymentIn
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentInId = null,Object? invoiceId = null,Object? paymentDate = null,Object? amount = null,Object? currencyCode = null,Object? companyId = freezed,}) {
  return _then(_PaymentIn(
paymentInId: null == paymentInId ? _self.paymentInId : paymentInId // ignore: cast_nullable_to_non_nullable
as int,invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,paymentDate: null == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
