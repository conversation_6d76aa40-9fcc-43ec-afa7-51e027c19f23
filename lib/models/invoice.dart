import 'package:freezed_annotation/freezed_annotation.dart';
// ignore: unused_import
import 'package:flutter/foundation.dart';

part 'invoice.freezed.dart';
part 'invoice.g.dart';

@freezed
abstract class Invoice with _$Invoice {
  const factory Invoice({
    required int invoiceId,
    required String customerId,
    required DateTime invoiceDate,
    required DateTime dueDate,
    required double amount,
    required String currencyCode,
    int? projectId,
    int? staffId,
    double? taxAmount,
    int? companyId,
  }) = _Invoice;

  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);
}
