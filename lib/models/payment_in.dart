import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_in.freezed.dart';
part 'payment_in.g.dart';

@freezed
abstract class PaymentIn with _$PaymentIn {
  const factory PaymentIn({
    required int paymentInId,
    required int invoiceId,
    required DateTime paymentDate,
    required double amount,
    required String currencyCode,
    int? companyId,
  }) = _PaymentIn;

  factory PaymentIn.fromJson(Map<String, dynamic> json) =>
      _$PaymentInFromJson(json);
}
