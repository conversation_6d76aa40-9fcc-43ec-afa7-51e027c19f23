// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expense.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Expense _$ExpenseFromJson(Map<String, dynamic> json) => _Expense(
  expenseId: (json['expenseId'] as num).toInt(),
  transactionId: json['transactionId'] as String,
  vendorId: json['vendorId'] as String,
  expenseDate: DateTime.parse(json['expenseDate'] as String),
  amount: (json['amount'] as num).toDouble(),
  currencyCode: json['currencyCode'] as String,
  projectId: (json['projectId'] as num?)?.toInt(),
  staffId: (json['staffId'] as num?)?.toInt(),
  taxAmount: (json['taxAmount'] as num?)?.toDouble(),
  companyId: (json['companyId'] as num).toInt(),
  paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
  creditCardNumber: json['creditCardNumber'] as String?,
);

Map<String, dynamic> _$ExpenseToJson(_Expense instance) => <String, dynamic>{
  'expenseId': instance.expenseId,
  'transactionId': instance.transactionId,
  'vendorId': instance.vendorId,
  'expenseDate': instance.expenseDate.toIso8601String(),
  'amount': instance.amount,
  'currencyCode': instance.currencyCode,
  'projectId': instance.projectId,
  'staffId': instance.staffId,
  'taxAmount': instance.taxAmount,
  'companyId': instance.companyId,
  'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
  'creditCardNumber': instance.creditCardNumber,
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.cash: 'cash',
  PaymentMethod.creditCard: 'credit_card',
  PaymentMethod.bankTransfer: 'bank_transfer',
  PaymentMethod.check: 'check',
  PaymentMethod.other: 'other',
};
