// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vendor_invoice_line_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VendorInvoiceLineItem {

@JsonKey(name: 'line_item_id') int get lineItemId;@JsonKey(name: 'invoice_id') int get invoiceId;@JsonKey(name: 'account_number') String get accountNumber; String get description; double get amount;@JsonKey(name: 'tax_amount') double get taxAmount;@JsonKey(name: 'company_id') int get companyId;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;
/// Create a copy of VendorInvoiceLineItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VendorInvoiceLineItemCopyWith<VendorInvoiceLineItem> get copyWith => _$VendorInvoiceLineItemCopyWithImpl<VendorInvoiceLineItem>(this as VendorInvoiceLineItem, _$identity);

  /// Serializes this VendorInvoiceLineItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VendorInvoiceLineItem&&(identical(other.lineItemId, lineItemId) || other.lineItemId == lineItemId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lineItemId,invoiceId,accountNumber,description,amount,taxAmount,companyId,createdAt,updatedAt);

@override
String toString() {
  return 'VendorInvoiceLineItem(lineItemId: $lineItemId, invoiceId: $invoiceId, accountNumber: $accountNumber, description: $description, amount: $amount, taxAmount: $taxAmount, companyId: $companyId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $VendorInvoiceLineItemCopyWith<$Res>  {
  factory $VendorInvoiceLineItemCopyWith(VendorInvoiceLineItem value, $Res Function(VendorInvoiceLineItem) _then) = _$VendorInvoiceLineItemCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'line_item_id') int lineItemId,@JsonKey(name: 'invoice_id') int invoiceId,@JsonKey(name: 'account_number') String accountNumber, String description, double amount,@JsonKey(name: 'tax_amount') double taxAmount,@JsonKey(name: 'company_id') int companyId,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt
});




}
/// @nodoc
class _$VendorInvoiceLineItemCopyWithImpl<$Res>
    implements $VendorInvoiceLineItemCopyWith<$Res> {
  _$VendorInvoiceLineItemCopyWithImpl(this._self, this._then);

  final VendorInvoiceLineItem _self;
  final $Res Function(VendorInvoiceLineItem) _then;

/// Create a copy of VendorInvoiceLineItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lineItemId = null,Object? invoiceId = null,Object? accountNumber = null,Object? description = null,Object? amount = null,Object? taxAmount = null,Object? companyId = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
lineItemId: null == lineItemId ? _self.lineItemId : lineItemId // ignore: cast_nullable_to_non_nullable
as int,invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _VendorInvoiceLineItem implements VendorInvoiceLineItem {
  const _VendorInvoiceLineItem({@JsonKey(name: 'line_item_id') required this.lineItemId, @JsonKey(name: 'invoice_id') required this.invoiceId, @JsonKey(name: 'account_number') required this.accountNumber, required this.description, required this.amount, @JsonKey(name: 'tax_amount') required this.taxAmount, @JsonKey(name: 'company_id') required this.companyId, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt});
  factory _VendorInvoiceLineItem.fromJson(Map<String, dynamic> json) => _$VendorInvoiceLineItemFromJson(json);

@override@JsonKey(name: 'line_item_id') final  int lineItemId;
@override@JsonKey(name: 'invoice_id') final  int invoiceId;
@override@JsonKey(name: 'account_number') final  String accountNumber;
@override final  String description;
@override final  double amount;
@override@JsonKey(name: 'tax_amount') final  double taxAmount;
@override@JsonKey(name: 'company_id') final  int companyId;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;

/// Create a copy of VendorInvoiceLineItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VendorInvoiceLineItemCopyWith<_VendorInvoiceLineItem> get copyWith => __$VendorInvoiceLineItemCopyWithImpl<_VendorInvoiceLineItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VendorInvoiceLineItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VendorInvoiceLineItem&&(identical(other.lineItemId, lineItemId) || other.lineItemId == lineItemId)&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lineItemId,invoiceId,accountNumber,description,amount,taxAmount,companyId,createdAt,updatedAt);

@override
String toString() {
  return 'VendorInvoiceLineItem(lineItemId: $lineItemId, invoiceId: $invoiceId, accountNumber: $accountNumber, description: $description, amount: $amount, taxAmount: $taxAmount, companyId: $companyId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$VendorInvoiceLineItemCopyWith<$Res> implements $VendorInvoiceLineItemCopyWith<$Res> {
  factory _$VendorInvoiceLineItemCopyWith(_VendorInvoiceLineItem value, $Res Function(_VendorInvoiceLineItem) _then) = __$VendorInvoiceLineItemCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'line_item_id') int lineItemId,@JsonKey(name: 'invoice_id') int invoiceId,@JsonKey(name: 'account_number') String accountNumber, String description, double amount,@JsonKey(name: 'tax_amount') double taxAmount,@JsonKey(name: 'company_id') int companyId,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt
});




}
/// @nodoc
class __$VendorInvoiceLineItemCopyWithImpl<$Res>
    implements _$VendorInvoiceLineItemCopyWith<$Res> {
  __$VendorInvoiceLineItemCopyWithImpl(this._self, this._then);

  final _VendorInvoiceLineItem _self;
  final $Res Function(_VendorInvoiceLineItem) _then;

/// Create a copy of VendorInvoiceLineItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lineItemId = null,Object? invoiceId = null,Object? accountNumber = null,Object? description = null,Object? amount = null,Object? taxAmount = null,Object? companyId = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_VendorInvoiceLineItem(
lineItemId: null == lineItemId ? _self.lineItemId : lineItemId // ignore: cast_nullable_to_non_nullable
as int,invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
