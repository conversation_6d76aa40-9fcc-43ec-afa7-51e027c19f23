// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vendor.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Vendor {

@JsonKey(name: 'vendor_id') String get vendorId;@JsonKey(name: 'vendor_name') String get vendorName;@JsonKey(name: 'vendor_number') String? get vendorNumber;@JsonKey(name: 'organization_number') String? get organizationNumber;@JsonKey(name: 'is_active') bool get isActive; String? get phone; String? get email; String? get address;@JsonKey(name: 'zip_code') String? get zipCode; String? get city; String? get country;@JsonKey(name: 'bank_account_type') String? get bankAccountType;@JsonKey(name: 'bank_account_number') String? get bankAccountNumber;@JsonKey(name: 'contact_person') String? get contactPerson;@JsonKey(name: 'company_id') int? get companyId;
/// Create a copy of Vendor
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VendorCopyWith<Vendor> get copyWith => _$VendorCopyWithImpl<Vendor>(this as Vendor, _$identity);

  /// Serializes this Vendor to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Vendor&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.vendorName, vendorName) || other.vendorName == vendorName)&&(identical(other.vendorNumber, vendorNumber) || other.vendorNumber == vendorNumber)&&(identical(other.organizationNumber, organizationNumber) || other.organizationNumber == organizationNumber)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.zipCode, zipCode) || other.zipCode == zipCode)&&(identical(other.city, city) || other.city == city)&&(identical(other.country, country) || other.country == country)&&(identical(other.bankAccountType, bankAccountType) || other.bankAccountType == bankAccountType)&&(identical(other.bankAccountNumber, bankAccountNumber) || other.bankAccountNumber == bankAccountNumber)&&(identical(other.contactPerson, contactPerson) || other.contactPerson == contactPerson)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vendorId,vendorName,vendorNumber,organizationNumber,isActive,phone,email,address,zipCode,city,country,bankAccountType,bankAccountNumber,contactPerson,companyId);

@override
String toString() {
  return 'Vendor(vendorId: $vendorId, vendorName: $vendorName, vendorNumber: $vendorNumber, organizationNumber: $organizationNumber, isActive: $isActive, phone: $phone, email: $email, address: $address, zipCode: $zipCode, city: $city, country: $country, bankAccountType: $bankAccountType, bankAccountNumber: $bankAccountNumber, contactPerson: $contactPerson, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $VendorCopyWith<$Res>  {
  factory $VendorCopyWith(Vendor value, $Res Function(Vendor) _then) = _$VendorCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'vendor_id') String vendorId,@JsonKey(name: 'vendor_name') String vendorName,@JsonKey(name: 'vendor_number') String? vendorNumber,@JsonKey(name: 'organization_number') String? organizationNumber,@JsonKey(name: 'is_active') bool isActive, String? phone, String? email, String? address,@JsonKey(name: 'zip_code') String? zipCode, String? city, String? country,@JsonKey(name: 'bank_account_type') String? bankAccountType,@JsonKey(name: 'bank_account_number') String? bankAccountNumber,@JsonKey(name: 'contact_person') String? contactPerson,@JsonKey(name: 'company_id') int? companyId
});




}
/// @nodoc
class _$VendorCopyWithImpl<$Res>
    implements $VendorCopyWith<$Res> {
  _$VendorCopyWithImpl(this._self, this._then);

  final Vendor _self;
  final $Res Function(Vendor) _then;

/// Create a copy of Vendor
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? vendorId = null,Object? vendorName = null,Object? vendorNumber = freezed,Object? organizationNumber = freezed,Object? isActive = null,Object? phone = freezed,Object? email = freezed,Object? address = freezed,Object? zipCode = freezed,Object? city = freezed,Object? country = freezed,Object? bankAccountType = freezed,Object? bankAccountNumber = freezed,Object? contactPerson = freezed,Object? companyId = freezed,}) {
  return _then(_self.copyWith(
vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,vendorName: null == vendorName ? _self.vendorName : vendorName // ignore: cast_nullable_to_non_nullable
as String,vendorNumber: freezed == vendorNumber ? _self.vendorNumber : vendorNumber // ignore: cast_nullable_to_non_nullable
as String?,organizationNumber: freezed == organizationNumber ? _self.organizationNumber : organizationNumber // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,zipCode: freezed == zipCode ? _self.zipCode : zipCode // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,bankAccountType: freezed == bankAccountType ? _self.bankAccountType : bankAccountType // ignore: cast_nullable_to_non_nullable
as String?,bankAccountNumber: freezed == bankAccountNumber ? _self.bankAccountNumber : bankAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,contactPerson: freezed == contactPerson ? _self.contactPerson : contactPerson // ignore: cast_nullable_to_non_nullable
as String?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Vendor implements Vendor {
  const _Vendor({@JsonKey(name: 'vendor_id') required this.vendorId, @JsonKey(name: 'vendor_name') required this.vendorName, @JsonKey(name: 'vendor_number') this.vendorNumber, @JsonKey(name: 'organization_number') this.organizationNumber, @JsonKey(name: 'is_active') this.isActive = true, this.phone, this.email, this.address, @JsonKey(name: 'zip_code') this.zipCode, this.city, this.country, @JsonKey(name: 'bank_account_type') this.bankAccountType, @JsonKey(name: 'bank_account_number') this.bankAccountNumber, @JsonKey(name: 'contact_person') this.contactPerson, @JsonKey(name: 'company_id') this.companyId});
  factory _Vendor.fromJson(Map<String, dynamic> json) => _$VendorFromJson(json);

@override@JsonKey(name: 'vendor_id') final  String vendorId;
@override@JsonKey(name: 'vendor_name') final  String vendorName;
@override@JsonKey(name: 'vendor_number') final  String? vendorNumber;
@override@JsonKey(name: 'organization_number') final  String? organizationNumber;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override final  String? phone;
@override final  String? email;
@override final  String? address;
@override@JsonKey(name: 'zip_code') final  String? zipCode;
@override final  String? city;
@override final  String? country;
@override@JsonKey(name: 'bank_account_type') final  String? bankAccountType;
@override@JsonKey(name: 'bank_account_number') final  String? bankAccountNumber;
@override@JsonKey(name: 'contact_person') final  String? contactPerson;
@override@JsonKey(name: 'company_id') final  int? companyId;

/// Create a copy of Vendor
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VendorCopyWith<_Vendor> get copyWith => __$VendorCopyWithImpl<_Vendor>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VendorToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Vendor&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.vendorName, vendorName) || other.vendorName == vendorName)&&(identical(other.vendorNumber, vendorNumber) || other.vendorNumber == vendorNumber)&&(identical(other.organizationNumber, organizationNumber) || other.organizationNumber == organizationNumber)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.zipCode, zipCode) || other.zipCode == zipCode)&&(identical(other.city, city) || other.city == city)&&(identical(other.country, country) || other.country == country)&&(identical(other.bankAccountType, bankAccountType) || other.bankAccountType == bankAccountType)&&(identical(other.bankAccountNumber, bankAccountNumber) || other.bankAccountNumber == bankAccountNumber)&&(identical(other.contactPerson, contactPerson) || other.contactPerson == contactPerson)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,vendorId,vendorName,vendorNumber,organizationNumber,isActive,phone,email,address,zipCode,city,country,bankAccountType,bankAccountNumber,contactPerson,companyId);

@override
String toString() {
  return 'Vendor(vendorId: $vendorId, vendorName: $vendorName, vendorNumber: $vendorNumber, organizationNumber: $organizationNumber, isActive: $isActive, phone: $phone, email: $email, address: $address, zipCode: $zipCode, city: $city, country: $country, bankAccountType: $bankAccountType, bankAccountNumber: $bankAccountNumber, contactPerson: $contactPerson, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$VendorCopyWith<$Res> implements $VendorCopyWith<$Res> {
  factory _$VendorCopyWith(_Vendor value, $Res Function(_Vendor) _then) = __$VendorCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'vendor_id') String vendorId,@JsonKey(name: 'vendor_name') String vendorName,@JsonKey(name: 'vendor_number') String? vendorNumber,@JsonKey(name: 'organization_number') String? organizationNumber,@JsonKey(name: 'is_active') bool isActive, String? phone, String? email, String? address,@JsonKey(name: 'zip_code') String? zipCode, String? city, String? country,@JsonKey(name: 'bank_account_type') String? bankAccountType,@JsonKey(name: 'bank_account_number') String? bankAccountNumber,@JsonKey(name: 'contact_person') String? contactPerson,@JsonKey(name: 'company_id') int? companyId
});




}
/// @nodoc
class __$VendorCopyWithImpl<$Res>
    implements _$VendorCopyWith<$Res> {
  __$VendorCopyWithImpl(this._self, this._then);

  final _Vendor _self;
  final $Res Function(_Vendor) _then;

/// Create a copy of Vendor
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? vendorId = null,Object? vendorName = null,Object? vendorNumber = freezed,Object? organizationNumber = freezed,Object? isActive = null,Object? phone = freezed,Object? email = freezed,Object? address = freezed,Object? zipCode = freezed,Object? city = freezed,Object? country = freezed,Object? bankAccountType = freezed,Object? bankAccountNumber = freezed,Object? contactPerson = freezed,Object? companyId = freezed,}) {
  return _then(_Vendor(
vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,vendorName: null == vendorName ? _self.vendorName : vendorName // ignore: cast_nullable_to_non_nullable
as String,vendorNumber: freezed == vendorNumber ? _self.vendorNumber : vendorNumber // ignore: cast_nullable_to_non_nullable
as String?,organizationNumber: freezed == organizationNumber ? _self.organizationNumber : organizationNumber // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,zipCode: freezed == zipCode ? _self.zipCode : zipCode // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,bankAccountType: freezed == bankAccountType ? _self.bankAccountType : bankAccountType // ignore: cast_nullable_to_non_nullable
as String?,bankAccountNumber: freezed == bankAccountNumber ? _self.bankAccountNumber : bankAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,contactPerson: freezed == contactPerson ? _self.contactPerson : contactPerson // ignore: cast_nullable_to_non_nullable
as String?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
