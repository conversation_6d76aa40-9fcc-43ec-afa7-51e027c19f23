// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vendor_invoice_line_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VendorInvoiceLineItem _$VendorInvoiceLineItemFromJson(
  Map<String, dynamic> json,
) => _VendorInvoiceLineItem(
  lineItemId: (json['line_item_id'] as num).toInt(),
  invoiceId: (json['invoice_id'] as num).toInt(),
  accountNumber: json['account_number'] as String,
  description: json['description'] as String,
  amount: (json['amount'] as num).toDouble(),
  taxAmount: (json['tax_amount'] as num).toDouble(),
  companyId: (json['company_id'] as num).toInt(),
  createdAt:
      json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
  updatedAt:
      json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$VendorInvoiceLineItemToJson(
  _VendorInvoiceLineItem instance,
) => <String, dynamic>{
  'line_item_id': instance.lineItemId,
  'invoice_id': instance.invoiceId,
  'account_number': instance.accountNumber,
  'description': instance.description,
  'amount': instance.amount,
  'tax_amount': instance.taxAmount,
  'company_id': instance.companyId,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
};
