// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'vendor_invoice_line_item.freezed.dart';
part 'vendor_invoice_line_item.g.dart';

@freezed
abstract class VendorInvoiceLineItem with _$VendorInvoiceLineItem {
  const factory VendorInvoiceLineItem({
    @Json<PERSON>ey(name: 'line_item_id') required int lineItemId,
    @<PERSON>son<PERSON><PERSON>(name: 'invoice_id') required int invoiceId,
    @<PERSON>son<PERSON><PERSON>(name: 'account_number') required String accountNumber,
    required String description,
    required double amount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'tax_amount') required double taxAmount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'company_id') required int companyId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
    @<PERSON>son<PERSON><PERSON>(name: 'updated_at') DateTime? updatedAt,
  }) = _VendorInvoiceLineItem;

  factory VendorInvoiceLineItem.fromJson(Map<String, dynamic> json) =>
      _$VendorInvoiceLineItemFromJson(json);
}
