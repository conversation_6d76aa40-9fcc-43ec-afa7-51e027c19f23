// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'vendor.freezed.dart';
part 'vendor.g.dart';

@freezed
abstract class Vendor with _$Vendor {
  const factory Vendor({
    @J<PERSON><PERSON>ey(name: 'vendor_id') required String vendorId,
    @<PERSON>son<PERSON><PERSON>(name: 'vendor_name') required String vendorName,
    @Json<PERSON>ey(name: 'vendor_number') String? vendorNumber,
    @JsonKey(name: 'organization_number') String? organizationNumber,
    @<PERSON>son<PERSON>ey(name: 'is_active') @Default(true) bool isActive,
    String? phone,
    String? email,
    String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'zip_code') String? zipCode,
    String? city,
    String? country,
    @<PERSON>son<PERSON><PERSON>(name: 'bank_account_type') String? bankAccountType,
    @JsonKey(name: 'bank_account_number') String? bankAccountNumber,
    @<PERSON>son<PERSON><PERSON>(name: 'contact_person') String? contact<PERSON>erson,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'company_id') int? companyId,
  }) = _Vendor;

  factory Vendor.fromJson(Map<String, dynamic> json) => _$VendorFromJson(json);
}
