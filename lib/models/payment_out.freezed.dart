// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_out.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PaymentOut {

 int get paymentOutId; int get expenseId; DateTime get paymentDate; double get amount; String get currencyCode; int? get companyId;
/// Create a copy of PaymentOut
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentOutCopyWith<PaymentOut> get copyWith => _$PaymentOutCopyWithImpl<PaymentOut>(this as PaymentOut, _$identity);

  /// Serializes this PaymentOut to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentOut&&(identical(other.paymentOutId, paymentOutId) || other.paymentOutId == paymentOutId)&&(identical(other.expenseId, expenseId) || other.expenseId == expenseId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentOutId,expenseId,paymentDate,amount,currencyCode,companyId);

@override
String toString() {
  return 'PaymentOut(paymentOutId: $paymentOutId, expenseId: $expenseId, paymentDate: $paymentDate, amount: $amount, currencyCode: $currencyCode, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $PaymentOutCopyWith<$Res>  {
  factory $PaymentOutCopyWith(PaymentOut value, $Res Function(PaymentOut) _then) = _$PaymentOutCopyWithImpl;
@useResult
$Res call({
 int paymentOutId, int expenseId, DateTime paymentDate, double amount, String currencyCode, int? companyId
});




}
/// @nodoc
class _$PaymentOutCopyWithImpl<$Res>
    implements $PaymentOutCopyWith<$Res> {
  _$PaymentOutCopyWithImpl(this._self, this._then);

  final PaymentOut _self;
  final $Res Function(PaymentOut) _then;

/// Create a copy of PaymentOut
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentOutId = null,Object? expenseId = null,Object? paymentDate = null,Object? amount = null,Object? currencyCode = null,Object? companyId = freezed,}) {
  return _then(_self.copyWith(
paymentOutId: null == paymentOutId ? _self.paymentOutId : paymentOutId // ignore: cast_nullable_to_non_nullable
as int,expenseId: null == expenseId ? _self.expenseId : expenseId // ignore: cast_nullable_to_non_nullable
as int,paymentDate: null == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PaymentOut implements PaymentOut {
  const _PaymentOut({required this.paymentOutId, required this.expenseId, required this.paymentDate, required this.amount, required this.currencyCode, this.companyId});
  factory _PaymentOut.fromJson(Map<String, dynamic> json) => _$PaymentOutFromJson(json);

@override final  int paymentOutId;
@override final  int expenseId;
@override final  DateTime paymentDate;
@override final  double amount;
@override final  String currencyCode;
@override final  int? companyId;

/// Create a copy of PaymentOut
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentOutCopyWith<_PaymentOut> get copyWith => __$PaymentOutCopyWithImpl<_PaymentOut>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentOutToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentOut&&(identical(other.paymentOutId, paymentOutId) || other.paymentOutId == paymentOutId)&&(identical(other.expenseId, expenseId) || other.expenseId == expenseId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentOutId,expenseId,paymentDate,amount,currencyCode,companyId);

@override
String toString() {
  return 'PaymentOut(paymentOutId: $paymentOutId, expenseId: $expenseId, paymentDate: $paymentDate, amount: $amount, currencyCode: $currencyCode, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$PaymentOutCopyWith<$Res> implements $PaymentOutCopyWith<$Res> {
  factory _$PaymentOutCopyWith(_PaymentOut value, $Res Function(_PaymentOut) _then) = __$PaymentOutCopyWithImpl;
@override @useResult
$Res call({
 int paymentOutId, int expenseId, DateTime paymentDate, double amount, String currencyCode, int? companyId
});




}
/// @nodoc
class __$PaymentOutCopyWithImpl<$Res>
    implements _$PaymentOutCopyWith<$Res> {
  __$PaymentOutCopyWithImpl(this._self, this._then);

  final _PaymentOut _self;
  final $Res Function(_PaymentOut) _then;

/// Create a copy of PaymentOut
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentOutId = null,Object? expenseId = null,Object? paymentDate = null,Object? amount = null,Object? currencyCode = null,Object? companyId = freezed,}) {
  return _then(_PaymentOut(
paymentOutId: null == paymentOutId ? _self.paymentOutId : paymentOutId // ignore: cast_nullable_to_non_nullable
as int,expenseId: null == expenseId ? _self.expenseId : expenseId // ignore: cast_nullable_to_non_nullable
as int,paymentDate: null == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
