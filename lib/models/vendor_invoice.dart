// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
// ignore: unused_import
import 'package:flutter/foundation.dart';

part 'vendor_invoice.freezed.dart';
part 'vendor_invoice.g.dart';

@freezed
abstract class VendorInvoice with _$VendorInvoice {
  const factory VendorInvoice({
    @JsonKey(name: 'invoice_id') required int invoiceId,
    @<PERSON>sonKey(name: 'vendor_id') required String vendorId,
    @Json<PERSON>ey(name: 'invoice_number') required String invoiceNumber,
    @Json<PERSON>ey(name: 'invoice_date') required DateTime invoiceDate,
    @<PERSON>son<PERSON>ey(name: 'due_date') required DateTime dueDate,
    required double amount,
    @<PERSON>son<PERSON><PERSON>(name: 'currency_code') required String currencyCode,
    @JsonKey(name: 'expense_account_number')
    required String expenseAccountNumber,
    @JsonKey(name: 'tax_amount') double? taxAmount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'project_id') int? projectId,
    @<PERSON>son<PERSON>ey(name: 'staff_id') int? staffId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'company_id') int? companyId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_paid') bool? isPaid,
  }) = _VendorInvoice;

  factory VendorInvoice.fromJson(Map<String, dynamic> json) =>
      _$VendorInvoiceFromJson(json);
}
