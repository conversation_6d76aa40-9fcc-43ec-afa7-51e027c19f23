// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vendor_invoice.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VendorInvoice implements DiagnosticableTreeMixin {

@JsonKey(name: 'invoice_id') int get invoiceId;@JsonKey(name: 'vendor_id') String get vendorId;@JsonKey(name: 'invoice_number') String get invoiceNumber;@JsonKey(name: 'invoice_date') DateTime get invoiceDate;@JsonKey(name: 'due_date') DateTime get dueDate; double get amount;@JsonKey(name: 'currency_code') String get currencyCode;@JsonKey(name: 'expense_account_number') String get expenseAccountNumber;@JsonKey(name: 'tax_amount') double? get taxAmount;@JsonKey(name: 'project_id') int? get projectId;@JsonKey(name: 'staff_id') int? get staffId;@JsonKey(name: 'company_id') int? get companyId;@JsonKey(name: 'is_paid') bool? get isPaid;
/// Create a copy of VendorInvoice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VendorInvoiceCopyWith<VendorInvoice> get copyWith => _$VendorInvoiceCopyWithImpl<VendorInvoice>(this as VendorInvoice, _$identity);

  /// Serializes this VendorInvoice to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VendorInvoice'))
    ..add(DiagnosticsProperty('invoiceId', invoiceId))..add(DiagnosticsProperty('vendorId', vendorId))..add(DiagnosticsProperty('invoiceNumber', invoiceNumber))..add(DiagnosticsProperty('invoiceDate', invoiceDate))..add(DiagnosticsProperty('dueDate', dueDate))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('currencyCode', currencyCode))..add(DiagnosticsProperty('expenseAccountNumber', expenseAccountNumber))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('projectId', projectId))..add(DiagnosticsProperty('staffId', staffId))..add(DiagnosticsProperty('companyId', companyId))..add(DiagnosticsProperty('isPaid', isPaid));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VendorInvoice&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.invoiceNumber, invoiceNumber) || other.invoiceNumber == invoiceNumber)&&(identical(other.invoiceDate, invoiceDate) || other.invoiceDate == invoiceDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.expenseAccountNumber, expenseAccountNumber) || other.expenseAccountNumber == expenseAccountNumber)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,invoiceId,vendorId,invoiceNumber,invoiceDate,dueDate,amount,currencyCode,expenseAccountNumber,taxAmount,projectId,staffId,companyId,isPaid);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VendorInvoice(invoiceId: $invoiceId, vendorId: $vendorId, invoiceNumber: $invoiceNumber, invoiceDate: $invoiceDate, dueDate: $dueDate, amount: $amount, currencyCode: $currencyCode, expenseAccountNumber: $expenseAccountNumber, taxAmount: $taxAmount, projectId: $projectId, staffId: $staffId, companyId: $companyId, isPaid: $isPaid)';
}


}

/// @nodoc
abstract mixin class $VendorInvoiceCopyWith<$Res>  {
  factory $VendorInvoiceCopyWith(VendorInvoice value, $Res Function(VendorInvoice) _then) = _$VendorInvoiceCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'invoice_id') int invoiceId,@JsonKey(name: 'vendor_id') String vendorId,@JsonKey(name: 'invoice_number') String invoiceNumber,@JsonKey(name: 'invoice_date') DateTime invoiceDate,@JsonKey(name: 'due_date') DateTime dueDate, double amount,@JsonKey(name: 'currency_code') String currencyCode,@JsonKey(name: 'expense_account_number') String expenseAccountNumber,@JsonKey(name: 'tax_amount') double? taxAmount,@JsonKey(name: 'project_id') int? projectId,@JsonKey(name: 'staff_id') int? staffId,@JsonKey(name: 'company_id') int? companyId,@JsonKey(name: 'is_paid') bool? isPaid
});




}
/// @nodoc
class _$VendorInvoiceCopyWithImpl<$Res>
    implements $VendorInvoiceCopyWith<$Res> {
  _$VendorInvoiceCopyWithImpl(this._self, this._then);

  final VendorInvoice _self;
  final $Res Function(VendorInvoice) _then;

/// Create a copy of VendorInvoice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? invoiceId = null,Object? vendorId = null,Object? invoiceNumber = null,Object? invoiceDate = null,Object? dueDate = null,Object? amount = null,Object? currencyCode = null,Object? expenseAccountNumber = null,Object? taxAmount = freezed,Object? projectId = freezed,Object? staffId = freezed,Object? companyId = freezed,Object? isPaid = freezed,}) {
  return _then(_self.copyWith(
invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,invoiceNumber: null == invoiceNumber ? _self.invoiceNumber : invoiceNumber // ignore: cast_nullable_to_non_nullable
as String,invoiceDate: null == invoiceDate ? _self.invoiceDate : invoiceDate // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,expenseAccountNumber: null == expenseAccountNumber ? _self.expenseAccountNumber : expenseAccountNumber // ignore: cast_nullable_to_non_nullable
as String,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,isPaid: freezed == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _VendorInvoice with DiagnosticableTreeMixin implements VendorInvoice {
  const _VendorInvoice({@JsonKey(name: 'invoice_id') required this.invoiceId, @JsonKey(name: 'vendor_id') required this.vendorId, @JsonKey(name: 'invoice_number') required this.invoiceNumber, @JsonKey(name: 'invoice_date') required this.invoiceDate, @JsonKey(name: 'due_date') required this.dueDate, required this.amount, @JsonKey(name: 'currency_code') required this.currencyCode, @JsonKey(name: 'expense_account_number') required this.expenseAccountNumber, @JsonKey(name: 'tax_amount') this.taxAmount, @JsonKey(name: 'project_id') this.projectId, @JsonKey(name: 'staff_id') this.staffId, @JsonKey(name: 'company_id') this.companyId, @JsonKey(name: 'is_paid') this.isPaid});
  factory _VendorInvoice.fromJson(Map<String, dynamic> json) => _$VendorInvoiceFromJson(json);

@override@JsonKey(name: 'invoice_id') final  int invoiceId;
@override@JsonKey(name: 'vendor_id') final  String vendorId;
@override@JsonKey(name: 'invoice_number') final  String invoiceNumber;
@override@JsonKey(name: 'invoice_date') final  DateTime invoiceDate;
@override@JsonKey(name: 'due_date') final  DateTime dueDate;
@override final  double amount;
@override@JsonKey(name: 'currency_code') final  String currencyCode;
@override@JsonKey(name: 'expense_account_number') final  String expenseAccountNumber;
@override@JsonKey(name: 'tax_amount') final  double? taxAmount;
@override@JsonKey(name: 'project_id') final  int? projectId;
@override@JsonKey(name: 'staff_id') final  int? staffId;
@override@JsonKey(name: 'company_id') final  int? companyId;
@override@JsonKey(name: 'is_paid') final  bool? isPaid;

/// Create a copy of VendorInvoice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VendorInvoiceCopyWith<_VendorInvoice> get copyWith => __$VendorInvoiceCopyWithImpl<_VendorInvoice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VendorInvoiceToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'VendorInvoice'))
    ..add(DiagnosticsProperty('invoiceId', invoiceId))..add(DiagnosticsProperty('vendorId', vendorId))..add(DiagnosticsProperty('invoiceNumber', invoiceNumber))..add(DiagnosticsProperty('invoiceDate', invoiceDate))..add(DiagnosticsProperty('dueDate', dueDate))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('currencyCode', currencyCode))..add(DiagnosticsProperty('expenseAccountNumber', expenseAccountNumber))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('projectId', projectId))..add(DiagnosticsProperty('staffId', staffId))..add(DiagnosticsProperty('companyId', companyId))..add(DiagnosticsProperty('isPaid', isPaid));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VendorInvoice&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.invoiceNumber, invoiceNumber) || other.invoiceNumber == invoiceNumber)&&(identical(other.invoiceDate, invoiceDate) || other.invoiceDate == invoiceDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.expenseAccountNumber, expenseAccountNumber) || other.expenseAccountNumber == expenseAccountNumber)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,invoiceId,vendorId,invoiceNumber,invoiceDate,dueDate,amount,currencyCode,expenseAccountNumber,taxAmount,projectId,staffId,companyId,isPaid);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'VendorInvoice(invoiceId: $invoiceId, vendorId: $vendorId, invoiceNumber: $invoiceNumber, invoiceDate: $invoiceDate, dueDate: $dueDate, amount: $amount, currencyCode: $currencyCode, expenseAccountNumber: $expenseAccountNumber, taxAmount: $taxAmount, projectId: $projectId, staffId: $staffId, companyId: $companyId, isPaid: $isPaid)';
}


}

/// @nodoc
abstract mixin class _$VendorInvoiceCopyWith<$Res> implements $VendorInvoiceCopyWith<$Res> {
  factory _$VendorInvoiceCopyWith(_VendorInvoice value, $Res Function(_VendorInvoice) _then) = __$VendorInvoiceCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'invoice_id') int invoiceId,@JsonKey(name: 'vendor_id') String vendorId,@JsonKey(name: 'invoice_number') String invoiceNumber,@JsonKey(name: 'invoice_date') DateTime invoiceDate,@JsonKey(name: 'due_date') DateTime dueDate, double amount,@JsonKey(name: 'currency_code') String currencyCode,@JsonKey(name: 'expense_account_number') String expenseAccountNumber,@JsonKey(name: 'tax_amount') double? taxAmount,@JsonKey(name: 'project_id') int? projectId,@JsonKey(name: 'staff_id') int? staffId,@JsonKey(name: 'company_id') int? companyId,@JsonKey(name: 'is_paid') bool? isPaid
});




}
/// @nodoc
class __$VendorInvoiceCopyWithImpl<$Res>
    implements _$VendorInvoiceCopyWith<$Res> {
  __$VendorInvoiceCopyWithImpl(this._self, this._then);

  final _VendorInvoice _self;
  final $Res Function(_VendorInvoice) _then;

/// Create a copy of VendorInvoice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? invoiceId = null,Object? vendorId = null,Object? invoiceNumber = null,Object? invoiceDate = null,Object? dueDate = null,Object? amount = null,Object? currencyCode = null,Object? expenseAccountNumber = null,Object? taxAmount = freezed,Object? projectId = freezed,Object? staffId = freezed,Object? companyId = freezed,Object? isPaid = freezed,}) {
  return _then(_VendorInvoice(
invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,invoiceNumber: null == invoiceNumber ? _self.invoiceNumber : invoiceNumber // ignore: cast_nullable_to_non_nullable
as String,invoiceDate: null == invoiceDate ? _self.invoiceDate : invoiceDate // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,expenseAccountNumber: null == expenseAccountNumber ? _self.expenseAccountNumber : expenseAccountNumber // ignore: cast_nullable_to_non_nullable
as String,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,isPaid: freezed == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
