import 'package:freezed_annotation/freezed_annotation.dart';

part 'staff_member.freezed.dart';
part 'staff_member.g.dart';

@freezed
abstract class StaffMember with _$StaffMember {
  const factory StaffMember({
    required int staffId,
    required String staffName,
    required String email,
    int? companyId,
  }) = _StaffMember;

  factory StaffMember.fromJson(Map<String, dynamic> json) =>
      _$StaffMemberFromJson(json);
}
