// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_ledger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GeneralLedger _$GeneralLedgerFromJson(Map<String, dynamic> json) =>
    _GeneralLedger(
      ledgerId: (json['ledgerId'] as num).toInt(),
      transactionId: json['transactionId'] as String,
      transactionDate: DateTime.parse(json['transactionDate'] as String),
      accountNumber: json['accountNumber'] as String,
      description: json['description'] as String,
      debit: (json['debit'] as num).toDouble(),
      credit: (json['credit'] as num).toDouble(),
      currencyCode: json['currencyCode'] as String,
      projectId: (json['projectId'] as num?)?.toInt(),
      staffId: (json['staffId'] as num?)?.toInt(),
      taxAmount: (json['taxAmount'] as num?)?.toDouble(),
      companyId: (json['companyId'] as num).toInt(),
    );

Map<String, dynamic> _$GeneralLedgerToJson(_GeneralLedger instance) =>
    <String, dynamic>{
      'ledgerId': instance.ledgerId,
      'transactionId': instance.transactionId,
      'transactionDate': instance.transactionDate.toIso8601String(),
      'accountNumber': instance.accountNumber,
      'description': instance.description,
      'debit': instance.debit,
      'credit': instance.credit,
      'currencyCode': instance.currencyCode,
      'projectId': instance.projectId,
      'staffId': instance.staffId,
      'taxAmount': instance.taxAmount,
      'companyId': instance.companyId,
    };
