/// A simplified model for displaying ledger entries in the UI
class LedgerEntry {
  /// The unique identifier for the ledger entry
  final int id;

  /// The date of the transaction
  final DateTime date;

  /// The account number
  final String accountNumber;

  /// The description of the transaction
  final String description;

  /// The amount of the transaction (positive for debit, negative for credit)
  final double amount;

  /// The running balance after this transaction
  final double? runningBalance;

  /// Constructor
  const LedgerEntry({
    required this.id,
    required this.date,
    required this.accountNumber,
    required this.description,
    required this.amount,
    this.runningBalance,
  });

  /// Create a copy of this LedgerEntry with the given fields replaced
  LedgerEntry copyWith({
    int? id,
    DateTime? date,
    String? accountNumber,
    String? description,
    double? amount,
    double? runningBalance,
  }) {
    return LedgerEntry(
      id: id ?? this.id,
      date: date ?? this.date,
      accountNumber: accountNumber ?? this.accountNumber,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      runningBalance: runningBalance ?? this.runningBalance,
    );
  }

  /// Create a LedgerEntry from a JSON object
  factory LedgerEntry.fromJson(Map<String, dynamic> json) {
    return LedgerEntry(
      id: json['id'] as int,
      date: DateTime.parse(json['date'] as String),
      accountNumber: json['accountNumber'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      runningBalance:
          json['runningBalance'] != null
              ? (json['runningBalance'] as num).toDouble()
              : null,
    );
  }

  /// Convert this LedgerEntry to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'accountNumber': accountNumber,
      'description': description,
      'amount': amount,
      'runningBalance': runningBalance,
    };
  }

  /// Create a LedgerEntry from a GeneralLedger object
  factory LedgerEntry.fromGeneralLedger(dynamic generalLedger) {
    return LedgerEntry(
      id: generalLedger.ledgerId,
      date: generalLedger.transactionDate,
      accountNumber: generalLedger.accountNumber,
      description: generalLedger.description,
      amount: generalLedger.debit - generalLedger.credit,
    );
  }
}
