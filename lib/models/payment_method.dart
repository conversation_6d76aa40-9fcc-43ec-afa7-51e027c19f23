import 'package:json_annotation/json_annotation.dart';

/// Enum representing different payment methods for expenses
enum PaymentMethod {
  @JsonValue('cash')
  cash,
  @JsonValue('credit_card')
  creditCard,
  @JsonValue('bank_transfer')
  bankTransfer,
  @JsonValue('check')
  check,
  @JsonValue('other')
  other,
}

/// Extension to handle JSON serialization for PaymentMethod
extension PaymentMethodExtension on PaymentMethod {
  String toJson() {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.creditCard:
        return 'credit_card';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
      case PaymentMethod.check:
        return 'check';
      case PaymentMethod.other:
        return 'other';
    }
  }

  static PaymentMethod fromJson(String json) {
    switch (json) {
      case 'cash':
        return PaymentMethod.cash;
      case 'credit_card':
        return PaymentMethod.creditCard;
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      case 'check':
        return PaymentMethod.check;
      case 'other':
        return PaymentMethod.other;
      default:
        return PaymentMethod.other;
    }
  }

  /// Returns a user-friendly display name for the payment method
  String get displayName {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}
