// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'expense.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Expense {

 int get expenseId; String get transactionId; String get vendorId; DateTime get expenseDate; double get amount; String get currencyCode; int? get projectId; int? get staffId; double? get taxAmount; int get companyId; PaymentMethod get paymentMethod; String? get creditCardNumber;
/// Create a copy of Expense
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ExpenseCopyWith<Expense> get copyWith => _$ExpenseCopyWithImpl<Expense>(this as Expense, _$identity);

  /// Serializes this Expense to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Expense&&(identical(other.expenseId, expenseId) || other.expenseId == expenseId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.expenseDate, expenseDate) || other.expenseDate == expenseDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.creditCardNumber, creditCardNumber) || other.creditCardNumber == creditCardNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,expenseId,transactionId,vendorId,expenseDate,amount,currencyCode,projectId,staffId,taxAmount,companyId,paymentMethod,creditCardNumber);

@override
String toString() {
  return 'Expense(expenseId: $expenseId, transactionId: $transactionId, vendorId: $vendorId, expenseDate: $expenseDate, amount: $amount, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId, paymentMethod: $paymentMethod, creditCardNumber: $creditCardNumber)';
}


}

/// @nodoc
abstract mixin class $ExpenseCopyWith<$Res>  {
  factory $ExpenseCopyWith(Expense value, $Res Function(Expense) _then) = _$ExpenseCopyWithImpl;
@useResult
$Res call({
 int expenseId, String transactionId, String vendorId, DateTime expenseDate, double amount, String currencyCode, int? projectId, int? staffId, double? taxAmount, int companyId, PaymentMethod paymentMethod, String? creditCardNumber
});




}
/// @nodoc
class _$ExpenseCopyWithImpl<$Res>
    implements $ExpenseCopyWith<$Res> {
  _$ExpenseCopyWithImpl(this._self, this._then);

  final Expense _self;
  final $Res Function(Expense) _then;

/// Create a copy of Expense
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? expenseId = null,Object? transactionId = null,Object? vendorId = null,Object? expenseDate = null,Object? amount = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = null,Object? paymentMethod = null,Object? creditCardNumber = freezed,}) {
  return _then(_self.copyWith(
expenseId: null == expenseId ? _self.expenseId : expenseId // ignore: cast_nullable_to_non_nullable
as int,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,expenseDate: null == expenseDate ? _self.expenseDate : expenseDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,creditCardNumber: freezed == creditCardNumber ? _self.creditCardNumber : creditCardNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Expense implements Expense {
  const _Expense({required this.expenseId, required this.transactionId, required this.vendorId, required this.expenseDate, required this.amount, required this.currencyCode, this.projectId, this.staffId, this.taxAmount, required this.companyId, required this.paymentMethod, this.creditCardNumber});
  factory _Expense.fromJson(Map<String, dynamic> json) => _$ExpenseFromJson(json);

@override final  int expenseId;
@override final  String transactionId;
@override final  String vendorId;
@override final  DateTime expenseDate;
@override final  double amount;
@override final  String currencyCode;
@override final  int? projectId;
@override final  int? staffId;
@override final  double? taxAmount;
@override final  int companyId;
@override final  PaymentMethod paymentMethod;
@override final  String? creditCardNumber;

/// Create a copy of Expense
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ExpenseCopyWith<_Expense> get copyWith => __$ExpenseCopyWithImpl<_Expense>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ExpenseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Expense&&(identical(other.expenseId, expenseId) || other.expenseId == expenseId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.vendorId, vendorId) || other.vendorId == vendorId)&&(identical(other.expenseDate, expenseDate) || other.expenseDate == expenseDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.creditCardNumber, creditCardNumber) || other.creditCardNumber == creditCardNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,expenseId,transactionId,vendorId,expenseDate,amount,currencyCode,projectId,staffId,taxAmount,companyId,paymentMethod,creditCardNumber);

@override
String toString() {
  return 'Expense(expenseId: $expenseId, transactionId: $transactionId, vendorId: $vendorId, expenseDate: $expenseDate, amount: $amount, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId, paymentMethod: $paymentMethod, creditCardNumber: $creditCardNumber)';
}


}

/// @nodoc
abstract mixin class _$ExpenseCopyWith<$Res> implements $ExpenseCopyWith<$Res> {
  factory _$ExpenseCopyWith(_Expense value, $Res Function(_Expense) _then) = __$ExpenseCopyWithImpl;
@override @useResult
$Res call({
 int expenseId, String transactionId, String vendorId, DateTime expenseDate, double amount, String currencyCode, int? projectId, int? staffId, double? taxAmount, int companyId, PaymentMethod paymentMethod, String? creditCardNumber
});




}
/// @nodoc
class __$ExpenseCopyWithImpl<$Res>
    implements _$ExpenseCopyWith<$Res> {
  __$ExpenseCopyWithImpl(this._self, this._then);

  final _Expense _self;
  final $Res Function(_Expense) _then;

/// Create a copy of Expense
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? expenseId = null,Object? transactionId = null,Object? vendorId = null,Object? expenseDate = null,Object? amount = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = null,Object? paymentMethod = null,Object? creditCardNumber = freezed,}) {
  return _then(_Expense(
expenseId: null == expenseId ? _self.expenseId : expenseId // ignore: cast_nullable_to_non_nullable
as int,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,vendorId: null == vendorId ? _self.vendorId : vendorId // ignore: cast_nullable_to_non_nullable
as String,expenseDate: null == expenseDate ? _self.expenseDate : expenseDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,creditCardNumber: freezed == creditCardNumber ? _self.creditCardNumber : creditCardNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
