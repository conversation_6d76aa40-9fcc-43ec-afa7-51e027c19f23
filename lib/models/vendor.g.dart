// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vendor.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Vendor _$VendorFromJson(Map<String, dynamic> json) => _Vendor(
  vendorId: json['vendor_id'] as String,
  vendorName: json['vendor_name'] as String,
  vendorNumber: json['vendor_number'] as String?,
  organizationNumber: json['organization_number'] as String?,
  isActive: json['is_active'] as bool? ?? true,
  phone: json['phone'] as String?,
  email: json['email'] as String?,
  address: json['address'] as String?,
  zipCode: json['zip_code'] as String?,
  city: json['city'] as String?,
  country: json['country'] as String?,
  bankAccountType: json['bank_account_type'] as String?,
  bankAccountNumber: json['bank_account_number'] as String?,
  contactPerson: json['contact_person'] as String?,
  companyId: (json['company_id'] as num?)?.toInt(),
);

Map<String, dynamic> _$VendorToJson(_Vendor instance) => <String, dynamic>{
  'vendor_id': instance.vendorId,
  'vendor_name': instance.vendorName,
  'vendor_number': instance.vendorNumber,
  'organization_number': instance.organizationNumber,
  'is_active': instance.isActive,
  'phone': instance.phone,
  'email': instance.email,
  'address': instance.address,
  'zip_code': instance.zipCode,
  'city': instance.city,
  'country': instance.country,
  'bank_account_type': instance.bankAccountType,
  'bank_account_number': instance.bankAccountNumber,
  'contact_person': instance.contactPerson,
  'company_id': instance.companyId,
};
