// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'general_ledger.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GeneralLedger {

 int get ledgerId; String get transactionId; DateTime get transactionDate; String get accountNumber; String get description; double get debit; double get credit; String get currencyCode; int? get projectId; int? get staffId; double? get taxAmount; int get companyId;
/// Create a copy of GeneralLedger
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GeneralLedgerCopyWith<GeneralLedger> get copyWith => _$GeneralLedgerCopyWithImpl<GeneralLedger>(this as GeneralLedger, _$identity);

  /// Serializes this GeneralLedger to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GeneralLedger&&(identical(other.ledgerId, ledgerId) || other.ledgerId == ledgerId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.transactionDate, transactionDate) || other.transactionDate == transactionDate)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.debit, debit) || other.debit == debit)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,ledgerId,transactionId,transactionDate,accountNumber,description,debit,credit,currencyCode,projectId,staffId,taxAmount,companyId);

@override
String toString() {
  return 'GeneralLedger(ledgerId: $ledgerId, transactionId: $transactionId, transactionDate: $transactionDate, accountNumber: $accountNumber, description: $description, debit: $debit, credit: $credit, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $GeneralLedgerCopyWith<$Res>  {
  factory $GeneralLedgerCopyWith(GeneralLedger value, $Res Function(GeneralLedger) _then) = _$GeneralLedgerCopyWithImpl;
@useResult
$Res call({
 int ledgerId, String transactionId, DateTime transactionDate, String accountNumber, String description, double debit, double credit, String currencyCode, int? projectId, int? staffId, double? taxAmount, int companyId
});




}
/// @nodoc
class _$GeneralLedgerCopyWithImpl<$Res>
    implements $GeneralLedgerCopyWith<$Res> {
  _$GeneralLedgerCopyWithImpl(this._self, this._then);

  final GeneralLedger _self;
  final $Res Function(GeneralLedger) _then;

/// Create a copy of GeneralLedger
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? ledgerId = null,Object? transactionId = null,Object? transactionDate = null,Object? accountNumber = null,Object? description = null,Object? debit = null,Object? credit = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = null,}) {
  return _then(_self.copyWith(
ledgerId: null == ledgerId ? _self.ledgerId : ledgerId // ignore: cast_nullable_to_non_nullable
as int,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,transactionDate: null == transactionDate ? _self.transactionDate : transactionDate // ignore: cast_nullable_to_non_nullable
as DateTime,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,debit: null == debit ? _self.debit : debit // ignore: cast_nullable_to_non_nullable
as double,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _GeneralLedger implements GeneralLedger {
  const _GeneralLedger({required this.ledgerId, required this.transactionId, required this.transactionDate, required this.accountNumber, required this.description, required this.debit, required this.credit, required this.currencyCode, this.projectId, this.staffId, this.taxAmount, required this.companyId});
  factory _GeneralLedger.fromJson(Map<String, dynamic> json) => _$GeneralLedgerFromJson(json);

@override final  int ledgerId;
@override final  String transactionId;
@override final  DateTime transactionDate;
@override final  String accountNumber;
@override final  String description;
@override final  double debit;
@override final  double credit;
@override final  String currencyCode;
@override final  int? projectId;
@override final  int? staffId;
@override final  double? taxAmount;
@override final  int companyId;

/// Create a copy of GeneralLedger
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GeneralLedgerCopyWith<_GeneralLedger> get copyWith => __$GeneralLedgerCopyWithImpl<_GeneralLedger>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GeneralLedgerToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GeneralLedger&&(identical(other.ledgerId, ledgerId) || other.ledgerId == ledgerId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.transactionDate, transactionDate) || other.transactionDate == transactionDate)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.description, description) || other.description == description)&&(identical(other.debit, debit) || other.debit == debit)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,ledgerId,transactionId,transactionDate,accountNumber,description,debit,credit,currencyCode,projectId,staffId,taxAmount,companyId);

@override
String toString() {
  return 'GeneralLedger(ledgerId: $ledgerId, transactionId: $transactionId, transactionDate: $transactionDate, accountNumber: $accountNumber, description: $description, debit: $debit, credit: $credit, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$GeneralLedgerCopyWith<$Res> implements $GeneralLedgerCopyWith<$Res> {
  factory _$GeneralLedgerCopyWith(_GeneralLedger value, $Res Function(_GeneralLedger) _then) = __$GeneralLedgerCopyWithImpl;
@override @useResult
$Res call({
 int ledgerId, String transactionId, DateTime transactionDate, String accountNumber, String description, double debit, double credit, String currencyCode, int? projectId, int? staffId, double? taxAmount, int companyId
});




}
/// @nodoc
class __$GeneralLedgerCopyWithImpl<$Res>
    implements _$GeneralLedgerCopyWith<$Res> {
  __$GeneralLedgerCopyWithImpl(this._self, this._then);

  final _GeneralLedger _self;
  final $Res Function(_GeneralLedger) _then;

/// Create a copy of GeneralLedger
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? ledgerId = null,Object? transactionId = null,Object? transactionDate = null,Object? accountNumber = null,Object? description = null,Object? debit = null,Object? credit = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = null,}) {
  return _then(_GeneralLedger(
ledgerId: null == ledgerId ? _self.ledgerId : ledgerId // ignore: cast_nullable_to_non_nullable
as int,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,transactionDate: null == transactionDate ? _self.transactionDate : transactionDate // ignore: cast_nullable_to_non_nullable
as DateTime,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,debit: null == debit ? _self.debit : debit // ignore: cast_nullable_to_non_nullable
as double,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
