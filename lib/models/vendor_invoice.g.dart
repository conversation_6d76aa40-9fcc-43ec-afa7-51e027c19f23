// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vendor_invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VendorInvoice _$VendorInvoiceFromJson(Map<String, dynamic> json) =>
    _VendorInvoice(
      invoiceId: (json['invoice_id'] as num).toInt(),
      vendorId: json['vendor_id'] as String,
      invoiceNumber: json['invoice_number'] as String,
      invoiceDate: DateTime.parse(json['invoice_date'] as String),
      dueDate: DateTime.parse(json['due_date'] as String),
      amount: (json['amount'] as num).toDouble(),
      currencyCode: json['currency_code'] as String,
      expenseAccountNumber: json['expense_account_number'] as String,
      taxAmount: (json['tax_amount'] as num?)?.toDouble(),
      projectId: (json['project_id'] as num?)?.toInt(),
      staffId: (json['staff_id'] as num?)?.toInt(),
      companyId: (json['company_id'] as num?)?.toInt(),
      isPaid: json['is_paid'] as bool?,
    );

Map<String, dynamic> _$VendorInvoiceToJson(_VendorInvoice instance) =>
    <String, dynamic>{
      'invoice_id': instance.invoiceId,
      'vendor_id': instance.vendorId,
      'invoice_number': instance.invoiceNumber,
      'invoice_date': instance.invoiceDate.toIso8601String(),
      'due_date': instance.dueDate.toIso8601String(),
      'amount': instance.amount,
      'currency_code': instance.currencyCode,
      'expense_account_number': instance.expenseAccountNumber,
      'tax_amount': instance.taxAmount,
      'project_id': instance.projectId,
      'staff_id': instance.staffId,
      'company_id': instance.companyId,
      'is_paid': instance.isPaid,
    };
