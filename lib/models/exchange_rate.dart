// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'exchange_rate.freezed.dart';
part 'exchange_rate.g.dart';

@freezed
abstract class ExchangeRate with _$ExchangeRate {
  const factory ExchangeRate({
    @JsonKey(name: 'rate_id') required int rateId,
    @<PERSON>sonKey(name: 'from_currency') required String fromCurrency,
    @JsonKey(name: 'to_currency') required String toCurrency,
    @<PERSON>sonKey(name: 'rate') required double rate,
    @<PERSON>sonKey(name: 'effective_date') required DateTime effectiveDate,
    @JsonKey(name: 'company_id') required int companyId,
  }) = _ExchangeRate;

  factory ExchangeRate.fromJson(Map<String, dynamic> json) =>
      _$ExchangeRateFromJson(json);
}
