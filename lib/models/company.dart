// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'company.freezed.dart';
part 'company.g.dart';

@freezed
abstract class Company with _$Company {
  const factory Company({
    @JsonKey(name: 'company_id') required int companyId,
    @<PERSON>sonKey(name: 'company_name') required String companyName,
    @JsonKey(name: 'organization_number') required String organizationNumber,
    @JsonKey(name: 'phone') String? phone,
    @<PERSON>son<PERSON>ey(name: 'email') String? email,
    @JsonKey(name: 'address') String? address,
    @<PERSON>sonKey(name: 'zip_code') String? zipCode,
    @<PERSON>son<PERSON>ey(name: 'city') required String city,
    @Json<PERSON>ey(name: 'country') required String country,
  }) = _Company;

  factory Company.fromJson(Map<String, dynamic> json) =>
      _$CompanyFromJson(json);
}
