// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'project.freezed.dart';
part 'project.g.dart';

@freezed
abstract class Project with _$Project {
  const factory Project({
    @<PERSON>son<PERSON>ey(name: 'project_id') required int projectId,
    @<PERSON>sonKey(name: 'project_code') required String projectCode,
    @JsonKey(name: 'project_name') required String projectName,
    String? description,
    @JsonKey(name: 'company_id') int? companyId,
  }) = _Project;

  factory Project.fromJson(Map<String, dynamic> json) =>
      _$ProjectFromJson(json);
}
