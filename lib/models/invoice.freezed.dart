// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invoice.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Invoice implements DiagnosticableTreeMixin {

 int get invoiceId; String get customerId; DateTime get invoiceDate; DateTime get dueDate; double get amount; String get currencyCode; int? get projectId; int? get staffId; double? get taxAmount; int? get companyId;
/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InvoiceCopyWith<Invoice> get copyWith => _$InvoiceCopyWithImpl<Invoice>(this as Invoice, _$identity);

  /// Serializes this Invoice to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Invoice'))
    ..add(DiagnosticsProperty('invoiceId', invoiceId))..add(DiagnosticsProperty('customerId', customerId))..add(DiagnosticsProperty('invoiceDate', invoiceDate))..add(DiagnosticsProperty('dueDate', dueDate))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('currencyCode', currencyCode))..add(DiagnosticsProperty('projectId', projectId))..add(DiagnosticsProperty('staffId', staffId))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('companyId', companyId));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Invoice&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.invoiceDate, invoiceDate) || other.invoiceDate == invoiceDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,invoiceId,customerId,invoiceDate,dueDate,amount,currencyCode,projectId,staffId,taxAmount,companyId);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Invoice(invoiceId: $invoiceId, customerId: $customerId, invoiceDate: $invoiceDate, dueDate: $dueDate, amount: $amount, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $InvoiceCopyWith<$Res>  {
  factory $InvoiceCopyWith(Invoice value, $Res Function(Invoice) _then) = _$InvoiceCopyWithImpl;
@useResult
$Res call({
 int invoiceId, String customerId, DateTime invoiceDate, DateTime dueDate, double amount, String currencyCode, int? projectId, int? staffId, double? taxAmount, int? companyId
});




}
/// @nodoc
class _$InvoiceCopyWithImpl<$Res>
    implements $InvoiceCopyWith<$Res> {
  _$InvoiceCopyWithImpl(this._self, this._then);

  final Invoice _self;
  final $Res Function(Invoice) _then;

/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? invoiceId = null,Object? customerId = null,Object? invoiceDate = null,Object? dueDate = null,Object? amount = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = freezed,}) {
  return _then(_self.copyWith(
invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,invoiceDate: null == invoiceDate ? _self.invoiceDate : invoiceDate // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Invoice with DiagnosticableTreeMixin implements Invoice {
  const _Invoice({required this.invoiceId, required this.customerId, required this.invoiceDate, required this.dueDate, required this.amount, required this.currencyCode, this.projectId, this.staffId, this.taxAmount, this.companyId});
  factory _Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);

@override final  int invoiceId;
@override final  String customerId;
@override final  DateTime invoiceDate;
@override final  DateTime dueDate;
@override final  double amount;
@override final  String currencyCode;
@override final  int? projectId;
@override final  int? staffId;
@override final  double? taxAmount;
@override final  int? companyId;

/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InvoiceCopyWith<_Invoice> get copyWith => __$InvoiceCopyWithImpl<_Invoice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InvoiceToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Invoice'))
    ..add(DiagnosticsProperty('invoiceId', invoiceId))..add(DiagnosticsProperty('customerId', customerId))..add(DiagnosticsProperty('invoiceDate', invoiceDate))..add(DiagnosticsProperty('dueDate', dueDate))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('currencyCode', currencyCode))..add(DiagnosticsProperty('projectId', projectId))..add(DiagnosticsProperty('staffId', staffId))..add(DiagnosticsProperty('taxAmount', taxAmount))..add(DiagnosticsProperty('companyId', companyId));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Invoice&&(identical(other.invoiceId, invoiceId) || other.invoiceId == invoiceId)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.invoiceDate, invoiceDate) || other.invoiceDate == invoiceDate)&&(identical(other.dueDate, dueDate) || other.dueDate == dueDate)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.projectId, projectId) || other.projectId == projectId)&&(identical(other.staffId, staffId) || other.staffId == staffId)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,invoiceId,customerId,invoiceDate,dueDate,amount,currencyCode,projectId,staffId,taxAmount,companyId);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Invoice(invoiceId: $invoiceId, customerId: $customerId, invoiceDate: $invoiceDate, dueDate: $dueDate, amount: $amount, currencyCode: $currencyCode, projectId: $projectId, staffId: $staffId, taxAmount: $taxAmount, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$InvoiceCopyWith<$Res> implements $InvoiceCopyWith<$Res> {
  factory _$InvoiceCopyWith(_Invoice value, $Res Function(_Invoice) _then) = __$InvoiceCopyWithImpl;
@override @useResult
$Res call({
 int invoiceId, String customerId, DateTime invoiceDate, DateTime dueDate, double amount, String currencyCode, int? projectId, int? staffId, double? taxAmount, int? companyId
});




}
/// @nodoc
class __$InvoiceCopyWithImpl<$Res>
    implements _$InvoiceCopyWith<$Res> {
  __$InvoiceCopyWithImpl(this._self, this._then);

  final _Invoice _self;
  final $Res Function(_Invoice) _then;

/// Create a copy of Invoice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? invoiceId = null,Object? customerId = null,Object? invoiceDate = null,Object? dueDate = null,Object? amount = null,Object? currencyCode = null,Object? projectId = freezed,Object? staffId = freezed,Object? taxAmount = freezed,Object? companyId = freezed,}) {
  return _then(_Invoice(
invoiceId: null == invoiceId ? _self.invoiceId : invoiceId // ignore: cast_nullable_to_non_nullable
as int,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,invoiceDate: null == invoiceDate ? _self.invoiceDate : invoiceDate // ignore: cast_nullable_to_non_nullable
as DateTime,dueDate: null == dueDate ? _self.dueDate : dueDate // ignore: cast_nullable_to_non_nullable
as DateTime,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as String,projectId: freezed == projectId ? _self.projectId : projectId // ignore: cast_nullable_to_non_nullable
as int?,staffId: freezed == staffId ? _self.staffId : staffId // ignore: cast_nullable_to_non_nullable
as int?,taxAmount: freezed == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double?,companyId: freezed == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
