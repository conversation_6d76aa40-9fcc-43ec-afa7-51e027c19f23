import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_out.freezed.dart';
part 'payment_out.g.dart';

@freezed
abstract class PaymentOut with _$PaymentOut {
  const factory PaymentOut({
    required int paymentOutId,
    required int expenseId,
    required DateTime paymentDate,
    required double amount,
    required String currencyCode,
    int? companyId,
  }) = _PaymentOut;

  factory PaymentOut.fromJson(Map<String, dynamic> json) =>
      _$PaymentOutFromJson(json);
}
