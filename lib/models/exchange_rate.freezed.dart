// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'exchange_rate.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ExchangeRate {

@JsonKey(name: 'rate_id') int get rateId;@JsonKey(name: 'from_currency') String get fromCurrency;@JsonKey(name: 'to_currency') String get toCurrency;@JsonKey(name: 'rate') double get rate;@JsonKey(name: 'effective_date') DateTime get effectiveDate;@JsonKey(name: 'company_id') int get companyId;
/// Create a copy of ExchangeRate
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ExchangeRateCopyWith<ExchangeRate> get copyWith => _$ExchangeRateCopyWithImpl<ExchangeRate>(this as ExchangeRate, _$identity);

  /// Serializes this ExchangeRate to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ExchangeRate&&(identical(other.rateId, rateId) || other.rateId == rateId)&&(identical(other.fromCurrency, fromCurrency) || other.fromCurrency == fromCurrency)&&(identical(other.toCurrency, toCurrency) || other.toCurrency == toCurrency)&&(identical(other.rate, rate) || other.rate == rate)&&(identical(other.effectiveDate, effectiveDate) || other.effectiveDate == effectiveDate)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,rateId,fromCurrency,toCurrency,rate,effectiveDate,companyId);

@override
String toString() {
  return 'ExchangeRate(rateId: $rateId, fromCurrency: $fromCurrency, toCurrency: $toCurrency, rate: $rate, effectiveDate: $effectiveDate, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class $ExchangeRateCopyWith<$Res>  {
  factory $ExchangeRateCopyWith(ExchangeRate value, $Res Function(ExchangeRate) _then) = _$ExchangeRateCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'rate_id') int rateId,@JsonKey(name: 'from_currency') String fromCurrency,@JsonKey(name: 'to_currency') String toCurrency,@JsonKey(name: 'rate') double rate,@JsonKey(name: 'effective_date') DateTime effectiveDate,@JsonKey(name: 'company_id') int companyId
});




}
/// @nodoc
class _$ExchangeRateCopyWithImpl<$Res>
    implements $ExchangeRateCopyWith<$Res> {
  _$ExchangeRateCopyWithImpl(this._self, this._then);

  final ExchangeRate _self;
  final $Res Function(ExchangeRate) _then;

/// Create a copy of ExchangeRate
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? rateId = null,Object? fromCurrency = null,Object? toCurrency = null,Object? rate = null,Object? effectiveDate = null,Object? companyId = null,}) {
  return _then(_self.copyWith(
rateId: null == rateId ? _self.rateId : rateId // ignore: cast_nullable_to_non_nullable
as int,fromCurrency: null == fromCurrency ? _self.fromCurrency : fromCurrency // ignore: cast_nullable_to_non_nullable
as String,toCurrency: null == toCurrency ? _self.toCurrency : toCurrency // ignore: cast_nullable_to_non_nullable
as String,rate: null == rate ? _self.rate : rate // ignore: cast_nullable_to_non_nullable
as double,effectiveDate: null == effectiveDate ? _self.effectiveDate : effectiveDate // ignore: cast_nullable_to_non_nullable
as DateTime,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ExchangeRate implements ExchangeRate {
  const _ExchangeRate({@JsonKey(name: 'rate_id') required this.rateId, @JsonKey(name: 'from_currency') required this.fromCurrency, @JsonKey(name: 'to_currency') required this.toCurrency, @JsonKey(name: 'rate') required this.rate, @JsonKey(name: 'effective_date') required this.effectiveDate, @JsonKey(name: 'company_id') required this.companyId});
  factory _ExchangeRate.fromJson(Map<String, dynamic> json) => _$ExchangeRateFromJson(json);

@override@JsonKey(name: 'rate_id') final  int rateId;
@override@JsonKey(name: 'from_currency') final  String fromCurrency;
@override@JsonKey(name: 'to_currency') final  String toCurrency;
@override@JsonKey(name: 'rate') final  double rate;
@override@JsonKey(name: 'effective_date') final  DateTime effectiveDate;
@override@JsonKey(name: 'company_id') final  int companyId;

/// Create a copy of ExchangeRate
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ExchangeRateCopyWith<_ExchangeRate> get copyWith => __$ExchangeRateCopyWithImpl<_ExchangeRate>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ExchangeRateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ExchangeRate&&(identical(other.rateId, rateId) || other.rateId == rateId)&&(identical(other.fromCurrency, fromCurrency) || other.fromCurrency == fromCurrency)&&(identical(other.toCurrency, toCurrency) || other.toCurrency == toCurrency)&&(identical(other.rate, rate) || other.rate == rate)&&(identical(other.effectiveDate, effectiveDate) || other.effectiveDate == effectiveDate)&&(identical(other.companyId, companyId) || other.companyId == companyId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,rateId,fromCurrency,toCurrency,rate,effectiveDate,companyId);

@override
String toString() {
  return 'ExchangeRate(rateId: $rateId, fromCurrency: $fromCurrency, toCurrency: $toCurrency, rate: $rate, effectiveDate: $effectiveDate, companyId: $companyId)';
}


}

/// @nodoc
abstract mixin class _$ExchangeRateCopyWith<$Res> implements $ExchangeRateCopyWith<$Res> {
  factory _$ExchangeRateCopyWith(_ExchangeRate value, $Res Function(_ExchangeRate) _then) = __$ExchangeRateCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'rate_id') int rateId,@JsonKey(name: 'from_currency') String fromCurrency,@JsonKey(name: 'to_currency') String toCurrency,@JsonKey(name: 'rate') double rate,@JsonKey(name: 'effective_date') DateTime effectiveDate,@JsonKey(name: 'company_id') int companyId
});




}
/// @nodoc
class __$ExchangeRateCopyWithImpl<$Res>
    implements _$ExchangeRateCopyWith<$Res> {
  __$ExchangeRateCopyWithImpl(this._self, this._then);

  final _ExchangeRate _self;
  final $Res Function(_ExchangeRate) _then;

/// Create a copy of ExchangeRate
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? rateId = null,Object? fromCurrency = null,Object? toCurrency = null,Object? rate = null,Object? effectiveDate = null,Object? companyId = null,}) {
  return _then(_ExchangeRate(
rateId: null == rateId ? _self.rateId : rateId // ignore: cast_nullable_to_non_nullable
as int,fromCurrency: null == fromCurrency ? _self.fromCurrency : fromCurrency // ignore: cast_nullable_to_non_nullable
as String,toCurrency: null == toCurrency ? _self.toCurrency : toCurrency // ignore: cast_nullable_to_non_nullable
as String,rate: null == rate ? _self.rate : rate // ignore: cast_nullable_to_non_nullable
as double,effectiveDate: null == effectiveDate ? _self.effectiveDate : effectiveDate // ignore: cast_nullable_to_non_nullable
as DateTime,companyId: null == companyId ? _self.companyId : companyId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
