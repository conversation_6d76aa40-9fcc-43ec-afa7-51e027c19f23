// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Invoice _$InvoiceFromJson(Map<String, dynamic> json) => _Invoice(
  invoiceId: (json['invoiceId'] as num).toInt(),
  customerId: json['customerId'] as String,
  invoiceDate: DateTime.parse(json['invoiceDate'] as String),
  dueDate: DateTime.parse(json['dueDate'] as String),
  amount: (json['amount'] as num).toDouble(),
  currencyCode: json['currencyCode'] as String,
  projectId: (json['projectId'] as num?)?.toInt(),
  staffId: (json['staffId'] as num?)?.toInt(),
  taxAmount: (json['taxAmount'] as num?)?.toDouble(),
  companyId: (json['companyId'] as num?)?.toInt(),
);

Map<String, dynamic> _$InvoiceToJson(_Invoice instance) => <String, dynamic>{
  'invoiceId': instance.invoiceId,
  'customerId': instance.customerId,
  'invoiceDate': instance.invoiceDate.toIso8601String(),
  'dueDate': instance.dueDate.toIso8601String(),
  'amount': instance.amount,
  'currencyCode': instance.currencyCode,
  'projectId': instance.projectId,
  'staffId': instance.staffId,
  'taxAmount': instance.taxAmount,
  'companyId': instance.companyId,
};
