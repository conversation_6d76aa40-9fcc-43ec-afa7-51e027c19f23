import 'package:freezed_annotation/freezed_annotation.dart';

part 'general_ledger.freezed.dart';
part 'general_ledger.g.dart';

@freezed
abstract class GeneralLedger with _$GeneralLedger {
  const factory GeneralLedger({
    required int ledgerId,
    required String transactionId,
    required DateTime transactionDate,
    required String accountNumber,
    required String description,
    required double debit,
    required double credit,
    required String currencyCode,
    int? projectId,
    int? staffId,
    double? taxAmount,
    required int companyId,
  }) = _GeneralLedger;

  factory GeneralLedger.fromJson(Map<String, dynamic> json) =>
      _$GeneralLedgerFromJson(json);
}
