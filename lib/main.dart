import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:we_like_money/config/supabase_config.dart';
import 'package:we_like_money/config/app_config.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/app.dart';
import 'dart:io';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment variables
  bool envLoaded = false;

  // Print current working directory for debugging
  final workingDir = Directory.current.path;
  debugPrint('Current working directory: $workingDir');

  // Check if .env file exists at the expected locations
  final rootEnvFile = File('.env');
  final assetsEnvFile = File('assets/.env');
  final testEnvFile = File('test/.env');

  debugPrint('Root .env exists: ${rootEnvFile.existsSync()}');
  debugPrint('Assets .env exists: ${assetsEnvFile.existsSync()}');
  debugPrint('Test .env exists: ${testEnvFile.existsSync()}');

  // Try loading from absolute path
  final absolutePath = '$workingDir/.env';
  final absoluteFile = File(absolutePath);
  debugPrint('Absolute .env path: $absolutePath');
  debugPrint('Absolute .env exists: ${absoluteFile.existsSync()}');

  // Try loading from the root directory
  try {
    await dotenv.dotenv.load(fileName: ".env");
    debugPrint('Environment variables loaded from root directory.');
    envLoaded = true;
  } catch (e) {
    debugPrint('Failed to load .env from root directory: $e');
  }

  // If not found, try loading from the assets directory
  if (!envLoaded) {
    try {
      await dotenv.dotenv.load(fileName: "assets/.env");
      debugPrint('Environment variables loaded from assets directory.');
      envLoaded = true;
    } catch (e) {
      debugPrint('Failed to load .env from assets directory: $e');
    }
  }

  // If not found, try loading from the test directory
  if (!envLoaded) {
    try {
      await dotenv.dotenv.load(fileName: "test/.env");
      debugPrint('Environment variables loaded from test directory.');
      envLoaded = true;
    } catch (e) {
      debugPrint('Failed to load .env from test directory: $e');
    }
  }

  // Try loading from absolute path
  if (!envLoaded) {
    try {
      await dotenv.dotenv.load(fileName: absolutePath);
      debugPrint('Environment variables loaded from absolute path.');
      envLoaded = true;
    } catch (e) {
      debugPrint('Failed to load .env from absolute path: $e');
    }
  }

  // Try loading from Flutter asset bundle
  if (!envLoaded) {
    try {
      debugPrint('Attempting to load .env from Flutter asset bundle...');
      String envString = await rootBundle.loadString('.env');
      dotenv.dotenv.testLoad(fileInput: envString);
      debugPrint('Environment variables loaded from Flutter asset bundle.');
      envLoaded = true;
    } catch (e) {
      debugPrint('Failed to load .env from Flutter asset bundle: $e');
    }
  }

  // If no .env file was loaded, manually initialize the dotenv instance
  if (!envLoaded) {
    debugPrint('Using hardcoded default values for environment variables.');

    // Manually initialize the dotenv instance with default values
    dotenv.dotenv.testLoad(
      fileInput: '''
# Supabase Configuration
SUPABASE_URL=https://placeholder-project.supabase.co
SUPABASE_ANON_KEY=placeholder-anon-key

# App Configuration
APP_NAME=We Like Money
APP_ENV=development
''',
    );
  }

  // Print loaded environment variables (only non-sensitive ones)
  final supabaseUrl = dotenv.dotenv.env['SUPABASE_URL'];
  debugPrint('Loaded SUPABASE_URL: ${supabaseUrl?.substring(0, 8)}...');
  debugPrint('APP_NAME: ${dotenv.dotenv.env['APP_NAME']}');
  debugPrint('APP_ENV: ${dotenv.dotenv.env['APP_ENV']}');

  // Load app configuration settings
  final appConfig = AppConfig();
  await appConfig.loadSavedMode();

  // Initialize Supabase
  await initializeSupabase();

  // Initialize dependency injection
  await configureDependencies();

  runApp(const App());
}
