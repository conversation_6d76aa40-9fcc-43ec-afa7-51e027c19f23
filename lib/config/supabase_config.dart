import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:flutter/foundation.dart';
import 'package:we_like_money/config/app_config.dart';

// Track whether Supabase has been initialized
bool _isSupabaseInitialized = false;

/// Initializes Supabase with environment variables.
///
/// This function should be called during app startup before
/// any Supabase-dependent services are used.
Future<void> initializeSupabase() async {
  // Check if we should force mock mode
  final appConfig = AppConfig();
  if (appConfig.shouldUseMockMode()) {
    debugPrint('Using mock mode based on app configuration.');
    _isSupabaseInitialized = false;
    return;
  }

  try {
    // Get environment variables with fallbacks
    final url = _getEnvWithFallback(
      'SUPABASE_URL',
      'https://placeholder-project.supabase.co',
    );
    final anonKey = _getEnvWithFallback(
      'SUPABASE_ANON_KEY',
      'placeholder-anon-key',
    );

    if (url == 'https://placeholder-project.supabase.co' ||
        anonKey == 'placeholder-anon-key') {
      debugPrint('Warning: Using placeholder Supabase credentials.');
      debugPrint('Using development mode with mock data.');
      _isSupabaseInitialized = false;
      return; // Skip initialization if using placeholder credentials
    }

    await Supabase.initialize(url: url, anonKey: anonKey);
    _isSupabaseInitialized = true;
    debugPrint('Supabase initialized successfully.');
  } catch (e) {
    debugPrint('Error initializing Supabase: $e');
    _isSupabaseInitialized = false;
    // Continue without Supabase in development mode
    if (kReleaseMode) {
      // In release mode, rethrow the error as this is critical
      rethrow;
    }
  }
}

/// Gets an environment variable with a fallback value if not found.
String _getEnvWithFallback(String key, String fallback) {
  try {
    final value = dotenv.dotenv.get(key, fallback: fallback);
    return value;
  } catch (e) {
    debugPrint('Error getting environment variable $key: $e');
    return fallback;
  }
}

/// Provides the Supabase client as a singleton.
@singleton
@injectable
class SupabaseClientProvider {
  final AppConfig _appConfig = AppConfig();

  /// Returns the Supabase client instance.
  ///
  /// If Supabase is not initialized, this will return null in debug mode
  /// and throw an exception in release mode.
  SupabaseClient? getClient() {
    // Check if we should be in mock mode
    if (_appConfig.shouldUseMockMode()) {
      debugPrint('Returning null client due to mock mode setting');
      return null;
    }

    if (!_isSupabaseInitialized) {
      debugPrint('Supabase is not initialized, returning null client');
      return null;
    }

    try {
      return Supabase.instance.client;
    } catch (e) {
      debugPrint('Error getting Supabase client: $e');
      if (kReleaseMode && _appConfig.databaseMode == DatabaseMode.remoteOnly) {
        // Only throw in release mode if explicitly configured for remote only
        rethrow;
      }
      return null;
    }
  }
}
