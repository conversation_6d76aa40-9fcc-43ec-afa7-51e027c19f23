import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Defines the database connection mode
enum DatabaseMode {
  /// Auto mode decides based on environment (mock for debug, remote for release)
  auto,

  /// Always use mock data, never connect to remote database
  mockOnly,

  /// Always try to connect to remote database
  remoteOnly,
}

/// Global application configuration settings
@singleton
class AppConfig {
  static final AppConfig _instance = AppConfig._internal();

  factory AppConfig() => _instance;

  AppConfig._internal();

  DatabaseMode _databaseMode = DatabaseMode.auto;

  /// Gets the current database connection mode
  DatabaseMode get databaseMode => _databaseMode;

  /// Sets the database connection mode and persists it to shared preferences
  Future<void> setDatabaseMode(DatabaseMode mode) async {
    _databaseMode = mode;

    // Save to persistent storage
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('database_mode', mode.toString());
      debugPrint('Database mode set to: $_databaseMode');
    } catch (e) {
      debugPrint('Error saving database mode: $e');
    }
  }

  /// Loads the saved database mode from shared preferences
  Future<void> loadSavedMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMode = prefs.getString('database_mode');

      if (savedMode != null) {
        switch (savedMode) {
          case 'DatabaseMode.auto':
            _databaseMode = DatabaseMode.auto;
            break;
          case 'DatabaseMode.mockOnly':
            _databaseMode = DatabaseMode.mockOnly;
            break;
          case 'DatabaseMode.remoteOnly':
            _databaseMode = DatabaseMode.remoteOnly;
            break;
          default:
            _databaseMode = DatabaseMode.auto;
        }

        debugPrint('Loaded saved database mode: $_databaseMode');
      }
    } catch (e) {
      debugPrint('Error loading database mode: $e');
    }
  }

  /// Determines if mock mode should be used based on current settings
  bool shouldUseMockMode() {
    switch (_databaseMode) {
      case DatabaseMode.auto:
        // In auto mode, use mock in debug mode and remote in release mode
        return kDebugMode;
      case DatabaseMode.mockOnly:
        return true;
      case DatabaseMode.remoteOnly:
        return false;
    }
  }
}
