import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

/// Function type for printing messages
typedef PrintFunction = void Function(String message);

/// A simple logger class for the application
@injectable
class Logger {
  /// The print function to use
  final PrintFunction _print;

  /// Whether debug mode is enabled
  final bool _debugMode;

  /// Creates a new Logger instance
  ///
  /// @param print The print function to use (defaults to <PERSON><PERSON>'s print)
  /// @param debugMode Whether debug mode is enabled (defaults to kDebugMode)
  Logger({PrintFunction? print, bool? debugMode})
    : _print = print ?? debugPrint,
      _debugMode = debugMode ?? kDebugMode;

  /// Log an info message
  void info(String message) {
    if (_debugMode) {
      _print('ℹ️ INFO: $message');
    }
  }

  /// Log a warning message
  void warning(String message) {
    if (_debugMode) {
      _print('⚠️ WARNING: $message');
    }
  }

  /// Log an error message
  void error(String message, [dynamic exception, StackTrace? stackTrace]) {
    if (_debugMode) {
      _print('❌ ERROR: $message');
      if (exception != null) {
        _print('Exception: $exception');
      }
      if (stackTrace != null) {
        _print('Stack trace: $stackTrace');
      }
    }
  }

  /// Log a debug message
  void debug(String message) {
    if (_debugMode) {
      _print('🔍 DEBUG: $message');
    }
  }
}
