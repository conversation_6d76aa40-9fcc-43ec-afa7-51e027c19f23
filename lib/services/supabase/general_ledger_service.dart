import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/services/supabase/base_supabase_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';

/// Service for general ledger-related operations using Supabase.
class GeneralLedgerService extends BaseSupabaseService {
  final MockDataProvider _mockDataProvider;

  /// Creates a new instance of GeneralLedgerService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param mockDataProvider The provider for mock data
  /// @param useMockData Whether to use mock data instead of real database operations
  GeneralLedgerService(
    super.clientProvider,
    super.errorHandler,
    this._mockDataProvider, {
    super.useMockData,
  });

  /// Retrieves all general ledger entries.
  ///
  /// @return A list of GeneralLedger objects
  Future<List<GeneralLedger>> getGeneralLedgerEntries() async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<GeneralLedger>');
        return _mockDataProvider.getMockListData<List<GeneralLedger>>();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!.from('general_ledger').select();
        return (response as List)
            .map((json) => GeneralLedger.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get general ledger entries: $e');
      }
    });
  }

  /// Retrieves general ledger entries by account number.
  ///
  /// @param accountNumber The account number to filter by
  /// @return A list of GeneralLedger objects for the specified account
  Future<List<GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String accountNumber,
  ) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<GeneralLedger>');
        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        return entries
            .where((entry) => entry.accountNumber == accountNumber)
            .toList();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!
            .from('general_ledger')
            .select()
            .eq('account_number', accountNumber);
        return (response as List)
            .map((json) => GeneralLedger.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get general ledger entries by account: $e');
      }
    });
  }

  /// Retrieves general ledger entries by transaction ID.
  ///
  /// @param transactionId The transaction ID to filter by
  /// @return A list of GeneralLedger objects for the specified transaction
  Future<List<GeneralLedger>> getLedgerEntriesByTransactionId(
    String transactionId,
  ) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<GeneralLedger>');
        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        return entries
            .where((entry) => entry.transactionId == transactionId)
            .toList();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!
            .from('general_ledger')
            .select()
            .eq('transaction_id', transactionId);
        return (response as List)
            .map((json) => GeneralLedger.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get ledger entries by transaction ID: $e');
      }
    });
  }

  /// Retrieves a general ledger entry by its ID.
  ///
  /// @param id The ID of the general ledger entry to retrieve
  /// @return The GeneralLedger object if found, null otherwise
  Future<GeneralLedger?> getGeneralLedgerEntryById(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type GeneralLedger?');

        // For test case: return null for invalid ID
        if (id < 0) {
          return null;
        }

        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        debugPrint('Found ${entries.length} entries');
        for (final entry in entries) {
          debugPrint(
            'Entry: ${entry.ledgerId}, ${entry.accountNumber}, ${entry.description}',
          );
        }

        try {
          final result = entries.firstWhere((entry) => entry.ledgerId == id);
          debugPrint('Retrieved entry: $result');
          return result;
        } catch (e) {
          debugPrint('Entry not found: $e');
          return null;
        }
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('general_ledger')
                .select()
                .eq('ledger_id', id)
                .maybeSingle();

        if (response == null) {
          return null;
        }

        return GeneralLedger.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        debugPrint('Error fetching general ledger entry: $e');
        return null;
      }
    });
  }

  /// Creates a new general ledger entry.
  ///
  /// @param entry The general ledger entry to create
  /// @return The created GeneralLedger object
  Future<GeneralLedger> createGeneralLedgerEntry(GeneralLedger entry) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type GeneralLedger');

        // Generate a new ID for the general ledger entry
        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        final newId =
            entries.isEmpty
                ? 1
                : entries
                        .map((e) => e.ledgerId)
                        .reduce((a, b) => a > b ? a : b) +
                    1;
        final newEntry = entry.copyWith(ledgerId: newId);

        // Add the general ledger entry to the mock data
        final updatedEntries = List<GeneralLedger>.from(entries);
        updatedEntries.add(newEntry);
        _mockDataProvider.setMockData<List<GeneralLedger>>(updatedEntries);

        // Also update the single entity mock data
        _mockDataProvider.setMockData<GeneralLedger>(newEntry);

        return newEntry;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('general_ledger')
                .insert(convertCamelToSnake(entry.toJson()))
                .select()
                .single();
        return GeneralLedger.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to create general ledger entry: $e');
      }
    });
  }

  /// Updates an existing general ledger entry.
  ///
  /// @param entry The general ledger entry to update
  /// @return The updated GeneralLedger object
  Future<GeneralLedger> updateGeneralLedgerEntry(GeneralLedger entry) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type GeneralLedger');

        // Update the mock data
        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        final index = entries.indexWhere((e) => e.ledgerId == entry.ledgerId);

        if (index >= 0) {
          // Update the mock data
          final updatedEntries = List<GeneralLedger>.from(entries);
          updatedEntries[index] = entry;
          _mockDataProvider.setMockData<List<GeneralLedger>>(updatedEntries);

          // Also update the single entity mock data
          _mockDataProvider.setMockData<GeneralLedger>(entry);
        } else {
          throw Exception('General ledger entry not found');
        }

        return entry;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('general_ledger')
                .update(convertCamelToSnake(entry.toJson()))
                .eq('ledger_id', entry.ledgerId)
                .select()
                .single();
        return GeneralLedger.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to update general ledger entry: $e');
      }
    });
  }

  /// Deletes a general ledger entry.
  ///
  /// @param id The ID of the general ledger entry to delete
  Future<void> deleteGeneralLedgerEntry(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type void');

        // Verify general ledger entry exists before deletion
        final entryBeforeDelete = await getGeneralLedgerEntryById(id);
        if (entryBeforeDelete == null) {
          throw Exception('General ledger entry not found');
        }

        // Remove the general ledger entry from the mock data
        final entries =
            await _mockDataProvider.getMockListData<List<GeneralLedger>>();
        final updatedEntries =
            entries.where((entry) => entry.ledgerId != id).toList();
        _mockDataProvider.setMockData<List<GeneralLedger>>(updatedEntries);

        return;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        await client!.from('general_ledger').delete().eq('ledger_id', id);
      } catch (e) {
        throw Exception('Failed to delete general ledger entry: $e');
      }
    });
  }
}
