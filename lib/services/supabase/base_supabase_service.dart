import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:we_like_money/config/supabase_config.dart';
import 'package:we_like_money/utils/error_handler.dart';

/// Base class for all Supabase services.
///
/// This class provides common functionality for all Supabase services:
/// - Access to the Supabase client
/// - Error handling
/// - Mock mode support
/// - Common database operations
abstract class BaseSupabaseService {
  final SupabaseClientProvider _clientProvider;
  final ErrorHandler _errorHandler;
  bool useMockData = false;

  /// Creates a new instance of BaseSupabaseService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param useMockData Whether to use mock data instead of real database operations
  BaseSupabaseService(
    this._clientProvider,
    this._errorHandler, {
    bool useMockData = false,
  });

  /// Gets the Supabase client.
  SupabaseClient? get client => _clientProvider.getClient();

  /// Gets the error handler.
  ErrorHandler get errorHandler => _errorHandler;

  /// Executes a database operation with error handling.
  ///
  /// This method:
  /// - Executes the provided operation
  /// - Catches and handles any errors
  /// - Returns the result of the operation
  ///
  /// @param operation The database operation to execute
  /// @return The result of the operation
  /// @throws Exception if the operation fails
  Future<T> executeDbOperation<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      debugPrint('Error executing database operation: $e');
      debugPrint('Stack trace: $stackTrace');

      if (e is PostgrestException) {
        debugPrint('Postgrest error: ${e.message}');
      }

      rethrow;
    }
  }

  /// Converts a map with snake_case keys to camelCase.
  ///
  /// @param map The map to convert
  /// @return A new map with camelCase keys
  Map<String, dynamic> convertSnakeToCamel(Map<String, dynamic> map) {
    final result = <String, dynamic>{};
    map.forEach((key, value) {
      final camelKey = key.replaceAllMapped(
        RegExp(r'_([a-z])'),
        (match) => match.group(1)!.toUpperCase(),
      );
      result[camelKey] = value;
    });
    return result;
  }

  /// Converts a map with camelCase keys to snake_case.
  ///
  /// @param map The map to convert
  /// @return A new map with snake_case keys
  Map<String, dynamic> convertCamelToSnake(Map<String, dynamic> map) {
    final result = <String, dynamic>{};
    map.forEach((key, value) {
      final snakeKey = key.replaceAllMapped(
        RegExp(r'([A-Z])'),
        (match) => '_${match.group(1)!.toLowerCase()}',
      );
      result[snakeKey] = value;
    });
    return result;
  }
}
