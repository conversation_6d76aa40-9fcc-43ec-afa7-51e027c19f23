import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/services/supabase/base_supabase_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';

/// Service for vendor invoice-related operations using Supabase.
class VendorInvoiceService extends BaseSupabaseService {
  final MockDataProvider _mockDataProvider;

  /// Creates a new instance of VendorInvoiceService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param mockDataProvider The provider for mock data
  /// @param useMockData Whether to use mock data instead of real database operations
  VendorInvoiceService(
    super.clientProvider,
    super.errorHandler,
    this._mockDataProvider, {
    super.useMockData,
  });

  /// Retrieves all vendor invoices.
  ///
  /// @return A list of VendorInvoice objects
  Future<List<VendorInvoice>> getVendorInvoices() async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<VendorInvoice>');
        return _mockDataProvider.getMockListData<List<VendorInvoice>>();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!.from('vendor_invoices').select();
        return (response as List)
            .map((json) => VendorInvoice.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get vendor invoices: $e');
      }
    });
  }

  /// Retrieves a vendor invoice by its ID.
  ///
  /// @param id The ID of the vendor invoice to retrieve
  /// @return The VendorInvoice object if found, null otherwise
  Future<VendorInvoice?> getVendorInvoiceById(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type VendorInvoice? (mock mode)');

        // For test case: return null for invalid ID
        if (id < 0) {
          return null;
        }

        return _mockDataProvider.getMockSingleData<VendorInvoice?>(id: id);
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendor_invoices')
                .select()
                .eq('invoice_id', id)
                .maybeSingle();

        if (response == null) {
          return null;
        }

        return VendorInvoice.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        debugPrint('Error fetching vendor invoice: $e');
        return null;
      }
    });
  }

  /// Retrieves vendor invoices by vendor ID.
  ///
  /// @param vendorId The ID of the vendor
  /// @return A list of VendorInvoice objects for the specified vendor
  Future<List<VendorInvoice>> getVendorInvoicesByVendorId(
    String vendorId,
  ) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<VendorInvoice>');
        final invoices =
            await _mockDataProvider.getMockListData<List<VendorInvoice>>();
        return invoices
            .where((invoice) => invoice.vendorId == vendorId)
            .toList();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!
            .from('vendor_invoices')
            .select()
            .eq('vendor_id', vendorId);
        return (response as List)
            .map((json) => VendorInvoice.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get vendor invoices by vendor ID: $e');
      }
    });
  }

  /// Creates a new vendor invoice.
  ///
  /// @param vendorInvoice The vendor invoice to create
  /// @return The created VendorInvoice object
  Future<VendorInvoice> createVendorInvoice(VendorInvoice vendorInvoice) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type VendorInvoice');

        // Generate a new ID for the vendor invoice
        final invoices =
            await _mockDataProvider.getMockListData<List<VendorInvoice>>();
        final newId =
            invoices.isEmpty
                ? 1
                : invoices
                        .map((i) => i.invoiceId)
                        .reduce((a, b) => a > b ? a : b) +
                    1;
        final newInvoice = vendorInvoice.copyWith(invoiceId: newId);

        // Add the vendor invoice to the mock data
        final updatedInvoices = List<VendorInvoice>.from(invoices);
        updatedInvoices.add(newInvoice);
        _mockDataProvider.setMockData<List<VendorInvoice>>(updatedInvoices);

        // Also update the single entity mock data
        _mockDataProvider.setMockData<VendorInvoice>(newInvoice);

        return newInvoice;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendor_invoices')
                .insert(convertCamelToSnake(vendorInvoice.toJson()))
                .select()
                .single();
        return VendorInvoice.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to create vendor invoice: $e');
      }
    });
  }

  /// Updates an existing vendor invoice.
  ///
  /// @param vendorInvoice The vendor invoice to update
  /// @return The updated VendorInvoice object
  Future<VendorInvoice> updateVendorInvoice(VendorInvoice vendorInvoice) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type VendorInvoice');

        // Update the mock data
        final invoices =
            await _mockDataProvider.getMockListData<List<VendorInvoice>>();
        final index = invoices.indexWhere(
          (i) => i.invoiceId == vendorInvoice.invoiceId,
        );

        if (index >= 0) {
          // Update the mock data
          final updatedInvoices = List<VendorInvoice>.from(invoices);
          updatedInvoices[index] = vendorInvoice;
          _mockDataProvider.setMockData<List<VendorInvoice>>(updatedInvoices);

          // Also update the single entity mock data
          _mockDataProvider.setMockData<VendorInvoice>(vendorInvoice);
        } else {
          throw Exception('Vendor invoice not found');
        }

        return vendorInvoice;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendor_invoices')
                .update(convertCamelToSnake(vendorInvoice.toJson()))
                .eq('invoice_id', vendorInvoice.invoiceId)
                .select()
                .single();
        return VendorInvoice.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to update vendor invoice: $e');
      }
    });
  }

  /// Deletes a vendor invoice.
  ///
  /// @param id The ID of the vendor invoice to delete
  Future<void> deleteVendorInvoice(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type void');

        // Verify vendor invoice exists before deletion
        final invoiceBeforeDelete = await getVendorInvoiceById(id);
        if (invoiceBeforeDelete == null) {
          throw Exception('Vendor invoice not found');
        }

        // Remove the vendor invoice from the mock data
        final invoices =
            await _mockDataProvider.getMockListData<List<VendorInvoice>>();
        final updatedInvoices =
            invoices.where((invoice) => invoice.invoiceId != id).toList();
        _mockDataProvider.setMockData<List<VendorInvoice>>(updatedInvoices);

        return;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        await client!.from('vendor_invoices').delete().eq('invoice_id', id);
      } catch (e) {
        throw Exception('Failed to delete vendor invoice: $e');
      }
    });
  }
}
