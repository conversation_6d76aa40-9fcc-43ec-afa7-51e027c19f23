import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:we_like_money/config/supabase_config.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/currency.dart';
import 'package:we_like_money/models/customer.dart';
import 'package:we_like_money/models/exchange_rate.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/invoice.dart';
import 'package:we_like_money/models/payment_in.dart';
import 'package:we_like_money/models/payment_out.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/supabase/account_service.dart';
import 'package:we_like_money/services/supabase/company_service.dart';
import 'package:we_like_money/services/supabase/general_ledger_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';
import 'package:we_like_money/services/supabase/project_service.dart';
import 'package:we_like_money/services/supabase/vendor_invoice_service.dart';
import 'package:we_like_money/services/supabase/vendor_service.dart';
import 'package:we_like_money/utils/error_handler.dart';

/// Implementation of the DatabaseService interface using Supabase.
///
/// This service:
/// - Connects to Supabase for database operations
/// - Implements CRUD operations for all entity types
/// - Handles errors consistently using ErrorHandler
/// - Transforms Supabase responses into domain models
@Injectable(as: DatabaseService)
class SupabaseDatabaseFacade implements DatabaseService {
  final SupabaseClientProvider _clientProvider;
  final ErrorHandler _errorHandler;
  final MockDataProvider _mockDataProvider;

  // Services
  late final AccountService _accountService;
  late final CompanyService _companyService;
  late final ProjectService _projectService;
  late final VendorService _vendorService;
  late final VendorInvoiceService _vendorInvoiceService;
  late final GeneralLedgerService _generalLedgerService;

  bool _useMockData;

  /// Creates a new instance of SupabaseDatabaseFacade.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param useMockData Whether to use mock data instead of real database operations
  SupabaseDatabaseFacade(
    this._clientProvider,
    this._errorHandler, {
    bool useMockData = false,
  }) : _useMockData = useMockData,
       _mockDataProvider = MockDataProvider() {
    _initializeServices();
    debugPrint(
      'Database service mock mode ${_useMockData ? 'enabled' : 'disabled'}',
    );
  }

  /// Initializes all services.
  void _initializeServices() {
    _accountService = AccountService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );

    _companyService = CompanyService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );

    _projectService = ProjectService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );

    _vendorService = VendorService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );

    _vendorInvoiceService = VendorInvoiceService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );

    _generalLedgerService = GeneralLedgerService(
      _clientProvider,
      _errorHandler,
      _mockDataProvider,
      useMockData: _useMockData,
    );
  }

  @override
  void setMockMode(bool useMockData) {
    _useMockData = useMockData;

    // Update mock mode for all services
    _accountService.useMockData = useMockData;
    _companyService.useMockData = useMockData;
    _projectService.useMockData = useMockData;
    _vendorService.useMockData = useMockData;
    _vendorInvoiceService.useMockData = useMockData;
    _generalLedgerService.useMockData = useMockData;

    debugPrint(
      'Database service mock mode ${_useMockData ? 'enabled' : 'disabled'}',
    );
  }

  // Company operations
  @override
  Future<List<Company>> getCompanies() {
    return _companyService.getCompanies();
  }

  @override
  Future<Company?> getCompanyById(int id) {
    return _companyService.getCompanyById(id);
  }

  @override
  Future<Company> createCompany(Company company) {
    return _companyService.createCompany(company);
  }

  @override
  Future<Company> updateCompany(Company company) {
    return _companyService.updateCompany(company);
  }

  @override
  Future<void> deleteCompany(int id) {
    return _companyService.deleteCompany(id);
  }

  // Project operations
  @override
  Future<List<Project>> getProjects([int? companyId]) {
    return _projectService.getProjects(companyId);
  }

  @override
  Future<Project?> getProjectById(int id) {
    return _projectService.getProjectById(id);
  }

  @override
  Future<Project> createProject(Project project) {
    return _projectService.createProject(project);
  }

  @override
  Future<Project> updateProject(Project project) {
    return _projectService.updateProject(project);
  }

  @override
  Future<void> deleteProject(int id) {
    return _projectService.deleteProject(id);
  }

  // Account operations
  @override
  Future<List<Account>> getAccounts([int? companyId]) {
    return _accountService.getAccounts(companyId);
  }

  @override
  Future<Account?> getAccountByNumber(String accountNumber) {
    return _accountService.getAccountByNumber(accountNumber);
  }

  @override
  Future<Account> createAccount(Account account) {
    return _accountService.createAccount(account);
  }

  @override
  Future<Account> updateAccount(Account account) {
    return _accountService.updateAccount(account);
  }

  @override
  Future<void> deleteAccount(String accountNumber) {
    return _accountService.deleteAccount(accountNumber);
  }

  // Vendor operations
  @override
  Future<List<Vendor>> getVendors() {
    return _vendorService.getVendors();
  }

  @override
  Future<Vendor?> getVendorById(String id) {
    return _vendorService.getVendorById(id);
  }

  @override
  Future<Vendor> createVendor(Vendor vendor) {
    return _vendorService.createVendor(vendor);
  }

  @override
  Future<Vendor> updateVendor(Vendor vendor) {
    return _vendorService.updateVendor(vendor);
  }

  @override
  Future<void> deleteVendor(String id) {
    return _vendorService.deleteVendor(id);
  }

  @override
  Future<List<Vendor>> searchVendors(String query) {
    return _vendorService.searchVendors(query);
  }

  // Vendor invoice operations
  @override
  Future<List<VendorInvoice>> getVendorInvoices() {
    return _vendorInvoiceService.getVendorInvoices();
  }

  @override
  Future<VendorInvoice?> getVendorInvoiceById(int id) {
    return _vendorInvoiceService.getVendorInvoiceById(id);
  }

  @override
  Future<VendorInvoice> createVendorInvoice(VendorInvoice vendorInvoice) {
    return _vendorInvoiceService.createVendorInvoice(vendorInvoice);
  }

  @override
  Future<VendorInvoice> updateVendorInvoice(VendorInvoice vendorInvoice) {
    return _vendorInvoiceService.updateVendorInvoice(vendorInvoice);
  }

  @override
  Future<void> deleteVendorInvoice(int id) {
    return _vendorInvoiceService.deleteVendorInvoice(id);
  }

  @override
  Future<List<VendorInvoice>> getVendorInvoicesByVendorId(String vendorId) {
    return _vendorInvoiceService.getVendorInvoicesByVendorId(vendorId);
  }

  // General ledger operations
  @override
  Future<List<GeneralLedger>> getGeneralLedgerEntries() {
    return _generalLedgerService.getGeneralLedgerEntries();
  }

  @override
  Future<List<GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String accountNumber,
  ) {
    return _generalLedgerService.getGeneralLedgerEntriesByAccount(
      accountNumber,
    );
  }

  @override
  Future<List<GeneralLedger>> getLedgerEntriesByTransactionId(
    String transactionId,
  ) {
    return _generalLedgerService.getLedgerEntriesByTransactionId(transactionId);
  }

  @override
  Future<GeneralLedger?> getGeneralLedgerEntryById(int id) {
    return _generalLedgerService.getGeneralLedgerEntryById(id);
  }

  @override
  Future<GeneralLedger> createGeneralLedgerEntry(GeneralLedger entry) {
    return _generalLedgerService.createGeneralLedgerEntry(entry);
  }

  @override
  Future<GeneralLedger> updateGeneralLedgerEntry(GeneralLedger entry) {
    return _generalLedgerService.updateGeneralLedgerEntry(entry);
  }

  @override
  Future<void> deleteGeneralLedgerEntry(int id) {
    return _generalLedgerService.deleteGeneralLedgerEntry(id);
  }

  // Staff member operations
  @override
  Future<List<StaffMember>> getStaffMembers() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<StaffMember>>();
    }
    throw UnimplementedError('getStaffMembers not implemented');
  }

  @override
  Future<StaffMember?> getStaffMemberById(int id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<StaffMember?>(id: id);
    }
    throw UnimplementedError('getStaffMemberById not implemented');
  }

  @override
  Future<StaffMember> createStaffMember(StaffMember staffMember) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(staffMember);
    }
    throw UnimplementedError('createStaffMember not implemented');
  }

  @override
  Future<StaffMember> updateStaffMember(StaffMember staffMember) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(staffMember);
    }
    throw UnimplementedError('updateStaffMember not implemented');
  }

  @override
  Future<void> deleteStaffMember(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteStaffMember not implemented');
  }

  // Currency operations
  @override
  Future<List<Currency>> getCurrencies() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<Currency>>();
    }
    throw UnimplementedError('getCurrencies not implemented');
  }

  @override
  Future<Currency?> getCurrencyByCode(String code) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<Currency?>(id: code);
    }
    throw UnimplementedError('getCurrencyByCode not implemented');
  }

  @override
  Future<Currency> createCurrency(Currency currency) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(currency);
    }
    throw UnimplementedError('createCurrency not implemented');
  }

  @override
  Future<Currency> updateCurrency(Currency currency) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(currency);
    }
    throw UnimplementedError('updateCurrency not implemented');
  }

  @override
  Future<void> deleteCurrency(String code) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteCurrency not implemented');
  }

  // Exchange rate operations
  @override
  Future<List<ExchangeRate>> getExchangeRates() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<ExchangeRate>>();
    }
    throw UnimplementedError('getExchangeRates not implemented');
  }

  @override
  Future<ExchangeRate?> getExchangeRateById(int id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<ExchangeRate?>(id: id);
    }
    throw UnimplementedError('getExchangeRateById not implemented');
  }

  @override
  Future<ExchangeRate> createExchangeRate(ExchangeRate exchangeRate) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(exchangeRate);
    }
    throw UnimplementedError('createExchangeRate not implemented');
  }

  @override
  Future<ExchangeRate> updateExchangeRate(ExchangeRate exchangeRate) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(exchangeRate);
    }
    throw UnimplementedError('updateExchangeRate not implemented');
  }

  @override
  Future<void> deleteExchangeRate(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteExchangeRate not implemented');
  }

  // Customer operations
  @override
  Future<List<Customer>> getCustomers() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<Customer>>();
    }
    throw UnimplementedError('getCustomers not implemented');
  }

  @override
  Future<Customer?> getCustomerById(String id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<Customer?>(id: id);
    }
    throw UnimplementedError('getCustomerById not implemented');
  }

  @override
  Future<Customer> createCustomer(Customer customer) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(customer);
    }
    throw UnimplementedError('createCustomer not implemented');
  }

  @override
  Future<Customer> updateCustomer(Customer customer) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(customer);
    }
    throw UnimplementedError('updateCustomer not implemented');
  }

  @override
  Future<void> deleteCustomer(String id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteCustomer not implemented');
  }

  // Invoice operations
  @override
  Future<List<Invoice>> getInvoices() {
    if (_useMockData && !kReleaseMode) {
      return Future.value([]);
    }
    throw UnimplementedError('getInvoices not implemented');
  }

  @override
  Future<Invoice?> getInvoiceById(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(null);
    }
    throw UnimplementedError('getInvoiceById not implemented');
  }

  @override
  Future<Invoice> createInvoice(Invoice invoice) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(invoice);
    }
    throw UnimplementedError('createInvoice not implemented');
  }

  @override
  Future<Invoice> updateInvoice(Invoice invoice) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(invoice);
    }
    throw UnimplementedError('updateInvoice not implemented');
  }

  @override
  Future<void> deleteInvoice(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteInvoice not implemented');
  }

  // Vendor invoice line item operations
  @override
  Future<List<VendorInvoiceLineItem>> getVendorInvoiceLineItems(int invoiceId) {
    if (_useMockData && !kReleaseMode) {
      final items =
          _mockDataProvider.getMockListData<List<VendorInvoiceLineItem>>();
      return items.then(
        (list) => list.where((item) => item.invoiceId == invoiceId).toList(),
      );
    }
    throw UnimplementedError('getVendorInvoiceLineItems not implemented');
  }

  @override
  Future<VendorInvoiceLineItem?> getVendorInvoiceLineItemById(int id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<VendorInvoiceLineItem?>(
        id: id,
      );
    }
    throw UnimplementedError('getVendorInvoiceLineItemById not implemented');
  }

  @override
  Future<List<VendorInvoiceLineItem>> getVendorInvoiceLineItemsByAccount(
    String accountNumber,
  ) {
    if (_useMockData && !kReleaseMode) {
      final items =
          _mockDataProvider.getMockListData<List<VendorInvoiceLineItem>>();
      return items.then(
        (list) =>
            list.where((item) => item.accountNumber == accountNumber).toList(),
      );
    }
    throw UnimplementedError(
      'getVendorInvoiceLineItemsByAccount not implemented',
    );
  }

  @override
  Future<VendorInvoiceLineItem> createVendorInvoiceLineItem(
    VendorInvoiceLineItem lineItem,
  ) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(lineItem);
    }
    throw UnimplementedError('createVendorInvoiceLineItem not implemented');
  }

  @override
  Future<VendorInvoiceLineItem> updateVendorInvoiceLineItem(
    VendorInvoiceLineItem lineItem,
  ) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(lineItem);
    }
    throw UnimplementedError('updateVendorInvoiceLineItem not implemented');
  }

  @override
  Future<void> deleteVendorInvoiceLineItem(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteVendorInvoiceLineItem not implemented');
  }

  // Expense operations
  @override
  Future<List<Expense>> getExpenses() {
    if (_useMockData && !kReleaseMode) {
      return Future.value([]);
    }
    throw UnimplementedError('getExpenses not implemented');
  }

  @override
  Future<Expense?> getExpenseById(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(null);
    }
    throw UnimplementedError('getExpenseById not implemented');
  }

  @override
  Future<Expense> createExpense(Expense expense) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(expense);
    }
    throw UnimplementedError('createExpense not implemented');
  }

  @override
  Future<Expense> updateExpense(Expense expense) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(expense);
    }
    throw UnimplementedError('updateExpense not implemented');
  }

  @override
  Future<void> deleteExpense(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deleteExpense not implemented');
  }

  // Payment in operations
  @override
  Future<List<PaymentIn>> getPaymentsIn() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<PaymentIn>>();
    }
    throw UnimplementedError('getPaymentsIn not implemented');
  }

  @override
  Future<PaymentIn?> getPaymentInById(int id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<PaymentIn?>(id: id);
    }
    throw UnimplementedError('getPaymentInById not implemented');
  }

  @override
  Future<PaymentIn> createPaymentIn(PaymentIn paymentIn) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(paymentIn);
    }
    throw UnimplementedError('createPaymentIn not implemented');
  }

  @override
  Future<PaymentIn> updatePaymentIn(PaymentIn paymentIn) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(paymentIn);
    }
    throw UnimplementedError('updatePaymentIn not implemented');
  }

  @override
  Future<void> deletePaymentIn(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deletePaymentIn not implemented');
  }

  // Payment out operations
  @override
  Future<List<PaymentOut>> getPaymentsOut() {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockListData<List<PaymentOut>>();
    }
    throw UnimplementedError('getPaymentsOut not implemented');
  }

  @override
  Future<PaymentOut?> getPaymentOutById(int id) {
    if (_useMockData && !kReleaseMode) {
      return _mockDataProvider.getMockSingleData<PaymentOut?>(id: id);
    }
    throw UnimplementedError('getPaymentOutById not implemented');
  }

  @override
  Future<PaymentOut> createPaymentOut(PaymentOut paymentOut) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(paymentOut);
    }
    throw UnimplementedError('createPaymentOut not implemented');
  }

  @override
  Future<PaymentOut> updatePaymentOut(PaymentOut paymentOut) {
    if (_useMockData && !kReleaseMode) {
      return Future.value(paymentOut);
    }
    throw UnimplementedError('updatePaymentOut not implemented');
  }

  @override
  Future<void> deletePaymentOut(int id) {
    if (_useMockData && !kReleaseMode) {
      return Future.value();
    }
    throw UnimplementedError('deletePaymentOut not implemented');
  }

  @override
  dynamic noSuchMethod(Invocation invocation) {
    if (invocation.isMethod) {
      final methodName = invocation.memberName.toString();
      debugPrint('Method $methodName not implemented');
      if (methodName.contains('get')) {
        if (methodName.contains('List')) {
          return Future.value([]);
        } else {
          return Future.value(null);
        }
      } else if (methodName.contains('create') ||
          methodName.contains('update')) {
        return Future.value(invocation.positionalArguments.first);
      } else if (methodName.contains('delete')) {
        return Future.value(null);
      }
    }
    return super.noSuchMethod(invocation);
  }
}
