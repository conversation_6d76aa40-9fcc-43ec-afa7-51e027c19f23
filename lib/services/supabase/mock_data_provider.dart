import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/models.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';

/// Provider for mock data used in tests and development.
///
/// This class:
/// - Initializes mock data for all entity types
/// - Provides access to mock data
/// - Allows updating mock data
class MockDataProvider {
  final Map<Type, dynamic> _mockData = {};

  /// Creates a new instance of MockDataProvider.
  MockDataProvider() {
    _initializeMockData();
  }

  /// Gets mock data for the specified type.
  ///
  /// @param T The type of data to get
  /// @return The mock data for the specified type
  T? getMockData<T>() {
    return _mockData[T] as T?;
  }

  /// Sets mock data for the specified type.
  ///
  /// @param T The type of data to set
  /// @param data The mock data to set
  void setMockData<T>(T data) {
    _mockData[T] = data;
  }

  /// Initializes mock data for all entity types.
  void _initializeMockData() {
    debugPrint('Initializing mock data');

    // Initialize list mock data first
    _mockData[List<Company>] = [
      const Company(
        companyId: 1,
        companyName: 'Test Company',
        address: '789 Company St',
        organizationNumber: '123-45-6789',
        city: 'Test City',
        country: 'Test Country',
      ),
    ];
    debugPrint('Initialized Company list');

    _mockData[List<VendorInvoiceLineItem>] = [
      const VendorInvoiceLineItem(
        lineItemId: 1,
        invoiceId: 1,
        accountNumber: '5000',
        description: 'Test line item',
        amount: 100.0,
        taxAmount: 10.0,
        companyId: 1,
      ),
      const VendorInvoiceLineItem(
        lineItemId: 2,
        invoiceId: 1,
        accountNumber: '5000',
        description: 'Another line item',
        amount: 200.0,
        taxAmount: 20.0,
        companyId: 1,
      ),
    ];
    debugPrint('Initialized VendorInvoiceLineItem list');

    _mockData[List<Project>] = [
      const Project(
        projectId: 1,
        projectCode: 'PRJ001',
        projectName: 'Website Redesign',
        description: 'Redesign company website',
        companyId: 1,
      ),
      const Project(
        projectId: 2,
        projectCode: 'PRJ002',
        projectName: 'Mobile App',
        description: 'Develop mobile app',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized Project list');

    _mockData[List<Vendor>] = [
      const Vendor(
        vendorId: '1',
        vendorName: 'Office Depot',
        organizationNumber: '111-22-3333',
        companyId: 1,
      ),
      const Vendor(
        vendorId: '2',
        vendorName: 'Tech Solutions',
        organizationNumber: '444-55-6666',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized Vendor list');

    _mockData[List<StaffMember>] = [
      const StaffMember(
        staffId: 1,
        staffName: 'John Doe',
        email: '<EMAIL>',
        companyId: 1,
      ),
      const StaffMember(
        staffId: 2,
        staffName: 'Jane Smith',
        email: '<EMAIL>',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized StaffMember list');

    _mockData[List<Currency>] = [
      const Currency(currencyCode: 'USD', currencyName: 'US Dollar'),
      const Currency(currencyCode: 'EUR', currencyName: 'Euro'),
    ];
    debugPrint('Initialized Currency list');

    _mockData[List<GeneralLedger>] = [
      GeneralLedger(
        ledgerId: 1,
        transactionId: 'TX001',
        transactionDate: DateTime(2023, 1, 1),
        accountNumber: '5000',
        description: 'Office supplies purchase',
        debit: 750.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 1,
        staffId: 1,
        taxAmount: 0.0,
        companyId: 1,
      ),
    ];
    debugPrint('Initialized GeneralLedger list');

    _mockData[List<VendorInvoice>] = [
      VendorInvoice(
        invoiceId: 1,
        vendorId: '1',
        invoiceNumber: 'INV-2023-001',
        invoiceDate: DateTime(2023, 5, 15),
        dueDate: DateTime(2023, 6, 15),
        amount: 1000.0,
        currencyCode: 'USD',
        expenseAccountNumber: '5000',
        taxAmount: 100.0,
        companyId: 1,
      ),
      VendorInvoice(
        invoiceId: 2,
        vendorId: '1',
        invoiceNumber: 'INV-2023-002',
        invoiceDate: DateTime(2023, 5, 15),
        dueDate: DateTime(2023, 6, 15),
        amount: 500.0,
        currencyCode: 'USD',
        expenseAccountNumber: '5000',
        taxAmount: 0.0,
        companyId: 1,
      ),
    ];
    debugPrint('Initialized VendorInvoice list');

    _mockData[List<Account>] = [
      const Account(
        accountNumber: '1000',
        accountName: 'Cash',
        accountType: 'Asset',
        companyId: 1,
      ),
      const Account(
        accountNumber: '2000',
        accountName: 'Accounts Payable',
        accountType: 'Liability',
        companyId: 1,
      ),
      const Account(
        accountNumber: '5000',
        accountName: 'Office Supplies',
        accountType: 'Expense',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized Account list');

    _mockData[List<ExchangeRate>] = [
      ExchangeRate(
        rateId: 1,
        fromCurrency: 'USD',
        toCurrency: 'EUR',
        rate: 0.85,
        effectiveDate: DateTime(2023, 1, 1),
        companyId: 1,
      ),
    ];
    debugPrint('Initialized ExchangeRate list');

    _mockData[List<PaymentIn>] = [
      PaymentIn(
        paymentInId: 1,
        invoiceId: 1,
        paymentDate: DateTime(2023, 1, 15),
        amount: 1000.0,
        currencyCode: 'USD',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized PaymentIn list');

    _mockData[List<PaymentOut>] = [
      PaymentOut(
        paymentOutId: 1,
        expenseId: 1,
        paymentDate: DateTime(2023, 1, 15),
        amount: 1000.0,
        currencyCode: 'USD',
        companyId: 1,
      ),
    ];
    debugPrint('Initialized PaymentOut list');

    _mockData[List<Customer>] = [
      const Customer(customerId: '1', customerName: 'ABC Corp', companyId: 1),
    ];
    debugPrint('Initialized Customer list');

    _mockData[List<PaymentMethod>] = [
      PaymentMethod.cash,
      PaymentMethod.creditCard,
      PaymentMethod.bankTransfer,
      PaymentMethod.check,
      PaymentMethod.other,
    ];
    debugPrint('Initialized PaymentMethod list');

    // Initialize single entity mock data
    debugPrint('Initializing single entity mock data');
    _mockData[Company] = _mockData[List<Company>]![0];
    _mockData[Project] = _mockData[List<Project>]![0];
    _mockData[Vendor] = _mockData[List<Vendor>]![0];
    _mockData[StaffMember] = _mockData[List<StaffMember>]![0];
    _mockData[Currency] = _mockData[List<Currency>]![0];
    _mockData[GeneralLedger] = _mockData[List<GeneralLedger>]![0];
    _mockData[VendorInvoice] = _mockData[List<VendorInvoice>]![0];
    _mockData[Account] = _mockData[List<Account>]![0];
    _mockData[ExchangeRate] = _mockData[List<ExchangeRate>]![0];
    _mockData[PaymentIn] = _mockData[List<PaymentIn>]![0];
    _mockData[PaymentOut] = _mockData[List<PaymentOut>]![0];
    _mockData[Customer] = _mockData[List<Customer>]![0];
    _mockData[VendorInvoiceLineItem] =
        _mockData[List<VendorInvoiceLineItem>]![0];
    debugPrint('Initialized single entity mock data');
  }

  /// Gets mock list data for the specified type.
  ///
  /// @param T The type of list data to get
  /// @return The mock list data for the specified type
  Future<T> getMockListData<T>() async {
    debugPrint('Getting mock list data for type $T');
    final data = _mockData[T];
    if (data != null) {
      debugPrint('Found mock data for type $T');
      return data as T;
    }

    debugPrint('No mock data found for type $T, creating empty list');
    if (T == List<Company>) {
      return <Company>[] as T;
    } else if (T == List<Project>) {
      return <Project>[] as T;
    } else if (T == List<Vendor>) {
      return <Vendor>[] as T;
    } else if (T == List<StaffMember>) {
      return <StaffMember>[] as T;
    } else if (T == List<Currency>) {
      return <Currency>[] as T;
    } else if (T == List<GeneralLedger>) {
      return <GeneralLedger>[] as T;
    } else if (T == List<VendorInvoice>) {
      return <VendorInvoice>[] as T;
    } else if (T == List<Account>) {
      return <Account>[] as T;
    } else if (T == List<ExchangeRate>) {
      return <ExchangeRate>[] as T;
    } else if (T == List<PaymentIn>) {
      return <PaymentIn>[] as T;
    } else if (T == List<PaymentOut>) {
      return <PaymentOut>[] as T;
    } else if (T == List<Customer>) {
      return <Customer>[] as T;
    } else if (T == List<VendorInvoiceLineItem>) {
      return <VendorInvoiceLineItem>[] as T;
    }

    throw Exception('Unsupported type: $T');
  }

  /// Gets mock single data for the specified type.
  ///
  /// @param T The type of single data to get
  /// @param id The ID of the entity to get (optional)
  /// @return The mock single data for the specified type
  Future<T?> getMockSingleData<T>({dynamic id}) async {
    debugPrint('Getting mock single data for type $T');
    debugPrint('Input data: $id');

    // Handle nullable types
    final typeStr = T.toString();

    if (typeStr == 'Company?') {
      debugPrint('Getting Company by ID: $id');
      if (id == null) return null;
      final companies = await getMockListData<List<Company>>();
      try {
        return companies.firstWhere((c) => c.companyId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'Project?') {
      debugPrint('Getting Project by ID: $id');
      if (id == null) return null;
      final projects = await getMockListData<List<Project>>();
      try {
        return projects.firstWhere((p) => p.projectId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'Vendor?') {
      debugPrint('Using default mock data for type $T');
      return _mockData[Vendor] as T;
    } else if (typeStr == 'StaffMember?') {
      if (id == null) return null;
      final staffMembers = await getMockListData<List<StaffMember>>();
      try {
        return staffMembers.firstWhere((s) => s.staffId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'Currency?') {
      if (id == null) return null;
      final currencies = await getMockListData<List<Currency>>();
      try {
        return currencies.firstWhere((c) => c.currencyCode == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'GeneralLedger?') {
      debugPrint('Getting GeneralLedger by ID: $id');
      if (id == null) return null;
      final entries = await getMockListData<List<GeneralLedger>>();
      try {
        return entries.firstWhere((e) => e.ledgerId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'VendorInvoice?') {
      if (id == null) return null;
      final invoices = await getMockListData<List<VendorInvoice>>();
      try {
        return invoices.firstWhere((i) => i.invoiceId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'Account?') {
      if (id == null) return null;
      final accounts = await getMockListData<List<Account>>();
      try {
        return accounts.firstWhere((a) => a.accountNumber == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'ExchangeRate?') {
      if (id == null) return null;
      final rates = await getMockListData<List<ExchangeRate>>();
      try {
        return rates.firstWhere((r) => r.rateId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'PaymentIn?') {
      if (id == null) return null;
      final payments = await getMockListData<List<PaymentIn>>();
      try {
        return payments.firstWhere((p) => p.paymentInId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'PaymentOut?') {
      if (id == null) return null;
      final payments = await getMockListData<List<PaymentOut>>();
      try {
        return payments.firstWhere((p) => p.paymentOutId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'Customer?') {
      if (id == null) return null;
      final customers = await getMockListData<List<Customer>>();
      try {
        return customers.firstWhere((c) => c.customerId == id) as T;
      } catch (e) {
        return null;
      }
    } else if (typeStr == 'VendorInvoiceLineItem?') {
      debugPrint('Using default mock data for type $T');
      return _mockData[VendorInvoiceLineItem] as T;
    }

    debugPrint('Handling nullable type $T');
    debugPrint('Input data: $id');
    return null;
  }
}
