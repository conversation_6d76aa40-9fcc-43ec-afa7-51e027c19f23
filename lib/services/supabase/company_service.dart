import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/services/supabase/base_supabase_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';

/// Service for company-related operations using Supabase.
class CompanyService extends BaseSupabaseService {
  final MockDataProvider _mockDataProvider;

  /// Creates a new instance of CompanyService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param mockDataProvider The provider for mock data
  /// @param useMockData Whether to use mock data instead of real database operations
  CompanyService(
    super.clientProvider,
    super.errorHandler,
    this._mockDataProvider, {
    super.useMockData,
  });

  /// Retrieves all companies.
  ///
  /// @return A list of Company objects
  Future<List<Company>> getCompanies() async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<Company>');
        return _mockDataProvider.getMockListData<List<Company>>();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!.from('companies').select();
        return (response as List)
            .map((json) => Company.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get companies: $e');
      }
    });
  }

  /// Retrieves a company by its ID.
  ///
  /// @param id The ID of the company to retrieve
  /// @return The Company object if found, null otherwise
  Future<Company?> getCompanyById(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Company?');
        return _mockDataProvider.getMockSingleData<Company?>(id: id);
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('companies')
                .select()
                .eq('company_id', id)
                .maybeSingle();

        if (response == null) {
          return null;
        }

        return Company.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        debugPrint('Error fetching company: $e');
        return null;
      }
    });
  }

  /// Creates a new company.
  ///
  /// @param company The company to create
  /// @return The created Company object
  Future<Company> createCompany(Company company) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Company');

        // Generate a new ID for the company
        final companies =
            await _mockDataProvider.getMockListData<List<Company>>();
        final newId =
            companies.isEmpty
                ? 1
                : companies
                        .map((c) => c.companyId)
                        .reduce((a, b) => a > b ? a : b) +
                    1;
        final newCompany = company.copyWith(companyId: newId);

        // Add the company to the mock data
        final updatedCompanies = List<Company>.from(companies);
        updatedCompanies.add(newCompany);
        _mockDataProvider.setMockData<List<Company>>(updatedCompanies);

        // Also update the single entity mock data
        _mockDataProvider.setMockData<Company>(newCompany);

        return newCompany;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('companies')
                .insert(convertCamelToSnake(company.toJson()))
                .select()
                .single();
        return Company.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to create company: $e');
      }
    });
  }

  /// Updates an existing company.
  ///
  /// @param company The company to update
  /// @return The updated Company object
  Future<Company> updateCompany(Company company) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Company');

        // Update the mock data
        final companies =
            await _mockDataProvider.getMockListData<List<Company>>();
        final index = companies.indexWhere(
          (c) => c.companyId == company.companyId,
        );

        if (index >= 0) {
          // Update the mock data
          final updatedCompanies = List<Company>.from(companies);
          updatedCompanies[index] = company;
          _mockDataProvider.setMockData<List<Company>>(updatedCompanies);

          // Also update the single entity mock data
          _mockDataProvider.setMockData<Company>(company);
        } else {
          throw Exception('Company not found');
        }

        return company;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('companies')
                .update(convertCamelToSnake(company.toJson()))
                .eq('company_id', company.companyId)
                .select()
                .single();
        return Company.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to update company: $e');
      }
    });
  }

  /// Deletes a company.
  ///
  /// @param id The ID of the company to delete
  Future<void> deleteCompany(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type void');

        // Verify company exists before deletion
        final companyBeforeDelete = await getCompanyById(id);
        if (companyBeforeDelete == null) {
          throw Exception('Company not found');
        }

        // Remove the company from the mock data
        final companies =
            await _mockDataProvider.getMockListData<List<Company>>();
        final updatedCompanies =
            companies.where((company) => company.companyId != id).toList();
        _mockDataProvider.setMockData<List<Company>>(updatedCompanies);

        return;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        await client!.from('companies').delete().eq('company_id', id);
      } catch (e) {
        throw Exception('Failed to delete company: $e');
      }
    });
  }
}
