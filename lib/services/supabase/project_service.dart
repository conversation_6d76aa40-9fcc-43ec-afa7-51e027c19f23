import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/services/supabase/base_supabase_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';

/// Service for project-related operations using Supabase.
class ProjectService extends BaseSupabaseService {
  final MockDataProvider _mockDataProvider;

  /// Creates a new instance of ProjectService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param mockDataProvider The provider for mock data
  /// @param useMockData Whether to use mock data instead of real database operations
  ProjectService(
    super.clientProvider,
    super.errorHandler,
    this._mockDataProvider, {
    super.useMockData,
  });

  /// Retrieves all projects.
  ///
  /// @param companyId Optional company ID to filter projects by
  /// @return A list of Project objects
  Future<List<Project>> getProjects([int? companyId]) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<Project>');
        final projects =
            await _mockDataProvider.getMockListData<List<Project>>();

        if (companyId != null) {
          return projects
              .where((project) => project.companyId == companyId)
              .toList();
        }

        return projects;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final query = client!.from('projects').select();

        if (companyId != null) {
          query.eq('company_id', companyId);
        }

        final response = await query;
        return (response as List)
            .map((json) => Project.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get projects: $e');
      }
    });
  }

  /// Retrieves a project by its ID.
  ///
  /// @param id The ID of the project to retrieve
  /// @return The Project object if found, null otherwise
  Future<Project?> getProjectById(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Project?');
        return _mockDataProvider.getMockSingleData<Project?>(id: id);
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('projects')
                .select()
                .eq('project_id', id)
                .maybeSingle();

        if (response == null) {
          return null;
        }

        return Project.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        debugPrint('Error fetching project: $e');
        return null;
      }
    });
  }

  /// Creates a new project.
  ///
  /// @param project The project to create
  /// @return The created Project object
  Future<Project> createProject(Project project) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Project');

        // Generate a new ID for the project
        final projects =
            await _mockDataProvider.getMockListData<List<Project>>();
        final newId =
            projects.isEmpty
                ? 1
                : projects
                        .map((p) => p.projectId)
                        .reduce((a, b) => a > b ? a : b) +
                    1;
        final newProject = project.copyWith(projectId: newId);

        // Add the project to the mock data
        final updatedProjects = List<Project>.from(projects);
        updatedProjects.add(newProject);
        _mockDataProvider.setMockData<List<Project>>(updatedProjects);

        // Also update the single entity mock data
        _mockDataProvider.setMockData<Project>(newProject);

        return newProject;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('projects')
                .insert(convertCamelToSnake(project.toJson()))
                .select()
                .single();
        return Project.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to create project: $e');
      }
    });
  }

  /// Updates an existing project.
  ///
  /// @param project The project to update
  /// @return The updated Project object
  Future<Project> updateProject(Project project) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Project');

        // Update the mock data
        final projects =
            await _mockDataProvider.getMockListData<List<Project>>();
        final index = projects.indexWhere(
          (p) => p.projectId == project.projectId,
        );

        if (index >= 0) {
          // Update the mock data
          final updatedProjects = List<Project>.from(projects);
          updatedProjects[index] = project;
          _mockDataProvider.setMockData<List<Project>>(updatedProjects);

          // Also update the single entity mock data
          _mockDataProvider.setMockData<Project>(project);
        } else {
          throw Exception('Project not found');
        }

        return project;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('projects')
                .update(convertCamelToSnake(project.toJson()))
                .eq('project_id', project.projectId)
                .select()
                .single();
        return Project.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to update project: $e');
      }
    });
  }

  /// Deletes a project.
  ///
  /// @param id The ID of the project to delete
  Future<void> deleteProject(int id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type void');

        // Verify project exists before deletion
        final projectBeforeDelete = await getProjectById(id);
        if (projectBeforeDelete == null) {
          throw Exception('Project not found');
        }

        // Remove the project from the mock data
        final projects =
            await _mockDataProvider.getMockListData<List<Project>>();
        final updatedProjects =
            projects.where((project) => project.projectId != id).toList();
        _mockDataProvider.setMockData<List<Project>>(updatedProjects);

        return;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        await client!.from('projects').delete().eq('project_id', id);
      } catch (e) {
        throw Exception('Failed to delete project: $e');
      }
    });
  }
}
