import 'package:flutter/foundation.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/services/supabase/base_supabase_service.dart';
import 'package:we_like_money/services/supabase/mock_data_provider.dart';

/// Service for vendor-related operations using Supabase.
class VendorService extends BaseSupabaseService {
  final MockDataProvider _mockDataProvider;

  /// Creates a new instance of VendorService.
  ///
  /// @param clientProvider The provider for the Supabase client
  /// @param errorHandler The error handler to use
  /// @param mockDataProvider The provider for mock data
  /// @param useMockData Whether to use mock data instead of real database operations
  VendorService(
    super.clientProvider,
    super.errorHandler,
    this._mockDataProvider, {
    super.useMockData,
  });

  /// Retrieves all vendors.
  ///
  /// @return A list of Vendor objects
  Future<List<Vendor>> getVendors() async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<Vendor>');
        return _mockDataProvider.getMockListData<List<Vendor>>();
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!.from('vendors').select();
        return (response as List)
            .map((json) => Vendor.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to get vendors: $e');
      }
    });
  }

  /// Retrieves a vendor by its ID.
  ///
  /// @param id The ID of the vendor to retrieve
  /// @return The Vendor object if found, null otherwise
  Future<Vendor?> getVendorById(String id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Vendor?');

        // For test case: return null for invalid ID
        if (id == 'invalid_id') {
          return null;
        }

        return _mockDataProvider.getMockSingleData<Vendor?>(id: id);
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendors')
                .select()
                .eq('vendor_id', id)
                .maybeSingle();

        if (response == null) {
          return null;
        }

        return Vendor.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        debugPrint('Error fetching vendor: $e');
        return null;
      }
    });
  }

  /// Searches for vendors by name.
  ///
  /// @param query The search query
  /// @return A list of Vendor objects matching the query
  Future<List<Vendor>> searchVendors(String query) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type List<Vendor> (mock mode)');
        final vendors = await _mockDataProvider.getMockListData<List<Vendor>>();
        debugPrint('Got ${vendors.length} vendors from mock data');

        final lowercaseQuery = query.toLowerCase();
        debugPrint('Search query (lowercase): $lowercaseQuery');

        final results =
            vendors
                .where(
                  (vendor) =>
                      vendor.vendorName.toLowerCase().contains(lowercaseQuery),
                )
                .toList();

        debugPrint('Found ${results.length} matching vendors');
        return results;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response = await client!
            .from('vendors')
            .select()
            .ilike('vendor_name', '%$query%');
        return (response as List)
            .map((json) => Vendor.fromJson(convertSnakeToCamel(json)))
            .toList();
      } catch (e) {
        throw Exception('Failed to search vendors: $e');
      }
    });
  }

  /// Creates a new vendor.
  ///
  /// @param vendor The vendor to create
  /// @return The created Vendor object
  Future<Vendor> createVendor(Vendor vendor) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Vendor');

        // Add the vendor to the mock data
        final vendors = await _mockDataProvider.getMockListData<List<Vendor>>();
        final updatedVendors = List<Vendor>.from(vendors);
        updatedVendors.add(vendor);
        _mockDataProvider.setMockData<List<Vendor>>(updatedVendors);

        // Also update the single entity mock data
        _mockDataProvider.setMockData<Vendor>(vendor);

        return vendor;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendors')
                .insert(convertCamelToSnake(vendor.toJson()))
                .select()
                .single();
        return Vendor.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to create vendor: $e');
      }
    });
  }

  /// Updates an existing vendor.
  ///
  /// @param vendor The vendor to update
  /// @return The updated Vendor object
  Future<Vendor> updateVendor(Vendor vendor) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type Vendor');

        // Update the mock data
        final vendors = await _mockDataProvider.getMockListData<List<Vendor>>();
        final index = vendors.indexWhere((v) => v.vendorId == vendor.vendorId);

        if (index >= 0) {
          // Update the mock data
          final updatedVendors = List<Vendor>.from(vendors);
          updatedVendors[index] = vendor;
          _mockDataProvider.setMockData<List<Vendor>>(updatedVendors);

          // Also update the single entity mock data
          _mockDataProvider.setMockData<Vendor>(vendor);
        } else {
          throw Exception('Vendor not found');
        }

        return vendor;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        final response =
            await client!
                .from('vendors')
                .update(convertCamelToSnake(vendor.toJson()))
                .eq('vendor_id', vendor.vendorId)
                .select()
                .single();
        return Vendor.fromJson(convertSnakeToCamel(response));
      } catch (e) {
        throw Exception('Failed to update vendor: $e');
      }
    });
  }

  /// Deletes a vendor.
  ///
  /// @param id The ID of the vendor to delete
  Future<void> deleteVendor(String id) async {
    return executeDbOperation(() async {
      if (useMockData && !kReleaseMode) {
        debugPrint('Using mock data for type void');

        // Verify vendor exists before deletion
        final vendorBeforeDelete = await getVendorById(id);
        if (vendorBeforeDelete == null) {
          throw Exception('Vendor not found');
        }

        // Remove the vendor from the mock data
        final vendors = await _mockDataProvider.getMockListData<List<Vendor>>();
        final updatedVendors =
            vendors.where((vendor) => vendor.vendorId != id).toList();
        _mockDataProvider.setMockData<List<Vendor>>(updatedVendors);

        return;
      }

      if (client == null) {
        throw Exception('Supabase client is not initialized');
      }

      try {
        await client!.from('vendors').delete().eq('vendor_id', id);
      } catch (e) {
        throw Exception('Failed to delete vendor: $e');
      }
    });
  }
}
