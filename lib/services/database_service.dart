import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/currency.dart';
import 'package:we_like_money/models/customer.dart';
import 'package:we_like_money/models/exchange_rate.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/invoice.dart';
import 'package:we_like_money/models/payment_in.dart';
import 'package:we_like_money/models/payment_out.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/models/vendor_invoice_line_item.dart';

abstract class DatabaseService {
  // Configuration methods
  void setMockMode(bool useMockData);

  // Company operations
  Future<List<Company>> getCompanies();
  Future<Company?> getCompanyById(int id);
  Future<Company> createCompany(Company company);
  Future<Company> updateCompany(Company company);
  Future<void> deleteCompany(int id);

  // Project operations
  Future<List<Project>> getProjects([int? companyId]);
  Future<Project?> getProjectById(int id);
  Future<Project> createProject(Project project);
  Future<Project> updateProject(Project project);
  Future<void> deleteProject(int id);

  // Staff member operations
  Future<List<StaffMember>> getStaffMembers();
  Future<StaffMember?> getStaffMemberById(int id);
  Future<StaffMember> createStaffMember(StaffMember staffMember);
  Future<StaffMember> updateStaffMember(StaffMember staffMember);
  Future<void> deleteStaffMember(int id);

  // Currency operations
  Future<List<Currency>> getCurrencies();
  Future<Currency?> getCurrencyByCode(String code);
  Future<Currency> createCurrency(Currency currency);
  Future<Currency> updateCurrency(Currency currency);
  Future<void> deleteCurrency(String code);

  // Exchange rate operations
  Future<List<ExchangeRate>> getExchangeRates();
  Future<ExchangeRate?> getExchangeRateById(int id);
  Future<ExchangeRate> createExchangeRate(ExchangeRate exchangeRate);
  Future<ExchangeRate> updateExchangeRate(ExchangeRate exchangeRate);
  Future<void> deleteExchangeRate(int id);

  // Account operations
  Future<List<Account>> getAccounts([int? companyId]);
  Future<Account?> getAccountByNumber(String accountNumber);
  Future<Account> createAccount(Account account);
  Future<Account> updateAccount(Account account);
  Future<void> deleteAccount(String accountNumber);

  // General ledger operations
  Future<List<GeneralLedger>> getGeneralLedgerEntries();
  Future<List<GeneralLedger>> getGeneralLedgerEntriesByAccount(
    String accountNumber,
  );
  Future<List<GeneralLedger>> getLedgerEntriesByTransactionId(
    String transactionId,
  );
  Future<GeneralLedger?> getGeneralLedgerEntryById(int id);
  Future<GeneralLedger> createGeneralLedgerEntry(GeneralLedger entry);
  Future<GeneralLedger> updateGeneralLedgerEntry(GeneralLedger entry);
  Future<void> deleteGeneralLedgerEntry(int id);

  // Customer operations
  Future<List<Customer>> getCustomers();
  Future<Customer?> getCustomerById(String id);
  Future<Customer> createCustomer(Customer customer);
  Future<Customer> updateCustomer(Customer customer);
  Future<void> deleteCustomer(String id);

  // Vendor operations
  Future<List<Vendor>> getVendors();
  Future<Vendor?> getVendorById(String id);
  Future<Vendor> createVendor(Vendor vendor);
  Future<Vendor> updateVendor(Vendor vendor);
  Future<void> deleteVendor(String id);
  Future<List<Vendor>> searchVendors(String query);

  // Invoice operations
  Future<List<Invoice>> getInvoices();
  Future<Invoice?> getInvoiceById(int id);
  Future<Invoice> createInvoice(Invoice invoice);
  Future<Invoice> updateInvoice(Invoice invoice);
  Future<void> deleteInvoice(int id);

  // Vendor invoice operations
  Future<List<VendorInvoice>> getVendorInvoices();
  Future<VendorInvoice?> getVendorInvoiceById(int id);
  Future<List<VendorInvoice>> getVendorInvoicesByVendorId(String vendorId);
  Future<VendorInvoice> createVendorInvoice(VendorInvoice vendorInvoice);
  Future<VendorInvoice> updateVendorInvoice(VendorInvoice vendorInvoice);
  Future<void> deleteVendorInvoice(int id);

  // Expense operations
  Future<List<Expense>> getExpenses();
  Future<Expense?> getExpenseById(int id);
  Future<Expense> createExpense(Expense expense);
  Future<Expense> updateExpense(Expense expense);
  Future<void> deleteExpense(int id);

  // Payment in operations
  Future<List<PaymentIn>> getPaymentsIn();
  Future<PaymentIn?> getPaymentInById(int id);
  Future<PaymentIn> createPaymentIn(PaymentIn paymentIn);
  Future<PaymentIn> updatePaymentIn(PaymentIn paymentIn);
  Future<void> deletePaymentIn(int id);

  // Payment out operations
  Future<List<PaymentOut>> getPaymentsOut();
  Future<PaymentOut?> getPaymentOutById(int id);
  Future<PaymentOut> createPaymentOut(PaymentOut paymentOut);
  Future<PaymentOut> updatePaymentOut(PaymentOut paymentOut);
  Future<void> deletePaymentOut(int id);

  // Vendor Invoice Line Item Operations
  Future<List<VendorInvoiceLineItem>> getVendorInvoiceLineItems(int invoiceId);
  Future<VendorInvoiceLineItem?> getVendorInvoiceLineItemById(int lineItemId);
  Future<VendorInvoiceLineItem> createVendorInvoiceLineItem(
    VendorInvoiceLineItem lineItem,
  );
  Future<VendorInvoiceLineItem> updateVendorInvoiceLineItem(
    VendorInvoiceLineItem lineItem,
  );
  Future<void> deleteVendorInvoiceLineItem(int lineItemId);
  Future<List<VendorInvoiceLineItem>> getVendorInvoiceLineItemsByAccount(
    String accountNumber,
  );
}
