import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart' as error_handler;
import 'package:we_like_money/utils/exceptions.dart';
import 'package:flutter/foundation.dart';

/// Service responsible for creating General Ledger entries for vendor invoices
///
/// This service handles the double-entry bookkeeping for vendor invoices:
/// 1. Debit to the specified expense account (including tax)
/// 2. Credit to the accounts payable account
@injectable
class VendorInvoiceLedgerService {
  final DatabaseService _databaseService;
  final error_handler.ErrorHandler _errorHandler;
  final _uuid = const Uuid();

  /// Used for testing to bypass the mock creation in debug mode
  final bool enableMockCreation;

  /// Default accounts
  static const String _accountsPayableAccount =
      '2100'; // Accounts Payable Liability Account

  VendorInvoiceLedgerService(
    this._databaseService,
    this._errorHandler, {
    this.enableMockCreation = true,
  });

  /// Creates the necessary general ledger entries for a vendor invoice
  ///
  /// When a vendor invoice is created, two general ledger entries are created:
  /// 1. Debit to the specified expense account
  /// 2. Credit to the accounts payable account
  ///
  /// The total amount will include tax if present
  ///
  /// @throws BusinessException if:
  /// - The invoice is missing required fields
  /// - The expense account number is invalid
  /// - The database operations fail
  Future<List<GeneralLedger>> createGeneralLedgerEntriesForVendorInvoice(
    VendorInvoice invoice,
  ) async {
    try {
      _validateVendorInvoice(invoice);
      debugPrint(
        'Creating general ledger entries for vendor invoice: ${invoice.invoiceId}',
      );
      debugPrint(
        'Transaction date: ${invoice.invoiceDate}, Amount: ${invoice.amount}, Description: ${invoice.invoiceNumber}',
      );

      try {
        final transactionId = _uuid.v4();
        final debitEntry = await _createDebitEntry(invoice, transactionId);
        final creditEntry = await _createCreditEntry(invoice, transactionId);
        return [debitEntry, creditEntry];
      } catch (e) {
        if (enableMockCreation && kDebugMode) {
          // Create mock entries in debug mode
          final transactionId = _uuid.v4();
          final totalAmount = invoice.amount + (invoice.taxAmount ?? 0);

          final debitEntry = GeneralLedger(
            ledgerId: 0,
            transactionId: transactionId,
            transactionDate: invoice.invoiceDate,
            accountNumber: invoice.expenseAccountNumber,
            description: 'Invoice ${invoice.invoiceNumber}',
            debit: totalAmount,
            credit: 0,
            currencyCode: invoice.currencyCode,
            projectId: invoice.projectId,
            staffId: invoice.staffId,
            taxAmount: invoice.taxAmount,
            companyId: invoice.companyId!,
          );

          final creditEntry = GeneralLedger(
            ledgerId: 0,
            transactionId: transactionId,
            transactionDate: invoice.invoiceDate,
            accountNumber: _accountsPayableAccount,
            description: 'Invoice ${invoice.invoiceNumber}',
            debit: 0,
            credit: totalAmount,
            currencyCode: invoice.currencyCode,
            projectId: invoice.projectId,
            staffId: invoice.staffId,
            taxAmount: invoice.taxAmount,
            companyId: invoice.companyId!,
          );

          // Create mock entries in the database
          await _databaseService.createGeneralLedgerEntry(debitEntry);
          await _databaseService.createGeneralLedgerEntry(creditEntry);

          return [debitEntry, creditEntry];
        }
        final errorMessage = _errorHandler.handleError(e);
        throw BusinessException(errorMessage);
      }
    } catch (e) {
      if (e is BusinessException) {
        rethrow;
      }
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Validates that the vendor invoice has all required fields
  ///
  /// @throws BusinessException if any required field is missing or invalid
  void _validateVendorInvoice(VendorInvoice invoice) {
    final companyId = invoice.companyId;
    if (companyId == null || companyId <= 0) {
      throw BusinessException(
        'Company ID is required for creating ledger entries',
      );
    }
    if (invoice.vendorId.isEmpty) {
      throw BusinessException(
        'Vendor ID is required for creating ledger entries',
      );
    }
    if (invoice.amount <= 0) {
      throw BusinessException('Amount must be greater than zero');
    }
  }

  Future<GeneralLedger> _createDebitEntry(
    VendorInvoice invoice,
    String transactionId,
  ) async {
    final totalAmount = invoice.amount + (invoice.taxAmount ?? 0);

    final debitEntry = GeneralLedger(
      ledgerId: 0, // Will be assigned by the database
      transactionId: transactionId,
      transactionDate: invoice.invoiceDate,
      accountNumber: invoice.expenseAccountNumber,
      description: 'Invoice ${invoice.invoiceNumber}',
      debit: totalAmount,
      credit: 0,
      currencyCode: invoice.currencyCode,
      projectId: invoice.projectId,
      staffId: invoice.staffId,
      taxAmount: invoice.taxAmount,
      companyId: invoice.companyId!,
    );

    return await _databaseService.createGeneralLedgerEntry(debitEntry);
  }

  Future<GeneralLedger> _createCreditEntry(
    VendorInvoice invoice,
    String transactionId,
  ) async {
    final totalAmount = invoice.amount + (invoice.taxAmount ?? 0);

    final creditEntry = GeneralLedger(
      ledgerId: 0, // Will be assigned by the database
      transactionId: transactionId,
      transactionDate: invoice.invoiceDate,
      accountNumber: _accountsPayableAccount,
      description: 'Invoice ${invoice.invoiceNumber}',
      debit: 0,
      credit: totalAmount,
      currencyCode: invoice.currencyCode,
      projectId: invoice.projectId,
      staffId: invoice.staffId,
      taxAmount: invoice.taxAmount,
      companyId: invoice.companyId!,
    );

    return await _databaseService.createGeneralLedgerEntry(creditEntry);
  }
}
