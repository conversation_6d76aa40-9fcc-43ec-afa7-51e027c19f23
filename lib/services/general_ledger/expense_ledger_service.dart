import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart' as error_handler;
import 'package:we_like_money/utils/exceptions.dart';
import 'package:flutter/foundation.dart';

/// Service responsible for creating General Ledger entries for expenses
///
/// This service handles the double-entry bookkeeping for expenses:
/// 1. Debit to the expense account (including tax)
/// 2. Credit to the appropriate account based on payment method
@injectable
class ExpenseLedgerService {
  final DatabaseService _databaseService;
  final error_handler.ErrorHandler _errorHandler;
  final _uuid = const Uuid();

  /// Default accounts for different expense types
  static const String _defaultExpenseAccount =
      '5000'; // General Expense Account
  static const String _cashAccount = '1000'; // Cash Account
  static const String _creditCardAccount =
      '2100'; // Credit Card Liability Account
  static const String _bankAccount = '1100'; // Bank Account
  static const String _checkAccount = '1200'; // Check Account

  ExpenseLedgerService(this._databaseService, this._errorHandler);

  /// Creates the necessary general ledger entries for an expense
  ///
  /// When an expense is created, two general ledger entries are created:
  /// 1. Debit to the expense account (including tax)
  /// 2. Credit to the appropriate account based on payment method
  ///
  /// @throws BusinessException if:
  /// - The expense is missing required fields
  /// - The payment method is not supported
  /// - The database operations fail
  Future<List<GeneralLedger>> createEntriesForExpense(Expense expense) async {
    try {
      _validateExpense(expense);
      debugPrint(
        'Creating general ledger entries for expense: ${expense.expenseId}',
      );
      debugPrint(
        'Transaction date: ${expense.expenseDate}, Amount: ${expense.amount}, Description: ${expense.transactionId}',
      );

      final debitEntry = await _createDebitEntry(expense);
      final creditEntry = await _createCreditEntry(expense);

      return [debitEntry, creditEntry];
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Returns the appropriate account number based on the payment method
  ///
  /// @throws BusinessException if the payment method is not supported
  String _getAccountForPaymentMethod(PaymentMethod paymentMethod) {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return _cashAccount;
      case PaymentMethod.creditCard:
        return _creditCardAccount;
      case PaymentMethod.bankTransfer:
        return _bankAccount;
      case PaymentMethod.check:
        return _checkAccount;
      case PaymentMethod.other:
        return _bankAccount; // Default to bank account for 'other' payment methods
    }
  }

  /// Validates that the expense has all required fields
  ///
  /// @throws BusinessException if any required field is missing or invalid
  void _validateExpense(Expense expense) {
    if (expense.amount <= 0) {
      throw BusinessException('Expense amount must be greater than 0');
    }
    if (expense.currencyCode.isEmpty) {
      throw BusinessException('Currency code is required');
    }
    if (expense.vendorId.isEmpty) {
      throw BusinessException('Vendor ID is required');
    }
  }

  Future<GeneralLedger> _createDebitEntry(Expense expense) async {
    final transactionId = _uuid.v4();
    final totalAmount = expense.amount + (expense.taxAmount ?? 0);

    final debitEntry = GeneralLedger(
      ledgerId: 0, // Will be assigned by the database
      transactionId: transactionId,
      transactionDate: expense.expenseDate,
      accountNumber: _defaultExpenseAccount,
      description: 'Expense: ${expense.vendorId}',
      debit: totalAmount,
      credit: 0,
      currencyCode: expense.currencyCode,
      projectId: expense.projectId,
      staffId: expense.staffId,
      taxAmount: expense.taxAmount,
      companyId: expense.companyId,
    );

    return await _databaseService.createGeneralLedgerEntry(debitEntry);
  }

  Future<GeneralLedger> _createCreditEntry(Expense expense) async {
    final transactionId = _uuid.v4();
    final totalAmount = expense.amount + (expense.taxAmount ?? 0);

    final creditAccount = _getAccountForPaymentMethod(expense.paymentMethod);

    final creditEntry = GeneralLedger(
      ledgerId: 0, // Will be assigned by the database
      transactionId: transactionId,
      transactionDate: expense.expenseDate,
      accountNumber: creditAccount,
      description: 'Payment for expense: ${expense.vendorId}',
      debit: 0,
      credit: totalAmount,
      currencyCode: expense.currencyCode,
      projectId: expense.projectId,
      staffId: expense.staffId,
      taxAmount: expense.taxAmount,
      companyId: expense.companyId,
    );

    return await _databaseService.createGeneralLedgerEntry(creditEntry);
  }
}
