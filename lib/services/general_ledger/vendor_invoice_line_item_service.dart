import 'package:we_like_money/models/vendor_invoice_line_item.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/exceptions.dart';

class VendorInvoiceLineItemService {
  final DatabaseService _databaseService;

  VendorInvoiceLineItemService(this._databaseService);

  /// Get all line items for a vendor invoice
  Future<List<VendorInvoiceLineItem>> getLineItems(int invoiceId) async {
    try {
      return await _databaseService.getVendorInvoiceLineItems(invoiceId);
    } catch (e) {
      throw BusinessException('Failed to get vendor invoice line items: $e');
    }
  }

  /// Get a specific line item by ID
  Future<VendorInvoiceLineItem?> getLineItemById(int lineItemId) async {
    try {
      return await _databaseService.getVendorInvoiceLineItemById(lineItemId);
    } catch (e) {
      throw BusinessException('Failed to get vendor invoice line item: $e');
    }
  }

  /// Create a new line item
  Future<VendorInvoiceLineItem> createLineItem(
    VendorInvoiceLineItem lineItem,
  ) async {
    try {
      return await _databaseService.createVendorInvoiceLineItem(lineItem);
    } catch (e) {
      throw BusinessException('Failed to create vendor invoice line item: $e');
    }
  }

  /// Update an existing line item
  Future<VendorInvoiceLineItem> updateLineItem(
    VendorInvoiceLineItem lineItem,
  ) async {
    try {
      return await _databaseService.updateVendorInvoiceLineItem(lineItem);
    } catch (e) {
      throw BusinessException('Failed to update vendor invoice line item: $e');
    }
  }

  /// Delete a line item
  Future<void> deleteLineItem(int lineItemId) async {
    try {
      await _databaseService.deleteVendorInvoiceLineItem(lineItemId);
    } catch (e) {
      throw BusinessException('Failed to delete vendor invoice line item: $e');
    }
  }

  /// Get all line items for a specific account
  Future<List<VendorInvoiceLineItem>> getLineItemsByAccount(
    String accountNumber,
  ) async {
    try {
      return await _databaseService.getVendorInvoiceLineItemsByAccount(
        accountNumber,
      );
    } catch (e) {
      throw BusinessException(
        'Failed to get vendor invoice line items by account: $e',
      );
    }
  }

  /// Create multiple line items for a vendor invoice
  Future<List<VendorInvoiceLineItem>> createLineItems(
    List<VendorInvoiceLineItem> lineItems,
  ) async {
    try {
      final List<VendorInvoiceLineItem> createdItems = [];
      for (final item in lineItems) {
        final created = await _databaseService.createVendorInvoiceLineItem(
          item,
        );
        createdItems.add(created);
      }
      return createdItems;
    } catch (e) {
      throw BusinessException('Failed to create vendor invoice line items: $e');
    }
  }

  /// Update multiple line items
  Future<List<VendorInvoiceLineItem>> updateLineItems(
    List<VendorInvoiceLineItem> lineItems,
  ) async {
    try {
      final List<VendorInvoiceLineItem> updatedItems = [];
      for (final item in lineItems) {
        final updated = await _databaseService.updateVendorInvoiceLineItem(
          item,
        );
        updatedItems.add(updated);
      }
      return updatedItems;
    } catch (e) {
      throw BusinessException('Failed to update vendor invoice line items: $e');
    }
  }

  /// Delete multiple line items
  Future<void> deleteLineItems(List<int> lineItemIds) async {
    try {
      for (final id in lineItemIds) {
        await _databaseService.deleteVendorInvoiceLineItem(id);
      }
    } catch (e) {
      throw BusinessException('Failed to delete vendor invoice line items: $e');
    }
  }
}
