import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';

/// Service responsible for managing expenses in the database
class ExpenseService {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;

  ExpenseService(this._databaseService, this._errorHandler);

  /// Validates an expense
  void _validateExpense(Expense expense) {
    if (expense.transactionId.isEmpty) {
      throw BusinessException('Transaction ID is required');
    }
    if (expense.vendorId.isEmpty) {
      throw BusinessException('Vendor ID is required');
    }
    if (expense.amount <= 0) {
      throw BusinessException('Amount must be greater than 0');
    }
    if (expense.currencyCode.isEmpty) {
      throw BusinessException('Currency code is required');
    }
    if (expense.taxAmount != null && expense.taxAmount! < 0) {
      throw BusinessException('Tax amount cannot be negative');
    }
  }

  /// Retrieves all expenses
  Future<List<Expense>> getExpenses() async {
    try {
      return await _databaseService.getExpenses();
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Retrieves an expense by ID
  Future<Expense?> getExpenseById(int id) async {
    try {
      return await _databaseService.getExpenseById(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Creates a new expense
  Future<Expense> createExpense(Expense expense) async {
    try {
      _validateExpense(expense);
      return await _databaseService.createExpense(expense);
    } on BusinessException {
      rethrow;
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Updates an existing expense
  Future<Expense> updateExpense(Expense expense) async {
    try {
      // Check if expense exists
      final existingExpense = await getExpenseById(expense.expenseId);
      if (existingExpense == null) {
        throw BusinessException('Expense not found');
      }

      _validateExpense(expense);
      return await _databaseService.updateExpense(expense);
    } on BusinessException {
      rethrow;
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Deletes an expense
  Future<void> deleteExpense(int id) async {
    try {
      // Check if expense exists
      final existingExpense = await getExpenseById(id);
      if (existingExpense == null) {
        throw BusinessException('Expense not found');
      }

      await _databaseService.deleteExpense(id);
    } on BusinessException {
      rethrow;
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }
}
