import 'package:injectable/injectable.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';

/// ViewModel for StaffMember operations
@injectable
class StaffMemberViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;

  StaffMemberViewModel(this._databaseService, this._errorHandler);

  /// Retrieves all staff members
  Future<List<StaffMember>> getStaffMembers() async {
    try {
      return await _databaseService.getStaffMembers();
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Retrieves a staff member by ID
  Future<StaffMember?> getStaffMemberById(int id) async {
    try {
      return await _databaseService.getStaffMemberById(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Creates a new staff member
  Future<StaffMember> createStaffMember(StaffMember staffMember) async {
    try {
      return await _databaseService.createStaffMember(staffMember);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Updates an existing staff member
  Future<StaffMember> updateStaffMember(StaffMember staffMember) async {
    try {
      return await _databaseService.updateStaffMember(staffMember);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Deletes a staff member
  Future<void> deleteStaffMember(int id) async {
    try {
      await _databaseService.deleteStaffMember(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }
}
