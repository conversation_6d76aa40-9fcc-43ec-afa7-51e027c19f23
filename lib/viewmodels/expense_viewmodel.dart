import 'package:injectable/injectable.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/general_ledger/expense_ledger_service.dart';
import 'package:we_like_money/utils/error_handler.dart';
import 'package:we_like_money/utils/exceptions.dart';

/// ViewModel for Expense operations
@injectable
class ExpenseViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;
  final ExpenseLedgerService _expenseLedgerService;

  ExpenseViewModel(
    this._databaseService,
    this._errorHandler,
    this._expenseLedgerService,
  );

  /// Retrieves all expenses
  Future<List<Expense>> getExpenses() async {
    try {
      return await _databaseService.getExpenses();
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Retrieves an expense by ID
  Future<Expense?> getExpenseById(int id) async {
    try {
      return await _databaseService.getExpenseById(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Creates a new expense and corresponding general ledger entries
  Future<Expense> createExpense(Expense expense) async {
    try {
      // Create the expense in the database
      final createdExpense = await _databaseService.createExpense(expense);

      // Create corresponding general ledger entries
      await _expenseLedgerService.createEntriesForExpense(createdExpense);

      return createdExpense;
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Updates an existing expense
  Future<Expense> updateExpense(Expense expense) async {
    try {
      return await _databaseService.updateExpense(expense);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  /// Deletes an expense
  Future<void> deleteExpense(int id) async {
    try {
      await _databaseService.deleteExpense(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }
}
