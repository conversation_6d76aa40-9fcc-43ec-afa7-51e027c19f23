#!/bin/bash

# Update imports in migrated files

# Function to update imports in a file
update_imports() {
  local file=$1
  
  # Update widget imports
  sed -i '' 's|import .*/widgets/custom_dropdown.dart|import "package:we_like_money/ui/common/widgets/custom_dropdown.dart"|g' "$file"
  sed -i '' 's|import .*/widgets/date_picker_field.dart|import "package:we_like_money/ui/common/widgets/date_picker_field.dart"|g' "$file"
  sed -i '' 's|import .*/widgets/error_display.dart|import "package:we_like_money/ui/common/widgets/error_display.dart"|g' "$file"
  sed -i '' 's|import .*/widgets/loading_display.dart|import "package:we_like_money/ui/common/widgets/loading_display.dart"|g' "$file"
  sed -i '' 's|import .*/ui/widgets/company_selector.dart|import "package:we_like_money/ui/common/widgets/company_selector.dart"|g' "$file"
  sed -i '' 's|import .*/ui/widgets/connection_status.dart|import "package:we_like_money/ui/common/widgets/connection_status.dart"|g' "$file"
  sed -i '' 's|import .*/ui/widgets/currency_display.dart|import "package:we_like_money/ui/common/widgets/currency_display.dart"|g' "$file"
  sed -i '' 's|import .*/ui/widgets/form_fields.dart|import "package:we_like_money/ui/common/widgets/form_fields.dart"|g' "$file"
  
  # Update account feature imports
  sed -i '' 's|import .*/screens/account/account_list_screen.dart|import "package:we_like_money/ui/features/accounts/account_list_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/account_list_screen_refactored.dart|import "package:we_like_money/ui/features/accounts/account_list_screen_refactored.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/account_register_screen.dart|import "package:we_like_money/ui/features/accounts/account_register_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/components/account_list_item.dart|import "package:we_like_money/ui/features/accounts/components/account_list_item.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/components/account_search_bar.dart|import "package:we_like_money/ui/features/accounts/components/account_search_bar.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/components/empty_accounts_view.dart|import "package:we_like_money/ui/features/accounts/components/empty_accounts_view.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/components/error_view.dart|import "package:we_like_money/ui/features/accounts/components/error_view.dart"|g' "$file"
  sed -i '' 's|import .*/screens/account/components/loading_view.dart|import "package:we_like_money/ui/features/accounts/components/loading_view.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/account_details_screen.dart|import "package:we_like_money/ui/features/accounts/account_details_screen.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/accounts_screen.dart|import "package:we_like_money/ui/features/accounts/accounts_screen.dart"|g' "$file"
  
  # Update general ledger feature imports
  sed -i '' 's|import .*/screens/general_ledger/general_ledger_screen_refactored.dart|import "package:we_like_money/ui/features/general_ledger/general_ledger_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/empty_ledger_view.dart|import "package:we_like_money/ui/features/general_ledger/components/empty_ledger_view.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/error_view.dart|import "package:we_like_money/ui/features/general_ledger/components/error_view.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/filter_bar.dart|import "package:we_like_money/ui/features/general_ledger/components/filter_bar.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/ledger_table.dart|import "package:we_like_money/ui/features/general_ledger/components/ledger_table.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/loading_view.dart|import "package:we_like_money/ui/features/general_ledger/components/loading_view.dart"|g' "$file"
  sed -i '' 's|import .*/screens/general_ledger/components/summary_card.dart|import "package:we_like_money/ui/features/general_ledger/components/summary_card.dart"|g' "$file"
  
  # Update vendor feature imports
  sed -i '' 's|import .*/screens/vendor/vendor_detail_screen.dart|import "package:we_like_money/ui/features/vendors/vendor_detail_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/vendor_detail_screen_refactored.dart|import "package:we_like_money/ui/features/vendors/vendor_detail_screen_refactored.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/vendor_list_screen.dart|import "package:we_like_money/ui/features/vendors/vendor_list_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/vendor_quick_create_dialog.dart|import "package:we_like_money/ui/features/vendors/vendor_quick_create_dialog.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/components/address_section.dart|import "package:we_like_money/ui/features/vendors/components/address_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/components/banking_info_section.dart|import "package:we_like_money/ui/features/vendors/components/banking_info_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/components/basic_info_section.dart|import "package:we_like_money/ui/features/vendors/components/basic_info_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/components/contact_info_section.dart|import "package:we_like_money/ui/features/vendors/components/contact_info_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor/components/save_button_section.dart|import "package:we_like_money/ui/features/vendors/components/save_button_section.dart"|g' "$file"
  
  # Update vendor invoice feature imports
  sed -i '' 's|import .*/screens/vendor_invoice/vendor_invoice_detail_screen.dart|import "package:we_like_money/ui/features/vendor_invoices/vendor_invoice_detail_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/vendor_invoice_entry_screen.dart|import "package:we_like_money/ui/features/vendor_invoices/vendor_invoice_entry_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/vendor_invoice_form_state.dart|import "package:we_like_money/ui/features/vendor_invoices/vendor_invoice_form_state.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/vendor_invoice_list_screen.dart|import "package:we_like_money/ui/features/vendor_invoices/vendor_invoice_list_screen.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/vendor_invoice_view_model.dart|import "package:we_like_money/ui/features/vendor_invoices/vendor_invoice_view_model.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/accounting_section.dart|import "package:we_like_money/ui/features/vendor_invoices/components/accounting_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/invoice_details_section.dart|import "package:we_like_money/ui/features/vendor_invoices/components/invoice_details_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/invoice_filter_dialog.dart|import "package:we_like_money/ui/features/vendor_invoices/components/invoice_filter_dialog.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/invoice_list_item.dart|import "package:we_like_money/ui/features/vendor_invoices/components/invoice_list_item.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/invoice_search_bar.dart|import "package:we_like_money/ui/features/vendor_invoices/components/invoice_search_bar.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/optional_fields_section.dart|import "package:we_like_money/ui/features/vendor_invoices/components/optional_fields_section.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/save_button.dart|import "package:we_like_money/ui/features/vendor_invoices/components/save_button.dart"|g' "$file"
  sed -i '' 's|import .*/screens/vendor_invoice/components/vendor_selection_section.dart|import "package:we_like_money/ui/features/vendor_invoices/components/vendor_selection_section.dart"|g' "$file"
  
  # Update expense feature imports
  sed -i '' 's|import .*/ui/screens/expense/expense_entry_screen.dart|import "package:we_like_money/ui/features/expenses/expense_entry_screen.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/expense_entry_screen_refactored.dart|import "package:we_like_money/ui/features/expenses/expense_entry_screen_refactored.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/expense_form_state.dart|import "package:we_like_money/ui/features/expenses/expense_form_state.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/expense_view_model.dart|import "package:we_like_money/ui/features/expenses/expense_view_model.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/expense_details_section.dart|import "package:we_like_money/ui/features/expenses/components/expense_details_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/form_section_header.dart|import "package:we_like_money/ui/features/expenses/components/form_section_header.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/payment_details_section.dart|import "package:we_like_money/ui/features/expenses/components/payment_details_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/project_staff_section.dart|import "package:we_like_money/ui/features/expenses/components/project_staff_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/save_button.dart|import "package:we_like_money/ui/features/expenses/components/save_button.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/vendor_selection_section.dart|import "package:we_like_money/ui/features/expenses/components/vendor_selection_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/refactored/expense_details_section.dart|import "package:we_like_money/ui/features/expenses/components/refactored/expense_details_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/refactored/payment_details_section.dart|import "package:we_like_money/ui/features/expenses/components/refactored/payment_details_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/refactored/project_staff_section.dart|import "package:we_like_money/ui/features/expenses/components/refactored/project_staff_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expense/components/refactored/vendor_section.dart|import "package:we_like_money/ui/features/expenses/components/refactored/vendor_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/expenses_screen.dart|import "package:we_like_money/ui/features/expenses/expenses_screen.dart"|g' "$file"
  
  # Update other screens imports
  sed -i '' 's|import .*/ui/screens/home_screen.dart|import "package:we_like_money/ui/features/home/<USER>"|g' "$file"
  sed -i '' 's|import .*/ui/screens/settings_screen.dart|import "package:we_like_money/ui/features/settings/settings_screen.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/companies_screen.dart|import "package:we_like_money/ui/features/companies/companies_screen.dart"|g' "$file"
  sed -i '' 's|import .*/ui/screens/projects_screen.dart|import "package:we_like_money/ui/features/projects/projects_screen.dart"|g' "$file"
  
  # Update form sections imports
  sed -i '' 's|import .*/ui/forms/form_section.dart|import "package:we_like_money/ui/forms/sections/form_section.dart"|g' "$file"
  sed -i '' 's|import .*/ui/forms/form_section_header.dart|import "package:we_like_money/ui/forms/sections/form_section_header.dart"|g' "$file"
}

# Find all Dart files in the new UI directory and update imports
find lib/ui -name "*.dart" -type f | while read file; do
  echo "Updating imports in $file"
  update_imports "$file"
done

# Update imports in other files that might reference the UI components
find lib -name "*.dart" -type f | grep -v "lib/ui/" | while read file; do
  echo "Updating imports in $file"
  update_imports "$file"
done

echo "Import updates complete."
