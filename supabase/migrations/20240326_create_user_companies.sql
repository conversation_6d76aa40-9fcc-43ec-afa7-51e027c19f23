-- Create user_companies table
CREATE TABLE user_companies (
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id INTEGER NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, company_id)
);

-- Add indexes for better query performance
CREATE INDEX idx_user_companies_user_id ON user_companies(user_id);
CREATE INDEX idx_user_companies_company_id ON user_companies(company_id);

-- Add RLS policies
ALTER TABLE user_companies ENABLE ROW LEVEL SECURITY;

-- Users can view their own company associations
CREATE POLICY "Users can view their own company associations"
    ON user_companies FOR SELECT
    USING (user_id = auth.uid());

-- Users can insert their own company associations
CREATE POLICY "Users can insert their own company associations"
    ON user_companies FOR INSERT
    WITH CHECK (user_id = auth.uid());

-- Users can delete their own company associations
CREATE POLICY "Users can delete their own company associations"
    ON user_companies FOR DELETE
    USING (user_id = auth.uid()); 