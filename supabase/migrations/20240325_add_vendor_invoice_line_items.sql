-- Create vendor_invoice_line_items table
CREATE TABLE vendor_invoice_line_items (
    line_item_id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES vendor_invoices(invoice_id) ON DELETE CASCADE,
    account_number VARCHAR NOT NULL REFERENCES accounts(account_number),
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    company_id INTEGER NOT NULL REFERENCES companies(company_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for better query performance
CREATE INDEX idx_vendor_invoice_line_items_invoice_id ON vendor_invoice_line_items(invoice_id);
CREATE INDEX idx_vendor_invoice_line_items_account_number ON vendor_invoice_line_items(account_number);
CREATE INDEX idx_vendor_invoice_line_items_company_id ON vendor_invoice_line_items(company_id);

-- Remove expense_account_number from vendor_invoices as it's now handled by line items
ALTER TABLE vendor_invoices DROP COLUMN expense_account_number;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vendor_invoice_line_items_updated_at
    BEFORE UPDATE ON vendor_invoice_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE vendor_invoice_line_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their company's vendor invoice line items"
    ON vendor_invoice_line_items FOR SELECT
    USING (company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    ));

CREATE POLICY "Users can insert vendor invoice line items for their company"
    ON vendor_invoice_line_items FOR INSERT
    WITH CHECK (company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    ));

CREATE POLICY "Users can update their company's vendor invoice line items"
    ON vendor_invoice_line_items FOR UPDATE
    USING (company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    ));

CREATE POLICY "Users can delete their company's vendor invoice line items"
    ON vendor_invoice_line_items FOR DELETE
    USING (company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    )); 