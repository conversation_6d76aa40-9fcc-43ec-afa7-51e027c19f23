-- Check if each column exists and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'vendor_number') THEN
        ALTER TABLE vendors ADD COLUMN vendor_number VARCHAR;
        RAISE NOTICE 'Added vendor_number column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'organization_number') THEN
        ALTER TABLE vendors ADD COLUMN organization_number VARCHAR;
        RAISE NOTICE 'Added organization_number column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'is_active') THEN
        ALTER TABLE vendors ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
        RAISE NOTICE 'Added is_active column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'phone') THEN
        ALTER TABLE vendors ADD COLUMN phone VARCHAR;
        RAISE NOTICE 'Added phone column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'email') THEN
        ALTER TABLE vendors ADD COLUMN email VARCHAR;
        RAISE NOTICE 'Added email column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'zip_code') THEN
        ALTER TABLE vendors ADD COLUMN zip_code VARCHAR;
        RAISE NOTICE 'Added zip_code column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'city') THEN
        ALTER TABLE vendors ADD COLUMN city VARCHAR;
        RAISE NOTICE 'Added city column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'country') THEN
        ALTER TABLE vendors ADD COLUMN country VARCHAR;
        RAISE NOTICE 'Added country column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'bank_account_type') THEN
        ALTER TABLE vendors ADD COLUMN bank_account_type VARCHAR;
        RAISE NOTICE 'Added bank_account_type column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendors' AND column_name = 'bank_account_number') THEN
        ALTER TABLE vendors ADD COLUMN bank_account_number VARCHAR;
        RAISE NOTICE 'Added bank_account_number column';
    END IF;

    -- Add comments to describe the table and fields if the table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') THEN
        COMMENT ON TABLE vendors IS 'Vendors for accounts payable operations';
        COMMENT ON COLUMN vendors.vendor_id IS 'Unique vendor identifier';
        COMMENT ON COLUMN vendors.vendor_name IS 'Name of the vendor (required)';
        COMMENT ON COLUMN vendors.vendor_number IS 'Vendor reference number';
        COMMENT ON COLUMN vendors.organization_number IS 'Organization/tax ID number';
        COMMENT ON COLUMN vendors.is_active IS 'Whether the vendor is active';
        COMMENT ON COLUMN vendors.phone IS 'Contact phone number';
        COMMENT ON COLUMN vendors.email IS 'Contact email address';
        COMMENT ON COLUMN vendors.address IS 'Physical address';
        COMMENT ON COLUMN vendors.zip_code IS 'ZIP/Postal code';
        COMMENT ON COLUMN vendors.city IS 'City';
        COMMENT ON COLUMN vendors.country IS 'Country';
        COMMENT ON COLUMN vendors.bank_account_type IS 'Type of bank account for payments';
        COMMENT ON COLUMN vendors.bank_account_number IS 'Bank account number for payments';
        COMMENT ON COLUMN vendors.contact_person IS 'Primary contact at vendor';
        COMMENT ON COLUMN vendors.company_id IS 'Associated company ID';
        
        RAISE NOTICE 'Added comments to vendors table and columns';
    END IF;
    
    -- Create RLS policies if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') AND 
       NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'vendors_select') THEN
        -- Enable RLS on vendors table
        ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for authenticated users to select vendors
        CREATE POLICY vendors_select ON vendors
            FOR SELECT
            USING (true);
            
        RAISE NOTICE 'Created SELECT policy for vendors table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') AND 
       NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'vendors_insert') THEN
        -- Create policy for authenticated users to insert vendors
        CREATE POLICY vendors_insert ON vendors
            FOR INSERT
            WITH CHECK (true);
            
        RAISE NOTICE 'Created INSERT policy for vendors table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') AND 
       NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'vendors_update') THEN
        -- Create policy for authenticated users to update vendors
        CREATE POLICY vendors_update ON vendors
            FOR UPDATE
            USING (true)
            WITH CHECK (true);
            
        RAISE NOTICE 'Created UPDATE policy for vendors table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') AND 
       NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'vendors_delete') THEN
        -- Create policy for authenticated users to delete vendors
        CREATE POLICY vendors_delete ON vendors
            FOR DELETE
            USING (true);
            
        RAISE NOTICE 'Created DELETE policy for vendors table';
    END IF;

    -- Create vendors table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendors') THEN
        CREATE TABLE vendors (
            vendor_id VARCHAR PRIMARY KEY,
            vendor_name VARCHAR NOT NULL,
            vendor_number VARCHAR,
            organization_number VARCHAR,
            is_active BOOLEAN DEFAULT TRUE,
            phone VARCHAR,
            email VARCHAR,
            address VARCHAR,
            zip_code VARCHAR,
            city VARCHAR,
            country VARCHAR,
            bank_account_type VARCHAR,
            bank_account_number VARCHAR,
            contact_person VARCHAR,
            company_id INTEGER REFERENCES companies(company_id) ON DELETE CASCADE
        );
        
        -- Enable RLS on vendors table
        ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for authenticated users to select vendors
        CREATE POLICY vendors_select ON vendors
            FOR SELECT
            USING (true);
            
        -- Create policy for authenticated users to insert vendors
        CREATE POLICY vendors_insert ON vendors
            FOR INSERT
            WITH CHECK (true);
            
        -- Create policy for authenticated users to update vendors
        CREATE POLICY vendors_update ON vendors
            FOR UPDATE
            USING (true)
            WITH CHECK (true);
            
        -- Create policy for authenticated users to delete vendors
        CREATE POLICY vendors_delete ON vendors
            FOR DELETE
            USING (true);
            
        RAISE NOTICE 'Created vendors table with all columns and policies';
    END IF;
END $$; 