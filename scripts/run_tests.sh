#!/bin/bash

# <PERSON>ript to run tests with different configurations

# Function to display usage information
show_usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  --online    Run tests against the remote Supabase instance"
  echo "  --offline   Run tests with mocks (default)"
  echo "  --all       Run both online and offline tests"
  echo "  --help      Display this help message"
}

# Parse command line arguments
case "$1" in
  --online)
    echo "Running tests in ONLINE mode against Supabase..."
    cp .env.test .env.test.bak 2>/dev/null || :
    echo "SUPABASE_TEST_ONLINE=true" > .env.test
    if [ -f .env.test.bak ]; then
      grep -v "SUPABASE_TEST_ONLINE" .env.test.bak >> .env.test
      rm .env.test.bak
    fi
    flutter test
    ;;
  --offline)
    echo "Running tests in OFFLINE mode with mocks..."
    cp .env.test .env.test.bak 2>/dev/null || :
    echo "SUPABASE_TEST_ONLINE=false" > .env.test
    if [ -f .env.test.bak ]; then
      grep -v "SUPABASE_TEST_ONLINE" .env.test.bak >> .env.test
      rm .env.test.bak
    fi
    flutter test
    ;;
  --all)
    echo "Running tests in OFFLINE mode with mocks..."
    cp .env.test .env.test.bak 2>/dev/null || :
    echo "SUPABASE_TEST_ONLINE=false" > .env.test
    if [ -f .env.test.bak ]; then
      grep -v "SUPABASE_TEST_ONLINE" .env.test.bak >> .env.test
      rm .env.test.bak
    fi
    flutter test
    
    echo "Running tests in ONLINE mode against Supabase..."
    cp .env.test .env.test.bak 2>/dev/null || :
    echo "SUPABASE_TEST_ONLINE=true" > .env.test
    if [ -f .env.test.bak ]; then
      grep -v "SUPABASE_TEST_ONLINE" .env.test.bak >> .env.test
      rm .env.test.bak
    fi
    flutter test
    ;;
  --help)
    show_usage
    ;;
  *)
    echo "Running tests in OFFLINE mode with mocks (default)..."
    cp .env.test .env.test.bak 2>/dev/null || :
    echo "SUPABASE_TEST_ONLINE=false" > .env.test
    if [ -f .env.test.bak ]; then
      grep -v "SUPABASE_TEST_ONLINE" .env.test.bak >> .env.test
      rm .env.test.bak
    fi
    flutter test
    ;;
esac 