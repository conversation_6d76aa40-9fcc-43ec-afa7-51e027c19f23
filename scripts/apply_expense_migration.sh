#!/bin/bash

# This script applies the migrations to add payment_method and credit_card_number to the Expenses table
# Make sure to have the correct environment variables set in your .env file

# Load environment variables
source .env

# Check if we have Supabase credentials
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
  echo "Error: Missing Supabase credentials in .env file"
  exit 1
fi

# Apply migration using PSQL (if using direct PostgreSQL connection)
# Uncomment and modify as needed for your environment
# PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -U "$DB_USER" -f sql/migrations/add_payment_method_to_expenses.sql

# Alternative: Apply using Supabase REST API
echo "Applying migration to add payment method fields to Expenses table..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_KEY" \
  -H "Authorization: Bearer $SUPABASE_KEY" \
  -H "Content-Type: application/json" \
  -d @- << EOF
{
  "query": "$(cat sql/migrations/add_payment_method_to_expenses.sql | tr -d '\n')"
}
EOF

echo ""
echo "Migration completed." 