#!/bin/bash

# Create labels
gh label create "enhancement" --color "0E8A16" --description "New feature or enhancement"
gh label create "dependencies" --color "0366D6" --description "Dependency updates and changes"
gh label create "localization" --color "0052CC" --description "Localization and internationalization"
gh label create "testing" --color "FFD700" --description "Testing and test coverage"
gh label create "documentation" --color "0052CC" --description "Documentation updates"
gh label create "infrastructure" --color "FFA500" --description "Infrastructure and deployment"
gh label create "database" --color "FF69B4" --description "Database related changes"
gh label create "models" --color "6A737D" --description "Data model changes"
gh label create "services" --color "6A737D" --description "Service layer changes"
gh label create "business-logic" --color "6A737D" --description "Business logic changes"
gh label create "ui" --color "FF69B4" --description "User interface changes"
gh label create "frontend" --color "FF69B4" --description "Frontend development"
gh label create "security" --color "FF0000" --description "Security related changes" 