# General Ledger Integration

## Overview

This document describes the integration between the expense management system and the general ledger. The integration ensures that when expenses are recorded, the appropriate accounting entries are automatically created to maintain the financial integrity of the system.

## Architectural Design

### Components

1. **ExpenseViewModel**: Responsible for exposing expense operations to the UI and orchestrating the creation of both expenses and their corresponding general ledger entries.

2. **ExpenseLedgerService**: Dedicated service that handles the creation of general ledger entries for expenses. It encapsulates the business logic for determining which accounts to debit and credit.

3. **DatabaseService**: Underlying data access layer that persists both expense and general ledger data to the database.

### Flow

1. UI submits a request to create a new expense via the ExpenseViewModel.
2. ExpenseViewModel creates the expense in the database.
3. ExpenseViewModel calls the ExpenseLedgerService to create the corresponding general ledger entries.
4. ExpenseLedgerService generates two entries (debit and credit) and persists them through the DatabaseService.
5. The transaction is complete when both the expense and general ledger entries are successfully created.

## General Ledger Entries

### Accounts Used

| Account Number | Description                   | Type       |
|----------------|-------------------------------|------------|
| 5000           | General Expense Account       | Expense    |
| 1000           | Cash Account                  | Asset      |
| 2000           | Credit Card Liability Account | Liability  |
| 1100           | Bank Account                  | Asset      |

### Entry Creation

For each expense, two general ledger entries are created:

1. **Debit Entry**:
   - Account: General Expense Account (5000)
   - Amount: Expense amount + tax amount
   - Includes project ID, staff ID, and company ID from the expense
   - Stores the tax amount for reporting purposes

2. **Credit Entry**:
   - Account: Determined by the payment method:
     - Cash: Cash Account (1000)
     - Credit Card: Credit Card Liability Account (2000)
     - Bank Transfer: Bank Account (1100)
   - Amount: Expense amount + tax amount (matching the debit)
   - Includes the same project ID, staff ID, and company ID
   - Does not store the tax amount (already captured in the debit entry)

### Payment Method Handling

The service automatically selects the appropriate account to credit based on the payment method:

```dart
String _getAccountForPaymentMethod(PaymentMethod? paymentMethod) {
  switch (paymentMethod) {
    case PaymentMethod.cash:
      return _cashAccount;
    case PaymentMethod.creditCard:
      return _creditCardAccount;
    case PaymentMethod.bankTransfer:
      return _bankAccount;
    case PaymentMethod.other:
      return _cashAccount; // Default to cash for "other" payment methods
    case null:
      return _cashAccount; // Default to cash if payment method is not specified
  }
}
```

## Testing Strategy

The integration is thoroughly tested at multiple levels:

1. **Unit Tests**:
   - Tests for ExpenseLedgerService verify that:
     - The correct accounts are selected based on payment method
     - The correct amounts are used (including tax handling)
     - The appropriate metadata is copied from the expense to the ledger entries

   - Tests for ExpenseViewModel verify that:
     - The general ledger service is called with the correct expense
     - Errors are properly handled and propagated

2. **Integration Tests**:
   - Verify the end-to-end flow from expense creation to general ledger entry creation

## Example Code

### Creating General Ledger Entries for an Expense

```dart
Future<List<GeneralLedger>> createEntriesForExpense(Expense expense) async {
  final entries = <GeneralLedger>[];
  
  // Determine the account to credit based on payment method
  final creditAccount = _getAccountForPaymentMethod(expense.paymentMethod);
  
  // Calculate total amount (expense + tax)
  final totalAmount = expense.amount + (expense.taxAmount ?? 0);
  
  // Create debit entry (expense account)
  final debitEntry = GeneralLedger(
    ledgerId: 0, // Will be assigned by the database
    transactionDate: expense.expenseDate,
    accountNumber: _defaultExpenseAccount,
    description: 'Expense: ${expense.vendorId}',
    debit: totalAmount,
    credit: 0,
    currencyCode: expense.currencyCode,
    projectId: expense.projectId,
    staffId: expense.staffId,
    taxAmount: expense.taxAmount,
    companyId: expense.companyId,
  );
  
  // Create credit entry (payment method account)
  final creditEntry = GeneralLedger(
    ledgerId: 0, // Will be assigned by the database
    transactionDate: expense.expenseDate,
    accountNumber: creditAccount,
    description: 'Expense payment: ${expense.vendorId}',
    debit: 0,
    credit: totalAmount,
    currencyCode: expense.currencyCode,
    projectId: expense.projectId,
    staffId: expense.staffId,
    taxAmount: null, // Tax is only recorded on the debit side
    companyId: expense.companyId,
  );
  
  // Create entries in the database
  final savedDebitEntry = await _databaseService.createGeneralLedgerEntry(debitEntry);
  entries.add(savedDebitEntry);
  
  final savedCreditEntry = await _databaseService.createGeneralLedgerEntry(creditEntry);
  entries.add(savedCreditEntry);
  
  return entries;
}
```

## Future Improvements

1. **Transaction Support**: Implement database transactions to ensure both the expense and general ledger entries are created atomically.
2. **Custom Accounts**: Allow specifying different expense accounts based on expense categories.
3. **Entry Updates**: Add support for updating general ledger entries when an expense is updated.
4. **Entry Deletion**: Cascade delete general ledger entries when an expense is deleted.
5. **Reporting**: Develop comprehensive financial reports based on general ledger data. 