# Expense Management

## Overview

The expense management module allows users to track, record, and manage all expenses. This module is critical for accurate financial reporting and supports the following features:

- Recording expenses with vendor information
- Categorizing expenses by project and staff member
- Tracking payment methods (cash, credit card, bank transfer)
- Supporting different currencies
- Managing tax amounts
- Automatic creation of general ledger entries

## Data Model

### Expense Entity

```dart
class Expense {
  final int expenseId;
  final String vendorId;
  final DateTime expenseDate;
  final double amount;
  final String currencyCode;
  final int? projectId;
  final int? staffId;
  final double taxAmount;
  final int companyId;
  final PaymentMethod? paymentMethod;
  final String? creditCardNumber;
}
```

### Payment Method Enum

```dart
enum PaymentMethod {
  cash,
  creditCard,
  bankTransfer,
  other
}
```

### General Ledger Integration

When an expense is created, the system automatically generates corresponding general ledger entries:

1. A debit entry to the expense account (Account #5000), including the tax amount
2. A credit entry to the appropriate account based on the payment method:
   - Cash payments: Cash Account (#1000)
   - Credit Card payments: Credit Card Liability Account (#2000)
   - Bank Transfer payments: Bank Account (#1100)

These entries ensure proper double-entry accounting and maintain the financial integrity of the system.

## Database Implementation

The expense entity is stored in the `expenses` table in Supabase with the following columns:

| Column Name        | Type     | Description                            |
|--------------------|----------|----------------------------------------|
| expense_id         | int      | Primary key                            |
| vendor_id          | varchar  | Foreign key to vendors table           |
| expense_date       | datetime | When the expense occurred              |
| amount             | decimal  | Amount of the expense                  |
| currency_code      | varchar  | Currency code (e.g., USD, EUR)         |
| project_id         | int      | Optional foreign key to projects table |
| staff_id           | int      | Optional foreign key to staff table    |
| tax_amount         | decimal  | Amount of tax included in the expense  |
| company_id         | int      | Foreign key to companies table         |
| payment_method     | enum     | Cash, credit card, or bank transfer    |
| credit_card_number | varchar  | Credit card number if applicable       |

## Mock Data Implementation

In development mode, the application can use mock expense data without requiring a connection to the Supabase backend. Mock expenses are generated with the following characteristics:

- Each expense is associated with a mock vendor
- Expenses are distributed among different dates
- A mix of payment methods is used
- Some expenses are associated with projects and staff members
- Data is realistic enough for UI testing

The mock implementation is in the `_getMockExpenses()` method in `SupabaseDatabaseService`.

## Code Examples

### Getting Expenses

```dart
// Using the database service
final expenses = await _databaseService.getExpenses();

// Using the view model
final expenses = await _expenseViewModel.getExpenses();
```

### Creating a New Expense

```dart
final newExpense = Expense(
  expenseId: 0,  // Will be set by the database
  vendorId: 'V001',
  expenseDate: DateTime.now(),
  amount: 150.0,
  currencyCode: 'USD',
  projectId: 1,
  staffId: 2,
  taxAmount: 15.0,
  companyId: 1,
  paymentMethod: PaymentMethod.creditCard,
  creditCardNumber: 'XXXX-XXXX-XXXX-1234',
);

final createdExpense = await _databaseService.createExpense(newExpense);
```

### Creating Expense with General Ledger Entries

```dart
// Using the view model that creates both expense and ledger entries
final createdExpense = await _expenseViewModel.createExpense(newExpense);
```

### Updating an Expense

```dart
final updatedExpense = existingExpense.copyWith(
  amount: 200.0,
  taxAmount: 20.0,
);

await _databaseService.updateExpense(updatedExpense);
```

### Deleting an Expense

```dart
await _databaseService.deleteExpense(expenseId);
```

## General Ledger Account Reference

| Account Number | Description                  | Used For                 |
|----------------|------------------------------|--------------------------|
| 5000           | General Expense Account      | Expense debits           |
| 1000           | Cash Account                 | Cash payments            |
| 2000           | Credit Card Liability Account| Credit card payments     |
| 1100           | Bank Account                 | Bank transfer payments   |

## UI Components

The expense management UI consists of the following components:

1. **ExpensesScreen** - Displays a list of expenses with filtering options
2. **ExpenseEntryScreen** - Form for creating or editing expenses
3. **ExpenseDetailScreen** - Shows detailed information about an expense

## Best Practices

1. **Currency Handling**: Always store and display the currency code alongside monetary values
2. **Vendor Validation**: Ensure the vendor exists before creating an expense
3. **Date Formatting**: Use consistent date formatting throughout the UI
4. **Tax Calculation**: Provide helpers for calculating tax amounts
5. **Payment Method Safety**: Validate payment method details, especially for credit cards
6. **General Ledger Consistency**: Ensure debit and credit entries always balance

## Testing

The expense management module has both unit tests and integration tests:

- **Unit Tests**: Test individual methods for creating, updating, and deleting expenses
- **Integration Tests**: Test the full expense workflow from UI to database
- **Mock Mode Tests**: Test using mock data without a Supabase connection
- **General Ledger Tests**: Verify the correct ledger entries are created for expenses

For more information on testing, see [Conditional Testing](conditional_testing.md).

## Future Enhancements

Planned improvements to the expense management module include:

1. **Receipt Uploads**: Allow attaching digital receipts to expenses
2. **Expense Categories**: Add categorization for better reporting
3. **Recurring Expenses**: Support for regularly occurring expenses
4. **Approval Workflow**: Multi-step approval process for expenses
5. **Expense Reports**: Generate PDF expense reports 