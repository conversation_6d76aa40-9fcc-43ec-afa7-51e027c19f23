erDiagram
    Companies {
        int company_id PK
        varchar company_name UK
        text address
        varchar contact_person
    }
    UserCompanies {
        uuid user_id PK,FK
        int company_id PK,FK
        timestamp created_at
    }
    Projects {
        int project_id PK
        varchar project_code UK
        varchar project_name
        text description
        int company_id FK
    }
    StaffMembers {
        int staff_id PK
        varchar staff_name
        varchar email UK
        varchar phone
        varchar position
        varchar department
        int company_id FK
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    Currencies {
        varchar currency_code PK
        varchar currency_name
    }
    ExchangeRates {
        int rate_id PK
        varchar from_currency FK
        varchar to_currency FK
        decimal exchange_rate
        date effective_date
    }
    Accounts {
        varchar account_number PK
        varchar account_name
        int company_id FK
    }
    GeneralLedger {
        int ledger_id PK
        uuid transaction_id
        date transaction_date
        varchar account_number FK
        text description
        decimal debit
        decimal credit
        varchar currency_code FK
        int project_id FK
        int staff_id FK
        decimal tax_amount
        int company_id FK
    }
    Customers {
        varchar customer_id PK
        varchar customer_name
        varchar address
        varchar contact_person
        int company_id FK
    }
    Vendors {
        varchar vendor_id PK
        varchar vendor_name
        varchar vendor_number
        varchar organization_number
        boolean is_active
        varchar phone
        varchar email
        varchar address
        varchar zip_code
        varchar city
        varchar country
        varchar bank_account_type
        varchar bank_account_number
        varchar contact_person
        int company_id FK
    }
    VendorInvoices {
        int invoice_id PK
        varchar vendor_id FK
        varchar invoice_number
        date invoice_date
        date due_date
        decimal amount
        varchar currency_code FK
        decimal tax_amount
        int project_id FK
        int staff_id FK
        int company_id FK
        boolean is_paid
        timestamp created_at
        timestamp updated_at
    }
    VendorInvoiceLineItems {
        int line_item_id PK
        int invoice_id FK
        varchar account_number FK
        text description
        decimal amount
        decimal tax_amount
        int company_id FK
        timestamp created_at
        timestamp updated_at
    }
    Invoices {
        int invoice_id PK
        varchar customer_id FK
        date invoice_date
        date due_date
        decimal amount
        varchar currency_code FK
        int project_id FK
        int staff_id FK
        decimal tax_amount
        int company_id FK
    }
    Expenses {
        int expense_id PK
        uuid transaction_id
        varchar vendor_id FK
        date expense_date
        decimal amount
        varchar currency_code FK
        int project_id FK
        int staff_id FK
        decimal tax_amount
        int company_id FK
        enum payment_method
        varchar credit_card_number
    }
    PaymentsIn {
        int payment_in_id PK
        int invoice_id FK
        date payment_date
        decimal amount
        varchar currency_code FK
        int company_id FK
    }
    PaymentsOut {
        int payment_out_id PK
        int expense_id FK
        date payment_date
        decimal amount
        varchar currency_code FK
        int company_id FK
    }

    ExchangeRates ||--|| Currencies : "from_currency"
    ExchangeRates ||--|| Currencies : "to_currency"
    GeneralLedger ||--|| Currencies : "currency_code"
    GeneralLedger ||--|| Accounts : "account_number"
    GeneralLedger ||--o{ Projects : "project_id"
    GeneralLedger ||--o{ StaffMembers : "staff_id"
    Invoices ||--|| Currencies : "currency_code"
    Invoices ||--|| Customers : "customer_id"
    Invoices ||--o{ Projects : "project_id"
    Invoices ||--o{ StaffMembers : "staff_id"
    Expenses ||--|| Currencies : "currency_code"
    Expenses ||--|| Vendors : "vendor_id"
    Expenses ||--o{ Projects : "project_id"
    Expenses ||--o{ StaffMembers : "staff_id"
    PaymentsIn ||--|| Invoices : "invoice_id"
    PaymentsIn ||--|| Currencies : "currency_code"
    PaymentsOut ||--|| Expenses : "expense_id"
    PaymentsOut ||--|| Currencies : "currency_code"
    Projects ||--|| Companies : "company_id"
    StaffMembers ||--|| Companies : "company_id"
    GeneralLedger ||--|| Companies : "company_id"
    Invoices ||--|| Companies : "company_id"
    Expenses ||--|| Companies : "company_id"
    Customers ||--|| Companies : "company_id"
    Vendors ||--|| Companies : "company_id"
    PaymentsIn ||--|| Companies : "company_id"
    PaymentsOut ||--|| Companies : "company_id"
    Accounts ||--|| Companies : "company_id"
    VendorInvoices ||--|| Vendors : "vendor_id"
    VendorInvoices ||--|| Currencies : "currency_code"
    VendorInvoices ||--o{ Projects : "project_id"
    VendorInvoices ||--o{ StaffMembers : "staff_id"
    VendorInvoices ||--|| Companies : "company_id"
    VendorInvoiceLineItems ||--|| VendorInvoices : "invoice_id"
    VendorInvoiceLineItems ||--|| Accounts : "account_number"
    VendorInvoiceLineItems ||--|| Companies : "company_id"
    GeneralLedger ||--|| Expenses : "transaction_id"
    UserCompanies ||--|| Companies : "company_id"
    UserCompanies ||--|| auth.users : "user_id"
    