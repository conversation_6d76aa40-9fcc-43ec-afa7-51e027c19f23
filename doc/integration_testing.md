# Integration Testing Guidelines

This document outlines best practices for creating and running integration tests in the We Like Money application.

## Environment Setup

All integration tests should properly load environment variables from `.env.test` files. This ensures that tests have access to the necessary configuration while keeping sensitive information separate from the codebase.

### Loading Environment Variables

Every integration test should include code to load environment variables from various potential locations. Here's the recommended pattern:

```dart
import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:platform/platform.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    // Print directory for debugging
    final currentDir = Directory.current.path;
    debugPrint('📂 Current directory: $currentDir');

    // Try to load environment variables from multiple potential locations
    bool envLoaded = false;
    final searchLocations = [
      // Try specific paths first
      '.env.test',
      '../.env.test',
      '../../.env.test',
      '$currentDir/.env.test',
      '${Directory.current.parent.path}/.env.test',
      // Try to locate the file in platform-specific common locations
      if (const LocalPlatform().environment.containsKey('HOME'))
        '${const LocalPlatform().environment['HOME']}/projects/WeLikeMoney/we_like_money/.env.test',
    ];

    for (final location in searchLocations) {
      try {
        debugPrint('🔄 Loading environment variables from $location');
        await dotenv.load(fileName: location);
        debugPrint('✅ Loaded environment variables from $location');
        envLoaded = true;
        break;
      } catch (e) {
        // Continue to the next location
        continue;
      }
    }

    if (!envLoaded) {
      debugPrint('⚠️ Could not load .env.test file from any location');
      debugPrint('⚠️ App may not function correctly without environment variables');
    }
    
    // Initialize services if needed
    // await initializeServices();
  });

  // Your test groups and test cases go here
}
```

### Running Tests

Always use the `-d macos` flag when running integration tests to skip the device selection prompt:

```bash
flutter test -d macos integration_test/app_test.dart
```

Alternatively, use the provided convenience script:

```bash
./scripts/run_integration_tests.sh integration_test/app_test.dart
```

## Test Structure

### Setup

- Initialize necessary services in `setUpAll()`
- Create test data that is needed for multiple tests

### Teardown

- Clean up any test data created during tests in `tearDownAll()`
- Ensure the app is left in a clean state

### Test Cases

- Group related tests using `group()`
- Use descriptive test names
- Keep tests focused on a single functionality
- Follow the Arrange-Act-Assert pattern

## Best Practices

1. **Mock Mode**: Ensure tests can run in both mock mode and with the real Supabase client.

2. **Idempotent Tests**: Tests should be idempotent (can be run multiple times without side effects).

3. **Independent Tests**: Tests should not depend on the state left by other tests.

4. **Error Handling**: Test both happy paths and error cases.

5. **Clear Assertions**: Use specific assertions that clearly show what's being tested.

6. **Descriptive Finders**: Use descriptive finders to locate widgets, and if possible, use keys for more reliability.

## Handling Platform Differences

For platform-specific behavior, use conditional logic:

```dart
if (Platform.isMacOS) {
  // macOS-specific test code
} else if (Platform.isWindows) {
  // Windows-specific test code
}
```

## Troubleshooting

Common issues and solutions:

1. **Environment Variables Not Loading**: Check if `.env.test` exists in one of the search locations, and ensure it contains the required variables.

2. **Widget Not Found**: Use `debugDumpApp()` to see the current widget tree and verify your finders.

3. **Dependency Injection Errors**: Make sure dependencies are properly initialized before tests and not duplicated.

4. **Asynchronous Timing Issues**: Use `pumpAndSettle()` to wait for animations to complete or `pump(Duration(...))` for specific timing. 