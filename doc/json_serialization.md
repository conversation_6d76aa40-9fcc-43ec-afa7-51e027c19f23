# JSON Serialization

## Overview

This document explains the approach for handling JSON serialization and deserialization in the application, specifically addressing challenges with converting between snake_case (database) and camelCase (Dart) naming conventions.

## Challenges Addressed

1. **Inconsistent Naming Conventions**: 
   - Database fields use snake_case (`user_id`, `first_name`)
   - Dart code uses camelCase (`userId`, `firstName`)

2. **Custom Serialization Needs**:
   - Handling missing fields with default values
   - Supporting both naming conventions for flexibility
   - Consistent handling of DateTime fields

3. **Special Cases for Different Models**:
   - Custom serialization for Company and ExchangeRate models
   - Preventing duplicate serialization logic

## Solution

### 1. JsonSerializationUtil

We created a utility class `JsonSerializationUtil` that provides consistent methods for converting between naming conventions:

```dart
class JsonSerializationUtil {
  /// Converts a camelCase key to snake_case
  static String camelToSnake(String input) { ... }

  /// Converts a snake_case key to camelCase
  static String snakeToCamel(String input) { ... }

  /// Converts an entire Map from camelCase keys to snake_case keys
  static Map<String, dynamic> convertMapKeysToSnakeCase(Map<String, dynamic> input) { ... }

  /// Converts an entire Map from snake_case keys to camelCase keys
  static Map<String, dynamic> convertMapKeysToCamelCase(Map<String, dynamic> input) { ... }
}
```

### 2. Model Class Pattern

We standardized the model class pattern to include:

1. **Custom `fromJson` Method**: Handles both snake_case and camelCase keys, provides defaults for missing fields.

2. **Static `toJson` Method**: Ensures consistent snake_case output for database operations.

Example pattern:

```dart
@freezed
abstract class SomeModel with _$SomeModel {
  const factory SomeModel({
    @JsonKey(name: 'model_id') required int modelId,
    @JsonKey(name: 'model_name') required String modelName,
  }) = _SomeModel;

  // Custom fromJson
  factory SomeModel.fromJson(Map<String, dynamic> json) {
    final workingJson = Map<String, dynamic>.from(json);
    
    // Handle field mapping and defaults
    if (workingJson['model_id'] == null && workingJson['modelId'] != null) {
      workingJson['model_id'] = workingJson['modelId'];
    } else if (workingJson['model_id'] == null) {
      workingJson['model_id'] = 0;
    }
    
    return _$SomeModelFromJson(workingJson);
  }
  
  // Custom toJson
  static Map<String, dynamic> toJson(SomeModel model) {
    final result = <String, dynamic>{};
    
    result['model_id'] = model.modelId;
    result['model_name'] = model.modelName;
    
    return result;
  }
}
```

### 3. Database Service Integration

The SupabaseDatabaseService was updated to:

1. Use model-specific `toJson` methods for consistent output
2. Use model-specific `fromJson` methods for consistent input handling
3. Remove duplicate serialization logic

```dart
// Helper methods in the SupabaseDatabaseService
Map<String, dynamic> _convertSnakeToCamel(Map<String, dynamic> snake) {
  return JsonSerializationUtil.convertMapKeysToCamelCase(snake);
}

Map<String, dynamic> _convertCamelToSnake(Map<String, dynamic> camel) {
  return JsonSerializationUtil.convertMapKeysToSnakeCase(camel);
}
```

## Implementation for Specific Models

### Company Model

The Company model was updated to include a static `toJson` method:

```dart
static Map<String, dynamic> toJson(Company company) {
  final result = <String, dynamic>{};
  
  result['company_id'] = company.companyId;
  result['company_name'] = company.companyName;
  if (company.address != null) {
    result['address'] = company.address;
  }
  if (company.contactPerson != null) {
    result['contact_person'] = company.contactPerson;
  }
  
  return result;
}
```

### ExchangeRate Model

The ExchangeRate model was updated with a static `toJson` method that properly handles DateTime fields:

```dart
static Map<String, dynamic> toJson(ExchangeRate rate) {
  final result = <String, dynamic>{};
  
  result['rate_id'] = rate.rateId;
  result['from_currency'] = rate.fromCurrency;
  result['to_currency'] = rate.toCurrency;
  result['exchange_rate'] = rate.exchangeRate;
  
  // Handle DateTime fields properly
  if (rate.effectiveDate != null) {
    result['effective_date'] = rate.effectiveDate.toIso8601String();
  }
  
  return result;
}
```

## Model Generator Template

To ensure consistency across all models, we created a model generator template file (`model_generator.dart`) that serves as a reference for creating new model classes with the standardized approach.

## Testing

The serialization approach is thoroughly tested with:

1. Unit tests for the `JsonSerializationUtil` class
2. Integration tests for model serialization/deserialization
3. Mock mode testing for database operations

## Best Practices

1. **Always use model-specific `toJson` methods** when sending data to the database
2. **Always use model-specific `fromJson` methods** when receiving data from the database
3. **Include defaults for required fields** to prevent runtime errors
4. **Handle both snake_case and camelCase keys** for flexibility
5. **Properly serialize DateTime fields** to ISO 8601 strings

## Future Improvements

1. **Code Generation**: Consider using code generation to automate the creation of serialization methods
2. **Database Schema Validation**: Implement validation against the database schema
3. **Error Handling**: Add more robust error handling for serialization edge cases 