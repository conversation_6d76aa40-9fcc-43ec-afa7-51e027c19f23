# We Like Money - Architecture Documentation

## Overview

We Like Money is an accounting package built with Flutter. This document outlines the architecture, design patterns, and key components of the application.

## Architecture

The application follows a layered architecture with clear separation of concerns:

1. **Presentation Layer** - UI components and screens
2. **Business Logic Layer** - ViewModels and business rules
3. **Data Layer** - Services and repositories
4. **Infrastructure Layer** - Configuration and utilities

### Dependency Flow

```
UI → ViewModels → Services → External APIs/Database
```

## Dependency Injection

The application uses `get_it` and `injectable` for dependency injection:

- **Service Registration**: Services are registered with the DI container using the `@injectable` annotation
- **Singleton Services**: Core services like `ErrorHandler` and `Logger` are registered as singletons
- **Interface Binding**: Services are bound to their interfaces using `@Injectable(as: Interface)`

Example:
```dart
@Injectable(as: DatabaseService)
class SupabaseDatabaseService implements DatabaseService {
  // Implementation
}
```

## Error Handling

The application implements a comprehensive error handling strategy:

### ErrorHandler

A centralized `ErrorHandler` class that:
- Logs errors using the injected `Logger`
- Transforms technical errors into user-friendly messages
- Handles specific error types (Supabase, Network, etc.)
- Provides different messages for debug vs. production

### Error Propagation

Errors flow through the application layers:
1. **Service Layer**: Catches technical errors, transforms them using `ErrorHandler`
2. **ViewModel Layer**: Catches service errors, transforms them into business exceptions
3. **UI Layer**: Displays user-friendly error messages and provides retry functionality

### Custom Exceptions

- `BusinessException`: Domain-specific errors with user-friendly messages
- `NetworkException`: Network-related errors

## Database Service

The application uses Supabase as its backend service:

### Interface

`DatabaseService` defines a contract for all database operations:
- CRUD operations for all entity types
- Type-safe methods with proper error handling

### Implementation

`SupabaseDatabaseService` implements the database operations using Supabase:
- Uses the `_executeDbOperation` helper for consistent error handling
- Transforms Supabase responses into domain models
- Handles Supabase-specific errors

## ViewModels

ViewModels serve as intermediaries between UI and services:

- Expose data and operations to the UI
- Handle business logic and validation
- Transform service exceptions into business exceptions
- Provide a clean API for UI components

Example:
```dart
@injectable
class AccountViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;

  AccountViewModel(this._databaseService, this._errorHandler);

  Future<List<Account>> getAccounts() async {
    try {
      return await _databaseService.getAccounts();
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }
}
```

## State Management

The application uses Riverpod for state management:

### Providers

- **Provider**: Basic provider for exposing services and functions
- **FutureProvider**: For asynchronous operations that return a future
- **StateProvider**: For simple state that can be mutated
- **StateNotifierProvider**: For complex state with a defined API

Example:
```dart
// Provider for the ViewModel
final projectViewModelProvider = Provider<ProjectViewModel>((ref) {
  return getIt<ProjectViewModel>();
});

// FutureProvider for fetching data
final projectsProvider = FutureProvider<List<Project>>((ref) async {
  final viewModel = ref.watch(projectViewModelProvider);
  return viewModel.getProjects();
});
```

### AsyncValue Pattern

UI components use the AsyncValue pattern to handle loading, error, and data states:

```dart
// In a ConsumerWidget
final projectsAsync = ref.watch(projectsProvider);

return projectsAsync.when(
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => ErrorDisplay(error: error),
  data: (projects) => ProjectsList(projects: projects),
);
```

## UI Components

The UI layer consists of screens and widgets that:

- Use dependency injection to access ViewModels
- Handle loading, error, and success states
- Provide user feedback and error messages
- Implement retry functionality for failed operations

Example:
```dart
class _AccountsScreenState extends State<AccountsScreen> {
  final AccountViewModel _viewModel = getIt<AccountViewModel>();
  
  // State management
  List<Account> _accounts = [];
  bool _isLoading = true;
  String? _error;

  // UI implementation with loading, error, and data states
}
```

## Configuration

### Environment Configuration

The application uses environment variables for configuration:
- Supabase URL and API keys
- Other environment-specific settings

### Logger Configuration

A centralized logger configuration that:
- Uses different log levels for debug vs. production
- Formats log output for readability
- Captures errors and stack traces

## Best Practices

The codebase follows these best practices:

1. **Separation of Concerns**: Clear boundaries between layers
2. **Dependency Injection**: Loose coupling between components
3. **Error Handling**: Comprehensive error handling strategy
4. **Testability**: Components designed for easy testing
5. **Documentation**: Code and architecture documentation 