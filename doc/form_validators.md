# Input Validation Guide

## Introduction

The input validation system in this project has been consolidated to provide a unified and consistent approach. The primary way to access all validation logic is now through the static methods available on the `Validator` class, located at `lib/utils/validation/validator.dart`.

This system is built around `ValidationRule<String>` objects. Each validation method on the `Validator` class (e.g., `Validator.required()`, `Validator.email()`) returns a `ValidationRule<String>`. To use these rules with <PERSON>lut<PERSON>'s form widgets (like `TextFormField`), you need to convert the `ValidationRule<String>` into a Flutter-compatible validator function (`String? Function(String?)`). This is done using the `Validator.createValidator(rule)` method.

## Core Concept: Rules and Flutter Validators

The process of using a validator typically involves two steps:

1.  **Get a `ValidationRule<String>`:**
    You obtain a specific validation rule by calling a method on the `Validator` class.
    ```dart
    // Example: Get a 'required' rule
    final requiredRule = Validator.required('This field cannot be empty');

    // Example: Get an 'email' rule
    final emailRule = Validator.email('Please enter a valid email');
    ```

2.  **Create a Flutter-compatible validator function:**
    Use `Validator.createValidator(rule)` to convert the rule into a function that Flutter forms can use.
    ```dart
    final flutterValidatorForRequired = Validator.createValidator(requiredRule);
    // Use flutterValidatorForRequired in TextFormField's validator property
    ```

### Combining Multiple Rules

You can easily combine multiple validation rules into a single rule using `Validator.all([...])`. This is useful for applying several checks to one field.
```dart
final combinedRule = Validator.all([
  Validator.required('Email is required'),
  Validator.email('Invalid email format'),
  Validator.minLength(5, 'Email must be at least 5 characters'),
]);

final flutterValidatorForEmail = Validator.createValidator(combinedRule);
// This validator will check for required, email format, and minLength.
```
The `Validator.all` method executes rules sequentially and returns the first error message encountered, or `null` if all rules pass.

## Using with Flutter Forms

Here's an example of how to use the `Validator` class with a `TextFormField` in Flutter:

```dart
import 'package:we_like_money/utils/validation/validator.dart'; // Adjust path if necessary

// Inside your widget build method:
TextFormField(
  decoration: InputDecoration(labelText: 'Email Address'),
  autovalidateMode: AutovalidateMode.onUserInteraction, // Or other mode
  validator: Validator.createValidator(
    Validator.all([
      Validator.required('Email is required'),
      Validator.email('Invalid email format, please check.'),
    ])
  ),
  // other properties like controller, onSaved, etc.
)
```

## Available Validator Methods

The `Validator` class provides a comprehensive set of static methods to create validation rules. These are grouped below for clarity. For detailed parameter information, please refer to the source code of `lib/utils/validation/validator.dart` or the specific underlying validator classes (`BaseValidator`, `DateValidator`, etc.).

### Base Validators
These are common, general-purpose validation rules.

| Method                      | Description                                                   |
|-----------------------------|---------------------------------------------------------------|
| `Validator.required()`      | Checks if a field is not empty.                               |
| `Validator.email()`         | Validates email format (e.g., `<EMAIL>`).            |
| `Validator.minLength()`     | Ensures the input meets a minimum character length.           |
| `Validator.maxLength()`     | Ensures the input does not exceed a maximum character length. |
| `Validator.numeric()`       | Validates if the input is a valid numeric value.              |
| `Validator.pattern()`       | Validates the input against a custom regular expression.      |
| `Validator.password()`      | Validates password complexity (length, case, numbers, special).|
| `Validator.moneyAmount()`   | Validates monetary amounts, handling common currency symbols. |
| `Validator.matches()`       | Checks if the input matches the value from another source.    |
| `Validator.numericRange()`  | Validates if a numeric input falls within a min/max range.    |

### Date Validators
These rules are specific to date validation.

| Method                      | Description                                                     |
|-----------------------------|-----------------------------------------------------------------|
| `Validator.dateFormat()`    | Checks if the date string matches a specified format.           |
| `Validator.dateAfter()`     | Ensures a date is after a specified minimum date.               |
| `Validator.dateBefore()`    | Ensures a date is before a specified maximum date.              |
| `Validator.dateBetween()`   | Ensures a date falls between a minimum and maximum date.        |
| `Validator.dateNotWeekend()`| Checks if a date is not a Saturday or Sunday.                   |
| `Validator.businessDay()`   | Checks if a date is a business day (not weekend or holiday).    |

### Currency Validators
These rules are specific to currency-related fields.

| Method                                  | Description                                                          |
|-----------------------------------------|----------------------------------------------------------------------|
| `Validator.currencyCode()`              | Validates ISO 4217 currency codes (e.g., "USD", "EUR").              |
| `Validator.currencyName()`              | Validates the name of a currency.                                    |
| `Validator.exchangeRate()`              | Validates numeric exchange rates (positive, optional min/max).       |
| `Validator.targetCurrencyNotSameAsBase()`| Ensures a target currency is different from a base currency.       |
| `Validator.currencySymbol()`            | Validates currency symbols.                                          |
| `Validator.decimalPlaces()`             | Validates the number of decimal places (e.g., for currency config).  |

### Invoice Validators
These rules are specific to invoice-related fields.

| Method                          | Description                                                          |
|---------------------------------|----------------------------------------------------------------------|
| `Validator.invoiceNumber()`     | Validates invoice number format.                                     |
| `Validator.invoiceDueDate()`    | Validates due dates relative to an invoice date (min/max days).      |
| `Validator.invoiceAmount()`     | Validates invoice amounts (min/max, zero allowance).                 |
| `Validator.taxAmount()`         | Validates tax amounts, often as a percentage of an invoice total.    |
| `Validator.vendorSelected()`    | Ensures a vendor has been selected (typically for dropdowns/ID fields).|
| `Validator.accountSelected()`   | Ensures an account has been selected (typically for dropdowns/ID fields).|

## Deprecation Note

The older validator classes previously located in `lib/utils/` (such as `FormValidators`, `DateValidators`, `CurrencyValidators`, and `InvoiceValidators`) have been removed. All their functionalities, and more, are now centralized and accessible via the `Validator` class found in `lib/utils/validation/validator.dart`. Please update any existing code to use this new unified API.
