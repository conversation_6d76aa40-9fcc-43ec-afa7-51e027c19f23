# Error Handling in We Like Money

## Overview

This document describes the error handling strategy implemented in the We Like Money application. The goal is to provide a consistent, user-friendly approach to error handling across all layers of the application.

## Components

### ErrorHandler Class

The `ErrorHandler` class is the central component of our error handling strategy:

```dart
@singleton
class ErrorHandler {
  final Logger _logger;

  ErrorHandler(this._logger);

  String handleError(dynamic error) {
    // Log the error
    _logger.e('Error occurred', error: error, stackTrace: StackTrace.current);

    // Handle specific error types
    if (error is PostgrestException) {
      return _handleSupabaseError(error);
    } else if (error is NetworkException) {
      return 'Network error. Please check your connection and try again.';
    } else {
      // Generic error handling
      return kDebugMode
          ? error.toString()
          : 'An unexpected error occurred. Please try again later.';
    }
  }

  String _handleSupabaseError(PostgrestException error) {
    // Specific Supabase error handling
    // ...
  }
}
```

Key features:
- Centralized error handling logic
- Dependency injection for testability
- Error logging with stack traces
- Different messages for debug vs. production environments
- Specific handling for different error types

### Custom Exceptions

We define custom exceptions to represent different types of errors:

```dart
/// Custom exception for network-related errors
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);

  @override
  String toString() => message;
}

/// Custom exception for business logic errors
class BusinessException implements Exception {
  final String message;
  BusinessException(this.message);

  @override
  String toString() => message;
}
```

These exceptions:
- Provide clear error categorization
- Carry user-friendly messages
- Allow for specific handling in UI components

## Error Handling Layers

### Service Layer

The service layer (e.g., `SupabaseDatabaseService`) handles technical errors and transforms them into domain exceptions:

```dart
Future<T> _executeDbOperation<T>(Future<T> Function() operation) async {
  try {
    return await operation();
  } on PostgrestException catch (e) {
    final errorMessage = _errorHandler.handleError(e);
    throw BusinessException(errorMessage);
  } catch (e) {
    final errorMessage = _errorHandler.handleError(e);
    throw BusinessException(errorMessage);
  }
}
```

This approach:
- Centralizes error handling logic in services
- Transforms technical errors into domain exceptions
- Ensures consistent error messages
- Logs all errors for debugging

### ViewModel Layer

ViewModels catch service exceptions and transform them into business exceptions:

```dart
Future<List<Account>> getAccounts() async {
  try {
    return await _databaseService.getAccounts();
  } catch (e) {
    final errorMessage = _errorHandler.handleError(e);
    throw BusinessException(errorMessage);
  }
}
```

This layer:
- Provides a clean API for UI components
- Ensures consistent error handling
- Transforms any remaining technical errors into business exceptions

### UI Layer

UI components handle business exceptions and display user-friendly messages:

```dart
Future<void> _loadAccounts() async {
  setState(() {
    _isLoading = true;
    _error = null;
  });
  
  try {
    final accounts = await _viewModel.getAccounts();
    setState(() {
      _accounts = accounts;
      _isLoading = false;
    });
  } on BusinessException catch (e) {
    setState(() {
      _error = e.message;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _error = 'An unexpected error occurred';
      _isLoading = false;
    });
  }
}
```

The UI layer:
- Handles loading, error, and success states
- Displays user-friendly error messages
- Provides retry functionality
- Maintains a consistent user experience

## Logging

Error logging is integrated with the error handling strategy:

```dart
@module
abstract class LoggerModule {
  @singleton
  Logger get logger => Logger(
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          printTime: true,
        ),
        level: kDebugMode ? Level.debug : Level.error,
      );
}
```

The logging configuration:
- Uses different log levels for debug vs. production
- Captures method calls and stack traces
- Formats logs for readability
- Integrates with the error handling system

## Best Practices

1. **Never swallow exceptions** without proper handling
2. **Always log errors** with stack traces for debugging
3. **Provide user-friendly messages** that guide users on next steps
4. **Include retry functionality** where appropriate
5. **Use different messages** for debug vs. production environments
6. **Categorize errors** to allow for specific handling

## Error Categories

| Error Type | Description | Handling Strategy |
|------------|-------------|-------------------|
| Network Errors | Connection issues, timeouts | Retry with exponential backoff |
| Authentication Errors | Invalid credentials, expired tokens | Redirect to login |
| Authorization Errors | Insufficient permissions | Show permission error |
| Validation Errors | Invalid input data | Show field-specific errors |
| Resource Errors | Not found, already exists | Show specific message |
| Server Errors | Internal server errors | Show generic message, retry option |
| Client Errors | Application bugs | Log for debugging, show generic message |

## Implementation Examples

### Handling Supabase Errors

```dart
String _handleSupabaseError(PostgrestException error) {
  if (error.code == 'PGRST116') {
    return 'Resource not found.';
  } else if (error.code == 'PGRST109') {
    return 'Database conflict. The operation could not be completed.';
  } else if (error.code == '23505') {
    return 'A record with this information already exists.';
  } else if (error.code == '23503') {
    return 'This operation would violate database constraints.';
  } else if (error.message.contains('JWT')) {
    return 'Your session has expired. Please log in again.';
  } else {
    return kDebugMode
        ? 'Database error: ${error.message} (Code: ${error.code})'
        : 'A database error occurred. Please try again later.';
  }
}
```

### UI Error Handling

```dart
body: _isLoading
    ? const Center(child: CircularProgressIndicator())
    : _error != null
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: $_error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadAccounts,
                  child: const Text('Retry'),
                ),
              ],
            ),
          )
        : // Success state UI
``` 