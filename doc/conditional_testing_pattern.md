# Conditional Testing Pattern

This document explains how to use the conditional testing pattern to write tests that can run with both mock data and real data connections in the WeLikeMoney app.

## Overview

The conditional testing pattern allows us to:

1. Write tests that can run with mock data (offline mode) for fast CI/CD pipelines
2. Run the same tests with real database connections (online mode) for integration testing
3. Maintain a single test suite instead of separate unit and integration tests

## How It Works

The key components of this pattern are:

- **TestConfig**: A singleton class that loads environment settings and determines whether tests should run in online or offline mode.
- **Conditional Service Initialization**: Tests initialize either mock services or real services based on the test configuration.
- **Conditional Assertions**: Tests contain assertions that vary based on whether we're testing with mock or real data.

## Implementation

### Step 1: Use TestConfig to determine the test mode

```dart
setUpAll(() async {
  await TestConfig.instance.initialize();
  isOnlineTest = TestConfig.instance.isOnlineTest;
});
```

### Step 2: Initialize services conditionally

```dart
setUp(() {
  if (isOnlineTest) {
    // Initialize real service
    final logger = Logger();
    final errorHandler = ErrorHandler(logger);
    final supabaseClientProvider = SupabaseClientProvider();
    databaseService = SupabaseDatabaseService(
      supabaseClientProvider, 
      errorHandler
    );
  } else {
    // Initialize mock service
    mockDatabaseService = MockDatabaseService();
    databaseService = mockDatabaseService;
  }
});
```

### Step 3: Write tests with conditional expectations

```dart
test('getAccounts returns list of accounts', () async {
  if (!isOnlineTest) {
    // Set up mock expectations
    when(mockDatabaseService.getAccounts())
        .thenAnswer((_) async => [testAccount]);
  }

  // Act - same for both modes
  final accounts = await databaseService.getAccounts();

  // Assert - different for each mode
  if (!isOnlineTest) {
    // Mock data assertions
    expect(accounts, [testAccount]);
    verify(mockDatabaseService.getAccounts()).called(1);
  } else {
    // Real data assertions
    expect(accounts, isA<List<Account>>());
    expect(accounts, isNotEmpty);
  }
});
```

## Example Implementation

Below is a complete example of a test file using the conditional testing pattern:

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

import 'package:we_like_money/services/database_service.dart';
import 'package:we_like_money/services/supabase_database_service.dart';
import '../config/test_config.dart';
import '../mocks/mock_supabase.dart';

@GenerateMocks([DatabaseService])
void main() {
  late DatabaseService databaseService;
  late MockDatabaseService mockDatabaseService;
  late bool isOnlineTest;

  setUpAll(() async {
    await TestConfig.instance.initialize();
    isOnlineTest = TestConfig.instance.isOnlineTest;
    debugPrint('Running tests in ${isOnlineTest ? "ONLINE" : "OFFLINE"} mode');
  });

  setUp(() {
    if (isOnlineTest) {
      // Setup real database service
      final logger = Logger();
      final errorHandler = ErrorHandler(logger);
      final mockClientProvider = MockSupabaseClientProvider(null);
      databaseService = SupabaseDatabaseService(mockClientProvider, errorHandler);
    } else {
      // Setup mock database service
      mockDatabaseService = MockDatabaseService();
      databaseService = mockDatabaseService;
    }
  });

  group('General Ledger Operations', () {
    test('getAllGeneralLedgerEntries returns entries', () async {
      if (!isOnlineTest) {
        // Mock setup for offline mode
        when(mockDatabaseService.getAllGeneralLedgerEntries())
            .thenAnswer((_) async => [mockEntry1, mockEntry2]);
      }

      // Act
      final entries = await databaseService.getAllGeneralLedgerEntries();

      // Assert
      if (!isOnlineTest) {
        // Offline mode assertions
        expect(entries.length, 2);
        verify(mockDatabaseService.getAllGeneralLedgerEntries()).called(1);
      } else {
        // Online mode assertions
        expect(entries, isNotNull);
        expect(entries, isA<List<GeneralLedgerEntry>>());
      }
    });

    // More tests...
  });
}
```

## Running Tests in Different Modes

### Offline Mode (Default)

```bash
flutter test
```

### Online Mode

```bash
ONLINE_TEST=true flutter test
```

### Specific Tests in Online Mode

```bash
ONLINE_TEST=true flutter test test/services/supabase_database_service_test.dart
```

## Best Practices

1. Always ensure tests can run in offline mode by default
2. Clean up data created during online tests
3. Use descriptive logging to indicate which mode tests are running in
4. Make assertions appropriate for each mode
5. Design tests to handle potential differences in data between mock and real environments 