# Dependency Injection

## Overview

This document describes the dependency injection (DI) system used in the We Like Money application. Dependency injection is a design pattern that allows for loose coupling between components, making the code more maintainable and testable.

## Libraries

The application uses the following libraries for dependency injection:

- **get_it**: A service locator for Dart and Flutter projects
- **injectable**: A code generation package that makes it easy to register dependencies

## Setup

### Configuration

The DI system is configured in `lib/di/injection.dart`:

```dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'injection.config.dart';

final GetIt getIt = GetIt.instance;

@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: false,
)
Future<void> configureDependencies() async {
  await init(getIt);
}
```

This setup:
- Creates a global `getIt` instance for accessing dependencies
- Uses `@InjectableInit` to configure code generation
- Provides an async `configureDependencies` function for initialization

### Initialization

The DI system is initialized during app startup in `main.dart`:

```dart
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  await dotenv.dotenv.load();
  
  // Initialize Supabase
  await initializeSupabase();
  
  // Initialize dependency injection
  await configureDependencies();
  
  runApp(const App());
}
```

This ensures that all dependencies are properly initialized before the app starts.

## Registration Types

### Singleton

Singletons are registered using the `@singleton` annotation:

```dart
@singleton
class ErrorHandler {
  final Logger _logger;
  ErrorHandler(this._logger);
  // ...
}
```

This creates a single instance that is reused throughout the application.

### Factory

Factories are registered using the `@injectable` annotation:

```dart
@injectable
class AccountViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;
  
  AccountViewModel(this._databaseService, this._errorHandler);
  // ...
}
```

This creates a new instance each time the dependency is requested.

### Interface Binding

Services can be bound to interfaces using the `@Injectable(as: Interface)` annotation:

```dart
@Injectable(as: DatabaseService)
class SupabaseDatabaseService implements DatabaseService {
  // ...
}
```

This allows for dependency inversion, where high-level modules depend on abstractions rather than concrete implementations.

### Provider Classes

Provider classes are used to create and provide instances of external dependencies:

```dart
@singleton
@injectable
class SupabaseClientProvider {
  SupabaseClient getClient() {
    return Supabase.instance.client;
  }
}
```

This approach:
- Creates a singleton provider class
- Provides a method to get the dependency
- Allows for lazy initialization
- Simplifies testing with mocks

### Module Registration

Modules are used to register external dependencies or complex configurations:

```dart
@module
abstract class LoggerModule {
  @singleton
  Logger get logger => Logger(
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          printTime: true,
        ),
        level: kDebugMode ? Level.debug : Level.error,
      );
}
```

This approach:
- Uses the `@module` annotation to define a module
- Registers dependencies using property getters
- Allows for complex initialization logic

## Usage

### In UI Components

Dependencies can be accessed in UI components using the global `getIt` instance:

```dart
class _AccountsScreenState extends State<AccountsScreen> {
  final AccountViewModel _viewModel = getIt<AccountViewModel>();
  
  // ...
}
```

### In Services

Dependencies can be injected into services using constructor injection:

```dart
@Injectable(as: DatabaseService)
class SupabaseDatabaseService implements DatabaseService {
  final SupabaseClient _client;
  final ErrorHandler _errorHandler;

  SupabaseDatabaseService(SupabaseClientProvider clientProvider, this._errorHandler)
      : _client = clientProvider.getClient();
  
  // ...
}
```

### In ViewModels

ViewModels can receive dependencies through constructor injection:

```dart
@injectable
class AccountViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;

  AccountViewModel(this._databaseService, this._errorHandler);
  
  // ...
}
```

## Testing

The DI system makes testing easier by allowing dependencies to be mocked:

```dart
void main() {
  late MockDatabaseService mockDatabaseService;
  late AccountViewModel viewModel;
  late MockErrorHandler mockErrorHandler;

  setUp(() {
    mockDatabaseService = MockDatabaseService();
    mockErrorHandler = MockErrorHandler();
    viewModel = AccountViewModel(mockDatabaseService, mockErrorHandler);
  });

  test('getAccounts returns list of accounts', () async {
    // Arrange
    when(mockDatabaseService.getAccounts())
        .thenAnswer((_) async => [testAccount]);

    // Act
    final result = await viewModel.getAccounts();

    // Assert
    expect(result, [testAccount]);
    verify(mockDatabaseService.getAccounts()).called(1);
  });
}
```

## Best Practices

1. **Use Constructor Injection** for better testability
2. **Register Services as Interfaces** for loose coupling
3. **Use Singletons Sparingly** for stateful services
4. **Keep Registration Simple** by using annotations
5. **Use Provider Classes** for external dependencies
6. **Document Dependencies** for clarity
7. **Test with Mocks** for isolation

## Dependency Graph

The application has the following high-level dependency graph:

```
UI Components
    ↓
ViewModels
    ↓
Services (Interfaces)
    ↓
Service Implementations
    ↓
External Dependencies (Supabase, Logger, etc.)
```

This layered approach:
- Promotes separation of concerns
- Enables easy testing
- Allows for flexible implementation changes
- Reduces coupling between components 