# Database Connection Modes

## Overview

We Like Money supports multiple database connection modes to facilitate development, testing, and production usage. You can easily switch between using mock data and connecting to a remote Supabase database.

## Connection Modes

The application supports three connection modes:

1. **Automatic Mode (Default)**
   - In debug builds, automatically uses mock data
   - In release builds, automatically connects to the remote database
   - Ideal for most development workflows

2. **Mock Data Only**
   - Always uses mock data, regardless of build type
   - No connection to remote database is attempted
   - Useful for offline development, UI testing, or demo purposes
   - Data changes are not persisted beyond the application session

3. **Remote Database Only**
   - Always attempts to connect to the remote Supabase database
   - If connection fails, the app will show appropriate error messages
   - Useful for testing real database connections and data persistence

## Changing Connection Mode

You can change the database connection mode via the Settings screen:

1. Navigate to the Settings screen (click the gear icon in the app bar or use the Settings button on the home screen)
2. Under "Database Settings", select your preferred connection mode
3. Confirm the change when prompted
4. The new connection mode will take effect immediately for new connections

## Connection Status Indicator

The current connection mode is displayed in the app bar:

- **Green Badge**: Connected to remote database
- **Yellow Badge**: Using mock data

The indicator helps you quickly identify which mode you're currently using.

## Technical Implementation

The connection mode is managed by the `AppConfig` class and is persisted using shared preferences. When the app starts, it loads the saved connection mode from preferences, defaulting to "Automatic" if none is found.

### How Mock Mode Works

Mock mode provides pre-defined test data in memory, including:
- Sample companies
- Sample projects
- Sample staff members
- Sample currencies
- Sample vendors
- Sample expenses
- Other accounting data (coming soon)

This data is reset each time the application restarts and changes are not persisted.

### Development Notes

- The `SupabaseDatabaseService` automatically returns mock data when in mock mode
- Generic mock data is defined in the `_getMockData<T>()` method of the service
- Expense-specific mock data is defined in the `_getMockExpenses()` method
- Each mock entity is designed to reference other mock entities (e.g., expenses reference vendors)
- You can expand the mock data by adding more sample entities to these methods
- The status indicator in the UI obtains the current mode from `AppConfig().shouldUseMockMode()`

## Troubleshooting

If you experience issues with connection modes:

1. **Cannot Connect to Remote Database**
   - Verify your Supabase credentials in the `.env` file
   - Check your internet connection
   - Try switching to Mock Mode temporarily

2. **Connection Mode Not Persisting**
   - This could indicate an issue with shared preferences
   - Try clearing app data or reinstalling the app

3. **App Crashes in Remote-Only Mode**
   - This is expected behavior if there's a connection error in Remote-Only mode
   - Switch to Automatic or Mock-Only mode for more resilient behavior

## Development Workflow

### Git Pre-Commit Hooks

The project uses Git pre-commit hooks to ensure code quality before commits are made:

1. **Automatic Linting**: The pre-commit hook runs `flutter analyze` to check for any linting issues
2. **Automatic Testing**: The pre-commit hook runs `flutter test` to ensure all tests pass

These hooks help maintain code quality by preventing commits that would introduce linting errors or failing tests.

To bypass these hooks in exceptional circumstances (not recommended for normal development):

```bash
git commit -m "Your commit message" --no-verify
```

### Adding New Features

When implementing new features:

1. Always add appropriate mock data implementations in the `_getMockData<T>()` method
2. Write unit tests for both mock mode and real database mode
3. Run tests locally before committing
4. Ensure all linting issues are fixed

### Testing Considerations

- Tests are configured to run in both online and offline modes
- By default, tests use mock implementations (offline mode)
- See `doc/conditional_testing.md` for details on conditional testing
- Mock data for testing should be consistent with the mock data used in development mode 