# We Like Money Documentation

This directory contains comprehensive documentation for the We Like Money accounting application.

## Documentation

This directory contains technical documentation for the We Like Money application:

- [Architecture](architecture.md) - Overall application architecture and design patterns
- [Dependency Injection](dependency_injection.md) - How dependency injection is implemented
- [Error Handling](error_handling.md) - Error handling strategies and implementation
- [Supabase Integration](supabase_integration.md) - Details about the Supabase backend integration
- [Configuration Setup](configuration_setup.md) - Environment configuration and network entitlements
- [Mock Mode](mock_mode.md) - Using the application in mock or remote database mode
- [Conditional Testing](conditional_testing.md) - How tests adapt to different environments
- [CRUD Operations](company_crud.md) - Implementation of CRUD operations for companies
- [Project Management](project_crud.md) - Implementation of project management features
- [Expense Management](expense_management.md) - Implementation of expense tracking and reporting
- [Integration Testing](integration_testing.md) - Guidelines for creating and running integration tests
- [Form Validation](form_validators.md) - Form validation utilities and best practices

### Diagrams

- [ER Diagram](er_diagram.txt) - Entity-relationship diagram in text format
- [Component Diagram](component_diagram.txt) - Component diagram in text format
- [Deployment Diagram](deployment_diagram.txt) - Deployment diagram in text format
- [Use Case Diagram](use_case_diagram.txt) - Use case diagram in text format

## Testing

### Conditional Testing with Supabase

The WeLikeMoney application supports conditional testing, which allows tests to run both with and without a remote Supabase instance. This approach provides flexibility and ensures that tests can be run in different environments.

For more information, see [Conditional Testing](conditional_testing.md).

To run tests with different configurations:

```bash
# Run tests in offline mode (with mocks)
./scripts/run_tests.sh --offline

# Run tests in online mode (against Supabase)
./scripts/run_tests.sh --online

# Run tests in both modes
./scripts/run_tests.sh --all
```

## Documentation Guidelines

When adding new code or features to the project, please follow these documentation guidelines:

1. **Code Documentation**
   - Add dartdoc comments to all public classes, methods, and properties
   - Include parameter descriptions and return values
   - Document exceptions that may be thrown
   - Provide usage examples for complex functionality

2. **Architecture Documentation**
   - Update relevant markdown files when changing architecture
   - Create new markdown files for new subsystems
   - Keep diagrams up-to-date with code changes

3. **Markdown Format**
   - Use proper markdown syntax
   - Include code examples with syntax highlighting
   - Use tables for structured data
   - Include links to related documentation

4. **Documentation Structure**
   - Overview section explaining purpose
   - Detailed sections for components
   - Examples of usage
   - Best practices and guidelines

## Generating Documentation

To generate API documentation from dartdoc comments:

```bash
dart doc .
```

This will create HTML documentation in the `doc/api` directory.

## Contributing to Documentation

When contributing to documentation:

1. Ensure accuracy and clarity
2. Update related documents for consistency
3. Include code examples where appropriate
4. Follow the established style and format
5. Add links to related documentation

## Running the Application

To run the application, execute:

```bash
flutter run -d macos
```

Always use the `-d macos` flag to skip the device selection prompt.

## Running Tests

To run unit tests:

```bash
flutter test
```

To run integration tests:

```bash
flutter test -d macos integration_test/app_test.dart
flutter test -d macos integration_test/supabase_online_test.dart
```

You can also use our convenience scripts in the `scripts` directory:

```bash
./scripts/run_integration_tests.sh integration_test/app_test.dart
./scripts/run_integration_tests.sh integration_test/supabase_online_test.dart 