# Vendor Management

## Overview
This document outlines the implementation of vendor management features for accounts payable operations. The vendor management system enables tracking and management of vendor information, which serves as the foundation for accounts payable operations.

## Data Model
The Vendor model contains fields required for accounts payable operations:

```dart
class Vendor {
  String vendorId;          // Unique identifier for the vendor
  String vendorName;        // Name of the vendor
  String? vendorNumber;     // Vendor number for reference
  String? organizationNumber; // Organization/tax ID number  
  bool isActive;            // Whether the vendor is active
  String? phone;            // Contact phone number
  String? email;            // Contact email address
  String? address;          // Physical address
  String? zipCode;          // ZIP/Postal code
  String? city;             // City
  String? country;          // Country
  String? bankAccountType;  // Type of bank account for payments
  String? bankAccountNumber; // Bank account number for payments
  String? contactPerson;    // Primary contact at vendor
  int? companyId;           // Associated company ID
}
```

## Database Structure
The vendors table in Supabase corresponds to the Vendor model with snake_case field names:

```
vendors {
    varchar vendor_id PK         // Primary key
    varchar vendor_name          // Required vendor name
    varchar vendor_number        // Optional vendor reference number
    varchar organization_number  // Optional tax/organization ID
    boolean is_active            // Activity status (defaults to true)
    varchar phone                // Optional contact phone
    varchar email                // Optional contact email
    varchar address              // Optional street address
    varchar zip_code             // Optional ZIP/postal code
    varchar city                 // Optional city
    varchar country              // Optional country
    varchar bank_account_type    // Optional bank account type
    varchar bank_account_number  // Optional bank account number
    varchar contact_person       // Optional primary contact name
    int company_id FK            // Foreign key to companies table
}
```

## Architecture
The vendor management implementation follows the application's MVVM architecture:

1. **Model**: `Vendor` class in `lib/models/vendor.dart` with Freezed for immutability
2. **ViewModel**: `VendorViewModel` in `lib/viewmodels/vendor_viewmodel.dart` for business logic
3. **Service**: Database operations in `SupabaseDatabaseService` for vendor CRUD operations
4. **Tests**: Unit tests in `test/viewmodels/vendor_viewmodel_test.dart`

## Implementation Details

### VendorViewModel
The VendorViewModel provides methods for:
- Retrieving all vendors
- Retrieving a vendor by ID
- Creating a new vendor
- Updating an existing vendor
- Deleting a vendor

All methods include proper error handling with BusinessException for UI feedback.

### Database Service
The SupabaseDatabaseService implements vendor operations using Supabase, including:
- JSON mapping between Dart camelCase and Supabase snake_case
- Mock implementations for testing
- Error handling and logging

### Testing
The implementation includes comprehensive unit tests for:
- All CRUD operations
- Error handling scenarios
- Both mocked and non-mocked operation modes

## Future Enhancements
- Vendor invoices management
- Payment processing and tracking
- Integration with general ledger
- Vendor statement reporting
- File attachments for vendor documentation 