# Project CRUD Operations

This document provides a comprehensive overview of the Create, Read, Update, Delete (CRUD) operations for Projects in the WeLikeMoney application.

## Architecture Overview

The Project CRUD operations follow a layered architecture pattern:

1. **Database Layer**: Implements the actual data operations using Supabase
2. **Service Layer**: Provides a clean interface for database operations
3. **ViewModel Layer**: Adds business logic and error handling
4. **Provider Layer**: Exposes operations to the UI through Riverpod
5. **UI Layer**: Presents data and captures user input

## Implementation Details

### Database Service Layer

The `SupabaseDatabaseService` class in `lib/services/supabase_database_service.dart` implements all CRUD operations for Projects:

#### Create

```dart
@override
Future<Project> createProject(Project project) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate creating a project
      debugPrint('Mock creating project: ${project.projectName}');
      // Return a new project with a generated ID
      return project.copyWith(projectId: 100 + DateTime.now().second);
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('projects')
            .insert(project.toJson())
            .select()
            .single();
    return Project.fromJson(response);
  });
}
```

#### Read

```dart
@override
Future<List<Project>> getProjects() async {
  return _executeDbOperation(() async {
    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response = await _client.from('projects').select();
    return (response as List)
        .map((json) => Project.fromJson(json as Map<String, dynamic>))
        .toList();
  });
}

@override
Future<Project?> getProjectById(int id) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, return mock data
      final mockProjects = _getMockData<List<Project>>();
      final matchingProject =
          mockProjects
              .where((project) => project.projectId == id)
              .firstOrNull;
      return matchingProject;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('projects')
            .select()
            .eq('project_id', id)
            .maybeSingle();
    if (response == null) return null;
    return Project.fromJson(response);
  });
}
```

#### Update

```dart
@override
Future<Project> updateProject(Project project) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate updating a project
      debugPrint('Mock updating project: ${project.projectName}');
      // Return the same project to simulate a successful update
      return project;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('projects')
            .update(project.toJson())
            .eq('project_id', project.projectId)
            .select()
            .single();
    return Project.fromJson(response);
  });
}
```

#### Delete

```dart
@override
Future<void> deleteProject(int id) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate deleting a project
      debugPrint('Mock deleting project with ID: $id');
      return;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    await _client.from('projects').delete().eq('project_id', id);
  });
}
```

### ViewModel Layer

The `ProjectViewModel` class in `lib/viewmodels/project_viewmodel.dart` wraps these database operations and adds error handling:

```dart
class ProjectViewModel {
  final DatabaseService _databaseService;
  final ErrorHandler _errorHandler;

  ProjectViewModel(this._databaseService, this._errorHandler);

  Future<List<Project>> getProjects() async {
    try {
      return await _databaseService.getProjects();
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  Future<Project?> getProjectById(int id) async {
    try {
      return await _databaseService.getProjectById(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  Future<Project> createProject(Project project) async {
    try {
      return await _databaseService.createProject(project);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  Future<Project> updateProject(Project project) async {
    try {
      return await _databaseService.updateProject(project);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  Future<void> deleteProject(int id) async {
    try {
      await _databaseService.deleteProject(id);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }
}
```

### Provider Layer

The `project_provider.dart` file exposes these operations through Riverpod providers:

```dart
/// Provider for the ProjectViewModel
final projectViewModelProvider = Provider<ProjectViewModel>((ref) {
  return getIt<ProjectViewModel>();
});

/// FutureProvider for fetching all projects
final projectsProvider = FutureProvider<List<Project>>((ref) async {
  final viewModel = ref.watch(projectViewModelProvider);
  return viewModel.getProjects();
});

/// FutureProvider family for fetching a project by ID
final projectByIdProvider = FutureProvider.family<Project?, int>((
  ref,
  id,
) async {
  final viewModel = ref.watch(projectViewModelProvider);
  return viewModel.getProjectById(id);
});

/// Provider for project creation
final projectCreationProvider = Provider<Future<Project> Function(Project)>((
  ref,
) {
  final viewModel = ref.watch(projectViewModelProvider);
  return (Project project) => viewModel.createProject(project);
});

/// Provider for project updates
final projectUpdateProvider = Provider<Future<Project> Function(Project)>((
  ref,
) {
  final viewModel = ref.watch(projectViewModelProvider);
  return (Project project) => viewModel.updateProject(project);
});

/// Provider for project deletion
final projectDeletionProvider = Provider<Future<void> Function(int)>((ref) {
  final viewModel = ref.watch(projectViewModelProvider);
  return (int id) => viewModel.deleteProject(id);
});
```

### UI Layer

The `ProjectsScreen` widget in `lib/ui/screens/projects_screen.dart` uses these providers to implement the user interface for CRUD operations:

#### Read (List View)

```dart
body: projectsAsync.when(
  loading: () => const Center(child: CircularProgressIndicator()),
  error: (error, stackTrace) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 60),
        const SizedBox(height: 16),
        Text(
          'Error loading projects: ${error.toString()}',
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () {
            // Use the result of refresh
            final _ = ref.refresh(projectsProvider);
          },
          child: const Text('Retry'),
        ),
      ],
    ),
  ),
  data: (projects) {
    if (projects.isEmpty) {
      return const Center(
        child: Text('No projects found. Create one to get started.'),
      );
    }

    return ListView.builder(
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return ProjectListItem(project: project);
      },
    );
  },
),
```

#### Create

```dart
void _showAddProjectDialog(BuildContext context, WidgetRef ref) {
  final formKey = GlobalKey<FormState>();
  String projectCode = '';
  String projectName = '';
  String? description;

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Add Project'),
      content: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Project Code',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a project code';
                }
                return null;
              },
              onSaved: (value) => projectCode = value!,
            ),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Project Name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a project name';
                }
                return null;
              },
              onSaved: (value) => projectName = value!,
            ),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
              ),
              onSaved: (value) => description = value,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              formKey.currentState!.save();

              // Create a new project with a temporary ID
              final newProject = Project(
                projectId: 0, // Temporary ID
                projectCode: projectCode,
                projectName: projectName,
                description: description,
              );

              try {
                // Use the creation provider to create the project
                final createProject = ref.read(projectCreationProvider);
                await createProject(newProject);

                // Refresh the projects list
                final _ = await ref.refresh(projectsProvider.future);

                // Close the dialog
                if (context.mounted) {
                  Navigator.of(context).pop();
                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Project created successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                // Show error message
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to create project: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            }
          },
          child: const Text('Add'),
        ),
      ],
    ),
  );
}
```

#### Update

```dart
void _showEditProjectDialog(BuildContext context, WidgetRef ref) {
  final formKey = GlobalKey<FormState>();
  String projectCode = project.projectCode;
  String projectName = project.projectName;
  String? description = project.description;

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Edit Project'),
      content: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              initialValue: projectCode,
              decoration: const InputDecoration(
                labelText: 'Project Code',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a project code';
                }
                return null;
              },
              onSaved: (value) => projectCode = value!,
            ),
            TextFormField(
              initialValue: projectName,
              decoration: const InputDecoration(
                labelText: 'Project Name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a project name';
                }
                return null;
              },
              onSaved: (value) => projectName = value!,
            ),
            TextFormField(
              initialValue: description,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
              ),
              onSaved: (value) => description = value,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              formKey.currentState!.save();

              // Create an updated project with the same ID
              final updatedProject = project.copyWith(
                projectCode: projectCode,
                projectName: projectName,
                description: description,
              );

              try {
                // Use the update provider to update the project
                final updateProject = ref.read(projectUpdateProvider);
                await updateProject(updatedProject);

                // Refresh the projects list
                final _ = await ref.refresh(projectsProvider.future);

                // Close the dialog
                if (context.mounted) {
                  Navigator.of(context).pop();
                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Project updated successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                // Show error message
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to update project: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            }
          },
          child: const Text('Update'),
        ),
      ],
    ),
  );
}
```

#### Delete

```dart
void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Delete Project'),
      content: Text(
        'Are you sure you want to delete "${project.projectName}"?',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () async {
            try {
              // Use the deletion provider to delete the project
              final deleteProject = ref.read(projectDeletionProvider);
              await deleteProject(project.projectId);

              // Refresh the projects list
              final _ = await ref.refresh(projectsProvider.future);

              // Close the dialog
              if (context.mounted) {
                Navigator.of(context).pop();
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Project deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } catch (e) {
              // Show error message
              if (context.mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to delete project: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: const Text('Delete'),
        ),
      ],
    ),
  );
}
```

## Mock Implementation

For development without a live Supabase connection, there's a mock implementation in the `_getMockData` method of `SupabaseDatabaseService`:

```dart
T _getMockData<T>() {
  debugPrint('Using mock data for type $T');

  // Return mock projects list
  if (T == List<Project>) {
    return [
          const Project(
            projectId: 1,
            projectCode: 'PROJ-001',
            projectName: 'Website Redesign',
            description: 'Complete redesign of company website',
          ),
          const Project(
            projectId: 2,
            projectCode: 'PROJ-002',
            projectName: 'Mobile App Development',
            description: 'Development of iOS and Android mobile applications',
          ),
          const Project(
            projectId: 3,
            projectCode: 'PROJ-003',
            projectName: 'Database Migration',
            description: 'Migration from legacy database to new system',
          ),
        ]
        as T;
  } 
  // Return a single mock project
  else if (T == Project) {
    return const Project(
          projectId: 1,
          projectCode: 'PROJ-001',
          projectName: 'Website Redesign',
          description: 'Complete redesign of company website',
        )
        as T;
  }
  
  // Other mock data types...
  
  return null as T;
}
```

## Testing

The Project CRUD operations are thoroughly tested in:

1. `test/services/database_service_test.dart` - Tests the database service layer
2. `test/viewmodels/project_viewmodel_test.dart` - Tests the view model layer
3. `test/providers/project_provider_test.dart` - Tests the provider layer

## Database Schema

The Projects are stored in the `projects` table in Supabase with the following schema:

| Column       | Type    | Description                      |
|--------------|---------|----------------------------------|
| project_id   | integer | Primary key, auto-incrementing   |
| project_code | text    | Unique code for the project      |
| project_name | text    | Name of the project              |
| description  | text    | Optional description (nullable)  |

## Best Practices

1. **Error Handling**: All operations include proper error handling and user feedback
2. **Validation**: Input validation is performed before submitting data
3. **Optimistic Updates**: UI is updated optimistically before server confirmation
4. **Refresh Strategy**: Data is refreshed after CRUD operations to ensure consistency
5. **Separation of Concerns**: Clear separation between data, business logic, and UI layers

## Conclusion

The Project CRUD operations in WeLikeMoney follow a clean, layered architecture that promotes maintainability and testability. The implementation includes proper error handling, validation, and user feedback at all levels. 