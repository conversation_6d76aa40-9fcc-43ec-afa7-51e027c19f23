# Company CRUD Operations

This document provides a comprehensive overview of the Create, Read, Update, Delete (CRUD) operations for Companies in the WeLikeMoney application.

## Architecture Overview

The Company CRUD operations follow a layered architecture pattern:

1. **Database Layer**: Implements the actual data operations using Supabase
2. **Service Layer**: Provides a clean interface for database operations
3. **ViewModel Layer**: Adds business logic and error handling
4. **Provider Layer**: Exposes operations to the UI through Riverpod
5. **UI Layer**: Presents data and captures user input

## Implementation Details

### Database Service Layer

The `SupabaseDatabaseService` class in `lib/services/supabase_database_service.dart` implements all CRUD operations for Companies:

#### Create

```dart
@override
Future<Company> createCompany(Company company) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate creating a company
      debugPrint('Mock creating company: ${company.companyName}');
      // Return a new company with a generated ID
      return company.copyWith(companyId: 100 + DateTime.now().second);
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('companies')
            .insert(company.toJson())
            .select()
            .single();
    return Company.fromJson(response);
  });
}
```

#### Read

```dart
@override
Future<List<Company>> getCompanies() async {
  return _executeDbOperation(() async {
    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response = await _client.from('companies').select();
    return (response as List)
        .map((json) => Company.fromJson(json as Map<String, dynamic>))
        .toList();
  });
}

@override
Future<Company?> getCompanyById(int id) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, return mock data
      final mockCompanies = _getMockData<List<Company>>();
      final matchingCompany =
          mockCompanies
              .where((company) => company.companyId == id)
              .firstOrNull;
      return matchingCompany;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('companies')
            .select()
            .eq('company_id', id)
            .maybeSingle();
    if (response == null) return null;
    return Company.fromJson(response);
  });
}
```

#### Update

```dart
@override
Future<Company> updateCompany(Company company) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate updating a company
      debugPrint('Mock updating company: ${company.companyName}');
      // Return the same company to simulate a successful update
      return company;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    final response =
        await _client
            .from('companies')
            .update(company.toJson())
            .eq('company_id', company.companyId)
            .select()
            .single();
    return Company.fromJson(response);
  });
}
```

#### Delete

```dart
@override
Future<void> deleteCompany(int id) async {
  return _executeDbOperation(() async {
    if (_useMockData && !kReleaseMode) {
      // In development mode with mock data, simulate deleting a company
      debugPrint('Mock deleting company with ID: $id');
      return;
    }

    if (_client == null) {
      throw Exception('Supabase client is not initialized');
    }
    await _client.from('companies').delete().eq('company_id', id);
  });
}
```

## Company Model

The `Company` class in `lib/models/company.dart` defines the data structure for companies:

```dart
@freezed
abstract class Company with _$Company {
  const factory Company({
    required int companyId,
    required String companyName,
    String? address,
    String? contactPerson,
  }) = _Company;

  factory Company.fromJson(Map<String, dynamic> json) =>
      _$CompanyFromJson(json);
}
```

## Relationships

The Company entity has relationships with several other entities in the system:

1. **Projects**: Each project belongs to a company
2. **Staff Members**: Staff members are associated with a company
3. **Accounts**: Accounts are associated with a company
4. **Customers**: Customers are associated with a company
5. **Vendors**: Vendors are associated with a company
6. **Invoices**: Invoices are associated with a company
7. **Expenses**: Expenses are associated with a company
8. **Payments**: Payments (in and out) are associated with a company
9. **General Ledger**: General ledger entries are associated with a company

These relationships are implemented through foreign keys in the database and through the corresponding fields in the model classes.

## Mock Implementation

For development without a live Supabase connection, there's a mock implementation in the `_getMockData` method of `SupabaseDatabaseService`:

```dart
if (T == List<Company>) {
  return [
        const Company(
          companyId: 1,
          companyName: 'Acme Corporation',
          address: '123 Main St, Anytown, USA',
          contactPerson: 'John Doe',
        ),
        const Company(
          companyId: 2,
          companyName: 'TechCorp Inc.',
          address: '456 Tech Blvd, Silicon Valley, USA',
          contactPerson: 'Jane Smith',
        ),
      ]
      as T;
} else if (T == Company) {
  return const Company(
        companyId: 1,
        companyName: 'Acme Corporation',
        address: '123 Main St, Anytown, USA',
        contactPerson: 'John Doe',
      )
      as T;
}
```

## Testing

The Company CRUD operations are thoroughly tested in:

1. `test/models/company_test.dart` - Tests the Company model
2. `test/services/database_service_test.dart` - Tests the database service layer

## Database Schema

The Companies are stored in the `companies` table in Supabase with the following schema:

| Column         | Type    | Description                    |
|----------------|---------|--------------------------------|
| company_id     | integer | Primary key, auto-incrementing |
| company_name   | text    | Name of the company (unique)   |
| address        | text    | Company address (nullable)     |
| contact_person | text    | Contact person (nullable)      |

## Best Practices

1. **Error Handling**: All operations include proper error handling and user feedback
2. **Validation**: Input validation is performed before submitting data
3. **Optimistic Updates**: UI is updated optimistically before server confirmation
4. **Refresh Strategy**: Data is refreshed after CRUD operations to ensure consistency
5. **Separation of Concerns**: Clear separation between data, business logic, and UI layers

## Conclusion

The Company CRUD operations in WeLikeMoney follow a clean, layered architecture that promotes maintainability and testability. The implementation includes proper error handling, validation, and user feedback at all levels. 