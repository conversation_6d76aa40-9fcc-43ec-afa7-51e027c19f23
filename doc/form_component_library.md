# Form Component Library

The WeLikeMoney Form Component Library provides a comprehensive set of components for creating forms with consistent styling, validation, and behavior.

## Overview

The library consists of:

- **Base Components**: `FormSection`, `FormSectionHeader`, `FormBuilder`, `FormStateManager`
- **Form Fields**: `AppTextFormField`, `AppDateFormField`, `AppDropdownForm<PERSON>ield`, `AppCurrencyFormField`
- **Utilities**: Form validation integration, field dependencies, dirty tracking

## Getting Started

Import the form component library:

```dart
import 'package:we_like_money/ui/forms/index.dart';
```

## Using the Form Builder

The `FormBuilder` class provides a fluent API for creating forms with minimal boilerplate:

```dart
final formBuilder = FormBuilder();

final form = formBuilder
  .addSection(
    VendorSelectionSection(viewModel: viewModel),
  )
  .addSection(
    InvoiceDetailsSection(viewModel: viewModel),
  )
  .addSubmitButton(
    text: 'Save Invoice',
    onPressed: () {
      if (formBuilder.validate()) {
        viewModel.saveInvoice();
      }
    },
  )
  .buildScrollable(context);
```

## Creating Form Sections

Extend the `FormSection` base class to create custom form sections:

```dart
class ContactFormSection extends FormSection {
  final ContactViewModel viewModel;
  
  const ContactFormSection({
    required this.viewModel,
    super.title = 'Contact Information',
    super.helpText = 'Enter the contact details',
    super.headerIcon = Icons.contact_mail,
  });
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      children: [
        AppTextFormField(
          labelText: 'Name',
          value: viewModel.name,
          onChanged: (value) => viewModel.name = value,
          isRequired: true,
          validationRule: Validator.required('Name is required'),
        ),
        const SizedBox(height: 16),
        AppTextFormField.email(
          value: viewModel.email,
          onChanged: (value) => viewModel.email = value,
        ),
      ],
    );
  }
}
```

## Using Form Fields

The library provides several form field components with built-in validation:

### Text Form Field

```dart
AppTextFormField(
  labelText: 'Name',
  value: name,
  onChanged: (value) => name = value,
  isRequired: true,
  validationRule: Validator.required('Name is required'),
)

// Specialized variants
AppTextFormField.email(
  value: email,
  onChanged: (value) => email = value,
)

AppTextFormField.password(
  value: password,
  onChanged: (value) => password = value,
  minLength: 10,
)

AppTextFormField.numeric(
  value: quantity.toString(),
  onChanged: (value) => quantity = int.tryParse(value) ?? 0,
  min: 1,
  max: 100,
)
```

### Date Form Field

```dart
AppDateFormField(
  labelText: 'Invoice Date',
  value: invoiceDate,
  onChanged: (date) => invoiceDate = date,
  isRequired: true,
)

// Specialized variants
AppDateFormField.businessDay(
  value: dueDate,
  onChanged: (date) => dueDate = date,
  labelText: 'Due Date',
  holidays: holidays,
)

AppDateFormField.withMinDate(
  value: dueDate,
  onChanged: (date) => dueDate = date,
  minDate: invoiceDate,
  labelText: 'Due Date',
)
```

### Dropdown Form Field

```dart
AppDropdownFormField<int>(
  labelText: 'Project',
  value: selectedProjectId,
  onChanged: (value) => selectedProjectId = value,
  items: projects.map((p) => p.projectId).toList(),
  displayStringBuilder: (id) => projects
    .firstWhere((p) => p.projectId == id)
    .projectName,
  isRequired: true,
)

// Specialized variants
AppDropdownFormField.required(
  value: selectedVendorId,
  onChanged: (value) => selectedVendorId = value,
  items: vendors.map((v) => v.vendorId).toList(),
  displayStringBuilder: (id) => vendors
    .firstWhere((v) => v.vendorId == id)
    .vendorName,
  labelText: 'Vendor',
)

AppDropdownFormField.optional(
  value: selectedStaffId,
  onChanged: (value) => selectedStaffId = value,
  items: staffMembers.map((s) => s.staffId).toList(),
  displayStringBuilder: (id) => staffMembers
    .firstWhere((s) => s.staffId == id)
    .staffName,
  labelText: 'Staff Member',
)
```

### Currency Form Field

```dart
AppCurrencyFormField(
  labelText: 'Amount',
  value: amount,
  onChanged: (value) => amount = value,
  isRequired: true,
  currencySymbol: '$',
)

// Specialized variants
AppCurrencyFormField.invoiceAmount(
  value: amount,
  onChanged: (value) => amount = value,
  minAmount: 0.01,
)

AppCurrencyFormField.taxAmount(
  value: taxAmount,
  onChanged: (value) => taxAmount = value,
  invoiceAmount: amount,
)
```

## Managing Form State

The `FormStateManager` class provides utilities for managing form state:

```dart
final formStateManager = FormStateManager();

// Set original values
formStateManager.setOriginalValue('name', 'John Doe');
formStateManager.setOriginalValue('email', '<EMAIL>');

// Update values
formStateManager.setCurrentValue('name', 'Jane Doe');

// Check if form is dirty
final isDirty = formStateManager.isDirty.value; // true

// Validate and submit
if (formStateManager.validate()) {
  formStateManager.submit(() async {
    // Submit form data
    await apiService.updateUser(
      name: formStateManager.getCurrentValue('name'),
      email: formStateManager.getCurrentValue('email'),
    );
  });
}

// Reset form
formStateManager.reset();
```

## Field Dependencies

The `FormStateManager` also supports field dependencies:

```dart
// Add a dependency between fields
formStateManager.addFieldDependency('dueDate', 'invoiceDate');

// Add a listener for a field
formStateManager.addFieldListener('dueDate', () {
  // Update due date when invoice date changes
  final invoiceDate = formStateManager.getCurrentValue<DateTime>('invoiceDate');
  if (invoiceDate != null) {
    formStateManager.setCurrentValue(
      'dueDate',
      invoiceDate.add(const Duration(days: 30)),
    );
  }
});
```

## Best Practices

1. **Use the Form Builder**: The `FormBuilder` class simplifies form creation and ensures consistent styling.

2. **Create Reusable Form Sections**: Extend the `FormSection` base class to create reusable form sections.

3. **Use Specialized Form Fields**: Use the specialized form field variants for common input types.

4. **Leverage the Validation Framework**: Integrate with the validation framework for consistent validation.

5. **Track Form State**: Use the `FormStateManager` to track form state and handle submission.

6. **Handle Field Dependencies**: Use the field dependency system to handle relationships between fields.

7. **Provide Clear Error Messages**: Use descriptive error messages to guide users.

8. **Use Form Section Headers**: Include section headers with help text to improve usability.

9. **Test Form Validation**: Write tests for form validation to ensure correctness.

10. **Document Form Requirements**: Document form requirements and validation rules.

## Examples

See the following examples for more details:

- `lib/examples/forms/simple_form_example.dart` - A simple form using the library
- `lib/examples/forms/complex_form_example.dart` - A complex form with conditional fields and validation
