# Conditional Testing in WeLikeMoney

This document explains how we implement conditional testing in the WeLikeMoney app, allowing tests to run both in online mode (against a real Supabase instance) and offline mode (using mocks).

## Overview

Our testing framework is designed to be flexible, allowing developers to run tests in different environments:

1. **Offline Mode** (default): Uses mock implementations and doesn't require any external dependencies.
2. **Online Mode**: Connects to a real Supabase instance to test against actual database operations.
3. **Fallback Online Mode**: When true online testing isn't possible due to platform limitations, uses a special mock client.

This approach makes it easier to run tests in CI/CD environments while still enabling thorough testing against the real backend when needed.

## Configuration

Testing mode is controlled by the `.env.test` file, which should be placed in the project root. A template is provided as `.env.test.example`.

The key configuration option is:

```
SUPABASE_TEST_ONLINE=true|false
```

- When set to `true`, tests will attempt to connect to the Supabase instance specified by `SUPABASE_TEST_URL` and `SUPABASE_TEST_ANON_KEY`.
- When set to `false`, tests will use mock implementations and won't make any network requests.

## Implementation Details

### TestConfig Class

The `TestConfig` class (in `test/config/test_config.dart`) is responsible for:

1. Loading the `.env.test` file
2. Determining whether tests should run in online or offline mode
3. Initializing Supabase in online mode
4. Falling back to a mock client when necessary

```dart
// Initialize the test configuration
await TestConfig.instance.initialize();

// Check if we should use online testing
final useOnlineMode = TestConfig.instance.isOnlineTest;

// Check if we're using a fallback mock client
final usingFallbackClient = TestConfig.instance.usingFallbackClient;
```

### SharedPreferences in Test Environment

Flutter tests run in a special environment that doesn't support platform channels, which means plugins like `shared_preferences` used by Supabase Flutter don't work properly. We handle this in two ways:

1. **Custom Storage Implementation**: We provide a custom in-memory implementation of `GotrueAsyncStorage` that doesn't rely on `shared_preferences`.

2. **Fallback Mock Client**: If Supabase initialization fails in online mode (due to platform channel issues), we:
   - Log a helpful error message explaining the limitation
   - Set a flag `usingFallbackClient` to indicate we're in fallback mode
   - Provide a mock client through `SupabaseTestHelper`
   - Adapt test expectations to work with the mock client

This approach allows tests to run in "online mode" even on platforms where full online testing isn't possible due to platform limitations.

### Using SupabaseTestHelper

The `SupabaseTestHelper` class provides a way to get a Supabase client appropriate for the current test environment:

```dart
// Get the Supabase client (returns a mock in offline mode or fallback mode)
final supabaseClient = SupabaseTestHelper.getSupabaseClient();
```

In offline mode, this will return `null`. In online mode, it will return the real Supabase client when possible, or a mock client in fallback mode.

### Test Skipping and Adaptation

Tests that require a Supabase connection check both `isOnlineTest` and `usingFallbackClient` flags:

```dart
// Skip all tests if not in online mode
final bool skipTests = !TestConfig.instance.isOnlineTest;
if (skipTests) {
  // Skip tests in offline mode
  return;
}

// Check if we're using a fallback client
final usingFallbackClient = TestConfig.instance.usingFallbackClient;
if (usingFallbackClient) {
  // Adapt test expectations for fallback mode
}
```

For tests that cannot work with a mock client (like creating real records), we can skip them when in fallback mode:

```dart
if (usingFallbackClient) {
  debugPrint('Skipping test that requires real data when using fallback client');
  return;
}
```

## Best Practices

1. **Default to Offline Mode**: Commit `.env.test` with `SUPABASE_TEST_ONLINE=false` to ensure CI/CD pipelines and other developers can run tests without needing Supabase credentials.

2. **Use Try-Catch in Online Tests**: When running in online mode, wrap test initialization in try-catch blocks to gracefully handle connection failures.

3. **Mock Database Operations**: Ensure all database operations have corresponding mock implementations for offline testing.

4. **Check Both Flags**: Always check both `TestConfig.instance.isOnlineTest` and `TestConfig.instance.usingFallbackClient` when writing tests that need to work in all modes.

5. **Adapt Test Expectations**: Modify your test assertions based on the testing mode to ensure tests pass in all configurations.

## Running Tests

To run tests in offline mode (default):
```bash
flutter test
```

To run tests in online mode (requires properly configured `.env.test`):
```bash
# First modify .env.test to set SUPABASE_TEST_ONLINE=true
flutter test
```

## True Online Testing

To run tests against a real Supabase instance without the fallback mechanism, you need to use environments that support platform channels:

1. **Integration Tests**: Use `integration_test` package which runs tests in a real app environment.
2. **Device Testing**: Run tests on a physical device or emulator.
3. **Web Testing**: Run tests in a web environment.

Example of running integration tests:
```bash
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/app_test.dart
```

## Troubleshooting

If you see errors like:
```
MissingPluginException(No implementation found for method getAll on channel plugins.flutter.io/shared_preferences)
```

This indicates that the test is trying to use platform channels that aren't available in the test environment. You have three options:

1. **Use Offline Mode**: Set `SUPABASE_TEST_ONLINE=false` in your `.env.test` file
2. **Use Fallback Mode**: Keep `SUPABASE_TEST_ONLINE=true` and use the fallback client (tests will run but won't connect to Supabase)
3. **Use Integration Tests**: For true online testing, use the integration_test package 