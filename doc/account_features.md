# Account Features Documentation

## Overview

The Account Features module provides functionality for managing and viewing general ledger accounts within the WeLikeMoney application. This module integrates with the application's general ledger system to display account balances and detailed transaction history.

## Core Components

### Models

#### Account Model
- Located at: `lib/models/account.dart`
- Primary fields:
  - `accountNumber`: Unique identifier for the account
  - `accountName`: Descriptive name for the account
  - `companyId`: Reference to the company that owns the account
- The model is immutable and implements equality checks

### Services

#### Database Service Interface
- Located at: `lib/services/database_service.dart`
- Provides methods for account-related operations:
  - `getAccounts()`: Retrieves all accounts
  - `getAccountByNumber(String accountNumber)`: Finds a specific account by number
  - `getGeneralLedgerEntriesByAccount(String accountNumber)`: Retrieves all ledger entries for a specific account

#### Supabase Database Service Implementation
- Located at: `lib/services/supabase_database_service.dart`
- Implements the database service interface for Supabase
- Provides mock data generation for offline testing

### ViewModels

#### Account ViewModel
- Located at: `lib/viewmodels/account_viewmodel.dart`
- Business logic for account operations:
  - `getAccounts()`: Retrieves all accounts from the database service
  - `getAccountByNumber(String accountNumber)`: Finds a specific account
  - `getGeneralLedgerEntriesByAccount(String accountNumber)`: Gets all ledger entries for an account
  - `calculateAccountBalance(String accountNumber)`: Computes current balance by summing debits and credits

### UI Components

#### Account List Screen
- Located at: `lib/screens/account/account_list_screen.dart`
- Displays a list of all accounts with:
  - Account name and number
  - Current balance
  - Navigation to account details
- Implements loading, error, and empty states

#### Account Register Screen
- Located at: `lib/screens/account/account_register_screen.dart`
- Shows detailed transaction history for a selected account:
  - Account header with summary information
  - Chronological list of transactions
  - Debit and credit amounts clearly distinguished
  - Running balance calculations
- Implements loading, error, and empty states

## Integration Points

### Navigation
- The Account features are accessible from the Home screen
- Navigation flow:
  1. Home screen → Account List
  2. Account List → Account Register (selecting an account)

### General Ledger System
- Accounts are integrated with the General Ledger module
- Transactions affecting accounts are recorded as General Ledger entries
- Account balances are calculated dynamically based on ledger entries

## Test Coverage

### Unit Tests
- Located at: `test/viewmodels/account_viewmodel_test.dart`
- Tests business logic in AccountViewModel:
  - Account retrieval
  - Account lookup by number
  - Ledger entry retrieval
  - Balance calculation

### Widget Tests
- Located at: 
  - `test/screens/account/account_list_screen_test.dart`
  - `test/screens/account/account_register_screen_test.dart`
- Tests UI components:
  - Loading states
  - Error handling
  - Data display
  - User interactions
  - Empty states

## Error Handling

The Account Features module implements comprehensive error handling:
- Service failures are caught and propagated as BusinessExceptions
- UI components display user-friendly error messages
- Retry functionality for failed operations
- Proper validation of account numbers and data

## Future Enhancements

Potential future enhancements to the Account features include:
- Account creation and editing
- Account categorization and tagging
- Advanced filtering and sorting of transactions
- Export of account data to CSV/PDF
- Account reconciliation workflows 