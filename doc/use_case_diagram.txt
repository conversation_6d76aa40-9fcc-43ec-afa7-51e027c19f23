graph LR
    Accountant(Accountant)
    Bookkeeper(Bookkeeper)
    ExternalAccountant(External Accountant)
    Stakeholder(Stakeholder)
    BI_System(BI System)

    subgraph Accounting_System
        GeneralLedger[Manage General Ledger]
        Invoicing[Create and Manage Invoices]
        Expenses[Track Expenses]
        Reporting[Generate Reports]
        BankReconciliation[Reconcile Bank Accounts]

        CreateInvoice[Create Invoice]
        RecordPaymentIn[Record Payment Received]
        SendInvoice[Send Invoice]
        ViewInvoice[View Invoice History]

        RecordExpense[Record Expense]
        RecordPaymentOut[Record Payment Made]
        CategorizeExpense[Categorize Expense]

        GenerateFinancialReports[Generate Financial Reports]
        GenerateAuthorityReports[Generate Authority Reports]
        ViewInternalVisualizations[View Internal Visualizations]
    end

    Accountant --> GeneralLedger
    Accountant --> Invoicing
    Accountant --> Expenses
    Accountant --> Reporting
    Accountant --> BankReconciliation

    Bookkeeper --> GeneralLedger
    Bookkeeper --> Invoicing
    Bookkeeper --> Expenses

    ExternalAccountant --> Reporting

    Stakeholder --> Reporting

    BI_System <--> Reporting

    Invoicing --> CreateInvoice
    Invoicing --> RecordPaymentIn
    Invoicing --> SendInvoice
    Invoicing --> ViewInvoice

    Expenses --> RecordExpense
    Expenses --> RecordPaymentOut
    Expenses --> CategorizeExpense

    Reporting --> GenerateFinancialReports
    Reporting --> GenerateAuthorityReports
    Reporting --> ViewInternalVisualizations
    