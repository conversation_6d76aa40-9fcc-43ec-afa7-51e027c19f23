# Expense and Ledger Operations

## Overview
This document describes the database operations and business logic for handling expenses and their corresponding ledger entries in the accounting system.

## Database Structure

### Expenses Table
- `expense_id`: Primary key
- `transaction_id`: UUID to group related entries
- `vendor_id`: Reference to vendor
- `expense_date`: Date of the expense
- `amount`: Total amount including tax
- `currency_code`: Currency of the transaction
- `project_id`: Optional reference to project
- `staff_id`: Optional reference to staff member
- `tax_amount`: Optional tax amount
- `company_id`: Reference to company
- `payment_method`: Enum (cash, credit_card, bank_transfer, check)
- `credit_card_number`: Optional masked credit card number

### GeneralLedger Table
- `ledger_id`: Primary key
- `transaction_id`: UUID to group related entries
- `transaction_date`: Date of the transaction
- `account_number`: Reference to account
- `description`: Transaction description
- `debit`: Debit amount
- `credit`: Credit amount
- `currency_code`: Currency of the transaction
- `project_id`: Optional reference to project
- `staff_id`: Optional reference to staff member
- `tax_amount`: Optional tax amount
- `company_id`: Reference to company

## Business Rules

### Double-Entry Accounting
1. Every transaction must have equal debits and credits
2. Each transaction must be linked by a unique transaction_id
3. The total amount in the expense must match the total debit/credit in ledger entries

### Tax Handling
1. Tax amounts must be tracked separately in both expense and ledger entries
2. Total tax amount in expense must match sum of tax amounts in ledger entries

### Payment Methods
1. Credit card expenses require masked credit card number
2. Different payment methods map to different accounts:
   - Cash: 1000
   - Credit Card: 2000
   - Bank Transfer: 1100
   - Check: 1200

## Example: Office Supplies Purchase

### Scenario
An employee buys office supplies from Office Depot using a company credit card:
- Net price: $100
- Sales tax (VAT): $10
- Total charged to credit card: $110

### Required Entries

1. Expense Record:
```sql
INSERT INTO expenses (
    expense_id,
    transaction_id,
    vendor_id,
    expense_date,
    amount,
    currency_code,
    project_id,
    staff_id,
    tax_amount,
    company_id,
    payment_method,
    credit_card_number
) VALUES (
    1,
    'uuid-here',
    'OFFICE_DEPOT',
    CURRENT_DATE,
    110.00,
    'USD',
    1,
    1,
    10.00,
    1,
    'credit_card',
    '****1234'
);
```

2. Ledger Entries:
```sql
-- Office supplies expense
INSERT INTO general_ledger (
    ledger_id,
    transaction_id,
    transaction_date,
    account_number,
    description,
    debit,
    credit,
    currency_code,
    project_id,
    staff_id,
    tax_amount,
    company_id
) VALUES (
    1,
    'uuid-here',
    CURRENT_DATE,
    '6110',
    'Office supplies from Office Depot',
    100.00,
    0.00,
    'USD',
    1,
    1,
    0.00,
    1
);

-- Sales tax
INSERT INTO general_ledger (
    ledger_id,
    transaction_id,
    transaction_date,
    account_number,
    description,
    debit,
    credit,
    currency_code,
    project_id,
    staff_id,
    tax_amount,
    company_id
) VALUES (
    2,
    'uuid-here',
    CURRENT_DATE,
    '2641',
    'Sales tax on office supplies',
    10.00,
    0.00,
    'USD',
    1,
    1,
    10.00,
    1
);

-- Credit card payment
INSERT INTO general_ledger (
    ledger_id,
    transaction_id,
    transaction_date,
    account_number,
    description,
    debit,
    credit,
    currency_code,
    project_id,
    staff_id,
    tax_amount,
    company_id
) VALUES (
    3,
    'uuid-here',
    CURRENT_DATE,
    '2493',
    'Credit card payment for office supplies',
    0.00,
    110.00,
    'USD',
    1,
    1,
    0.00,
    1
);
```

## Validation Rules

1. Debit/Credit Balance:
   - Total debit must equal total credit
   - A single entry cannot have both debit and credit

2. Amount Validation:
   - Expense amount must be positive
   - Expense amount must match total debit/credit
   - Tax amounts must match between expense and ledger entries

3. Transaction ID:
   - All related entries must share the same transaction_id
   - Transaction_id must be unique across all transactions

4. Company Isolation:
   - All entries must belong to the same company
   - No cross-company transactions allowed

## Error Handling

The system will throw exceptions in the following cases:
1. Debit does not equal credit
2. Expense amount does not match total debit/credit
3. Tax amounts do not match
4. Invalid payment method
5. Missing required fields
6. Cross-company transaction attempt 