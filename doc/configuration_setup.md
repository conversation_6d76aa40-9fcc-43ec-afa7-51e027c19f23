# Configuration Setup

## Environment Variables

### Overview

We Like Money uses environment variables for configuration, with support for different environments (development, testing, production). Environment variables are loaded from `.env` files, which contain key-value pairs for configuration settings.

### Environment Files

The application looks for `.env` files in the following locations, in order of precedence:

1. Root directory: `/.env`
2. Assets directory: `/assets/.env`
3. Test directory: `/test/.env` (for test-specific configurations)
4. Bundled assets (for production builds)

For test runs, the application also looks for `.env.test` files with the same precedence order.

### Required Environment Variables

```
# Supabase Configuration
SUPABASE_URL=your-project-url.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# App Configuration
APP_NAME=We Like Money
APP_ENV=development|testing|production
```

### Setting Up Environment Files

1. **Development Environment**:
   
   Create a `.env` file in the project root with your development credentials:
   
   ```
   SUPABASE_URL=your-dev-instance.supabase.co
   SUPABASE_ANON_KEY=your-dev-anon-key
   APP_NAME=We Like Money
   APP_ENV=development
   ```

2. **Testing Environment**:
   
   Create a `.env.test` file in the project root with your testing credentials:
   
   ```
   SUPABASE_URL=your-test-instance.supabase.co
   SUPABASE_ANON_KEY=your-test-anon-key
   APP_NAME=We Like Money
   APP_ENV=testing
   ```

3. **Production Environment**:
   
   For production builds, create a `.env` file in the assets directory:
   
   ```
   SUPABASE_URL=your-production-instance.supabase.co
   SUPABASE_ANON_KEY=your-production-anon-key
   APP_NAME=We Like Money
   APP_ENV=production
   ```

### Including .env Files in Assets

To ensure that environment files are bundled with your application:

1. Add the `.env` file to your assets in `pubspec.yaml`:

   ```yaml
   flutter:
     assets:
       - .env
       - assets/.env
       - test/.env
   ```

2. Update your `.gitignore` to avoid committing sensitive information:

   ```
   # Environment files with secrets
   .env
   .env.test
   assets/.env
   test/.env
   
   # But keep example templates
   !.env.example
   !assets/.env.example
   !test/.env.example
   ```

3. Provide example templates with placeholder values for team members.

## Network Entitlements

### macOS Entitlements

For macOS applications, you need to configure network entitlements to allow the app to connect to the Supabase backend.

#### Setting Up Network Entitlements

1. **Update macos/Runner/DebugProfile.entitlements**:

   Add the following entitlements for development:

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>com.apple.security.app-sandbox</key>
       <true/>
       <key>com.apple.security.cs.allow-jit</key>
       <true/>
       <key>com.apple.security.network.server</key>
       <true/>
       <key>com.apple.security.network.client</key>
       <true/>
   </dict>
   </plist>
   ```

2. **Update macos/Runner/Release.entitlements**:

   Also add the network entitlements to the release configuration:

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>com.apple.security.app-sandbox</key>
       <true/>
       <key>com.apple.security.network.client</key>
       <true/>
   </dict>
   </plist>
   ```

3. **Handle Entitlement Errors Gracefully**:

   The application is designed to handle missing network entitlements gracefully by:
   
   - Detecting network permission errors in the `SupabaseDatabaseService`
   - Falling back to mock data when network access is restricted
   - Showing appropriate user feedback
   - Logging detailed error information for troubleshooting

### iOS Entitlements

For iOS applications, ensure the following:

1. **Update Info.plist**:

   Add the `NSAppTransportSecurity` entry to your `ios/Runner/Info.plist`:

   ```xml
   <key>NSAppTransportSecurity</key>
   <dict>
       <key>NSAllowsArbitraryLoads</key>
       <true/>
   </dict>
   ```

2. **For Specific Domains**:

   If you want to restrict to only your Supabase domain rather than allowing arbitrary loads:

   ```xml
   <key>NSAppTransportSecurity</key>
   <dict>
       <key>NSExceptionDomains</key>
       <dict>
           <key>your-project.supabase.co</key>
           <dict>
               <key>NSIncludesSubdomains</key>
               <true/>
               <key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
               <true/>
           </dict>
       </dict>
   </dict>
   ```

## Troubleshooting

### Environment Variables Not Loading

1. **Check File Location**:
   - Ensure your `.env` file is in one of the supported locations
   - Check for correct file naming (no hidden extensions like `.env.txt`)

2. **Verify File is Included in Assets**:
   - Confirm the file is listed in the assets section of `pubspec.yaml`
   - Run `flutter clean` followed by `flutter pub get`

3. **Debug Environment Loading**:
   - The application logs the loading process for environment files
   - Check console output for successful loading messages

### Network Connection Issues

1. **Check Entitlements**:
   - Verify that the entitlement files contain the correct network permissions
   - For macOS, ensure both `network.client` and `network.server` are set to `true`

2. **Test in Mock Mode**:
   - Switch to "Mock Data Only" mode in the Settings screen
   - If mock mode works but remote connection fails, it's likely an entitlement issue

3. **Check Supabase Credentials**:
   - Verify that your Supabase URL and anon key are correct
   - Try accessing your Supabase project directly from a browser

4. **Firewall Settings**:
   - Check if a firewall is blocking the connection
   - Try on a different network to rule out network-specific restrictions 