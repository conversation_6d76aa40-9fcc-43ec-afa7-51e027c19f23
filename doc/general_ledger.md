# General Ledger Feature Documentation

The General Ledger is a core accounting feature in the We Like Money application. It provides a comprehensive view of all financial transactions, following accounting best practices and double-entry bookkeeping principles.

## Core Components

### Models

- **GeneralLedger**: Represents individual ledger entries with properties such as:
  - `ledgerId`: Unique identifier
  - `transactionDate`: Date of the transaction
  - `accountNumber`: Account number the entry belongs to
  - `description`: Descriptive text of the transaction
  - `debit`: Debit amount (if applicable)
  - `credit`: Credit amount (if applicable)
  - `currencyCode`: Currency of the transaction
  - `companyId`: Company the entry belongs to
  - `projectId`: Optional project association
  - `staffId`: Optional staff member association
  - `taxAmount`: Optional tax amount

### ViewModel

- **GeneralLedgerViewModel**: Provides business logic for General Ledger functionality, including:
  - Retrieving all general ledger entries
  - Retrieving entries by account
  - Retrieving entries by date range
  - Calculating net balance across entries

### UI Components

- **GeneralLedgerScreen**: Main screen for viewing and filtering general ledger entries, featuring:
  - A data table displaying transactions with their details
  - Filtering capabilities by:
    - Account number
    - Date range
    - Text search
  - Summary section showing total debits, credits, and net balance
  - Error handling with retry functionality
  - Loading indicators during data retrieval

## Integration Points

The General Ledger integrates with several other components of the application:

1. **Account System**: Ledger entries are associated with accounts via the account number
2. **Expense System**: Expenses generate corresponding general ledger entries
3. **Invoice System**: Invoices create appropriate ledger entries
4. **Company System**: Ledger entries are associated with specific companies

## Double-Entry Bookkeeping Implementation

The system follows double-entry bookkeeping principles, ensuring that:

1. For every transaction, the sum of debits equals the sum of credits
2. Each transaction affects at least two accounts (e.g., an expense affects both the expense account and the payment method account)

For example, when an expense is recorded:
- The expense account is debited (increased)
- The cash/bank/credit card account is credited (decreased)

## Test Coverage

The General Ledger feature includes:

1. **Unit tests** for `GeneralLedgerViewModel`, covering:
   - Retrieving all entries
   - Filtering entries by account
   - Filtering entries by date range
   - Error handling

2. **Widget tests** for `GeneralLedgerScreen`, testing:
   - Loading state and indicators
   - Displaying ledger entries
   - Filtering functionality
   - Error handling and recovery
   - Empty state handling

## Error Handling

Error handling for the General Ledger follows the application's centralized approach:

1. Errors are caught and processed by the `ErrorHandler` class
2. User-friendly error messages are displayed in the UI
3. Retry functionality allows users to attempt failed operations again

## Future Enhancements

Potential enhancements for the General Ledger feature include:

1. **Advanced Filtering**: Additional filter options such as by transaction type or amount range
2. **Reporting**: Enhanced reporting capabilities, including exporting to various formats (CSV, PDF)
3. **Journal Entry**: Direct creation and editing of journal entries
4. **Reconciliation**: Account reconciliation functionality
5. **Audit Trail**: Enhanced tracking of changes for audit purposes

## Mock Data

For development and testing purposes, the General Ledger system provides mock data through the application's mock data framework, generating realistic entries across various account types such as:

- Cash accounts
- Accounts receivable
- Revenue accounts
- Expense accounts

This allows for development and testing without requiring a live database connection. 