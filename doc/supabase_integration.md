# Supabase Integration

## Overview

This document describes how We Like Money integrates with Supabase as its backend service. Supabase provides a PostgreSQL database, authentication, and real-time capabilities.

## Configuration

### Environment Setup

The application uses environment variables for Supabase configuration:

```
# .env.example
SUPABASE_URL=your-project-url.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

These variables are loaded using `flutter_dotenv`:

```dart
await dotenv.dotenv.load();
```

### Supabase Initialization

Supabase is initialized during app startup:

```dart
/// Initializes Supabase with environment variables.
///
/// This function should be called during app startup before
/// any Supabase-dependent services are used.
Future<void> initializeSupabase() async {
  final url = dotenv.dotenv.get('SUPABASE_URL');
  final anonKey = dotenv.dotenv.get('SUPABASE_ANON_KEY');
  
  await Supabase.initialize(
    url: url,
    anonKey: anon<PERSON><PERSON>,
  );
}
```

This is called in the `main.dart` file:

```dart
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  await dotenv.dotenv.load();
  
  // Initialize Supabase
  await initializeSupabase();
  
  // Initialize dependency injection
  await configureDependencies();
  
  runApp(const App());
}
```

### Supabase Client Provider

A provider class is used to make the Supabase client available for dependency injection:

```dart
/// Provides the Supabase client as a singleton.
@singleton
@injectable
class SupabaseClientProvider {
  /// Returns the Supabase client instance.
  SupabaseClient getClient() {
    return Supabase.instance.client;
  }
}
```

This approach:
- Initializes Supabase with environment variables
- Provides a singleton client provider for dependency injection
- Ensures initialization before app startup

## Database Service

### Interface

The `DatabaseService` interface defines the contract for database operations:

```dart
abstract class DatabaseService {
  // Account operations
  Future<List<Account>> getAccounts();
  Future<Account?> getAccountByNumber(String accountNumber);
  Future<Account> createAccount(Account account);
  Future<Account> updateAccount(Account account);
  Future<void> deleteAccount(String accountNumber);
  
  // Other entity operations...
}
```

This interface:
- Provides a clean API for database operations
- Abstracts the underlying database implementation
- Enables easy testing with mock implementations

### Implementation

The `SupabaseDatabaseService` implements the database operations using Supabase:

```dart
@Injectable(as: DatabaseService)
class SupabaseDatabaseService implements DatabaseService {
  final SupabaseClient _client;
  final ErrorHandler _errorHandler;

  SupabaseDatabaseService(SupabaseClientProvider clientProvider, this._errorHandler)
      : _client = clientProvider.getClient();

  // Helper method for error handling
  Future<T> _executeDbOperation<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } on PostgrestException catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    } catch (e) {
      final errorMessage = _errorHandler.handleError(e);
      throw BusinessException(errorMessage);
    }
  }

  @override
  Future<List<Account>> getAccounts() async {
    return _executeDbOperation(() async {
      final response = await _client.from('accounts').select();
      return (response as List).map((json) => Account.fromJson(json)).toList();
    });
  }

  // Other implementations...
}
```

Key features:
- Uses dependency injection for `SupabaseClientProvider` and `ErrorHandler`
- Implements the `DatabaseService` interface
- Uses a helper method for consistent error handling
- Transforms Supabase responses into domain models

## Query Operations

### Fetching Data

```dart
Future<List<Account>> getAccounts() async {
  return _executeDbOperation(() async {
    final response = await _client.from('accounts').select();
    return (response as List).map((json) => Account.fromJson(json)).toList();
  });
}
```

### Fetching a Single Record

```dart
Future<Account?> getAccountByNumber(String accountNumber) async {
  return _executeDbOperation(() async {
    try {
      final response = await _client
          .from('accounts')
          .select()
          .eq('account_number', accountNumber)
          .single();
      return Account.fromJson(response);
    } on PostgrestException catch (e) {
      if (e.message.contains('Row not found')) {
        return null;
      }
      rethrow;
    }
  });
}
```

### Creating Records

```dart
Future<Account> createAccount(Account account) async {
  return _executeDbOperation(() async {
    final response = await _client
        .from('accounts')
        .insert(account.toJson())
        .select()
        .single();
    return Account.fromJson(response);
  });
}
```

### Updating Records

```dart
Future<Account> updateAccount(Account account) async {
  return _executeDbOperation(() async {
    final response = await _client
        .from('accounts')
        .update(account.toJson())
        .eq('account_number', account.accountNumber)
        .select()
        .single();
    return Account.fromJson(response);
  });
}
```

### Deleting Records

```dart
Future<void> deleteAccount(String accountNumber) async {
  return _executeDbOperation(() async {
    await _client
        .from('accounts')
        .delete()
        .eq('account_number', accountNumber);
  });
}
```

## Error Handling

Supabase-specific errors are handled in the `ErrorHandler` class:

```dart
String _handleSupabaseError(PostgrestException error) {
  if (error.code == 'PGRST116') {
    return 'Resource not found.';
  } else if (error.code == 'PGRST109') {
    return 'Database conflict. The operation could not be completed.';
  } else if (error.code == '23505') {
    return 'A record with this information already exists.';
  } else if (error.code == '23503') {
    return 'This operation would violate database constraints.';
  } else if (error.message.contains('JWT')) {
    return 'Your session has expired. Please log in again.';
  } else {
    return kDebugMode
        ? 'Database error: ${error.message} (Code: ${error.code})'
        : 'A database error occurred. Please try again later.';
  }
}
```

This approach:
- Handles common Supabase error codes
- Provides user-friendly error messages
- Includes different messages for debug vs. production

## Best Practices

1. **Use Transactions** for operations that modify multiple tables
2. **Implement Retry Logic** for transient errors
3. **Handle Not Found** cases gracefully
4. **Validate Data** before sending to Supabase
5. **Use Proper Error Handling** for all database operations
6. **Implement Pagination** for large result sets
7. **Use Proper Indexing** for frequently queried fields

## Database Schema

The application uses the following tables in Supabase:

| Table | Primary Key | Description |
|-------|------------|-------------|
| accounts | account_number | Chart of accounts |
| currencies | code | Currency definitions |
| exchange_rates | id | Exchange rates between currencies |
| general_ledger | id | General ledger entries |
| customers | id | Customer information |
| vendors | id | Vendor information |
| invoices | id | Customer invoices |
| expenses | id | Vendor expenses |
| payments_in | id | Customer payments |
| payments_out | id | Vendor payments |

## Security Considerations

1. **Row-Level Security (RLS)** policies should be implemented in Supabase
2. **Environment Variables** should be used for sensitive configuration
3. **Input Validation** should be performed before database operations
4. **Error Messages** should not expose sensitive information in production
5. **Authentication** should be required for all database operations 