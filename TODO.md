# WeLikeMoney Project TODO List

## 🔥 Today's Focus (April 11, 2023)
- [x] Fix linter warnings in the company selector test
- [x] Review conditional testing implementation
- [x] Create local .env.test file
- [x] Improve .env.test file loading with fallback and search in multiple locations
- [x] Verify tests work correctly with the improved environment loading
- [x] Fix online testing with Supabase by implementing custom in-memory storage
- [x] Implement fallback mock client for online testing when SharedPreferences isn't available
- [x] Update documentation with conditional testing improvements
- [x] Run tests as part of the local pre-commit check
- [x] Fix failures in integration tests
- [x] Set up true online testing with integration tests

## 🚀 Current Sprint Tasks

### Conditional Testing Implementation ✅
- [x] Set up environment variables for conditional testing
- [x] Create TestConfig class for managing test configuration
- [x] Implement SupabaseTestHelper for providing test clients
- [x] Create mock data for offline testing
- [x] Update test files to support both online and offline modes
- [x] Create run_tests.sh script for running tests in different modes
- [x] Document conditional testing approach
- [x] Add support for fallback mock client when platform channels aren't available
- [x] Update README.md with testing information

### Integration Testing Improvements 🚧
- [x] Set up integration_test package and configuration
- [x] Create test driver for integration tests
- [x] Implement true online testing via integration tests
- [ ] Add GitHub workflow for running integration tests
- [x] Document integration testing approach

### Company Feature 🏢
- [x] Implement Company model
- [x] Add company CRUD operations to database service
- [x] Create CompanyViewModel
- [x] Implement company providers
- [x] Create CompanySelector widget
- [x] Create CompaniesScreen
- [x] Update project providers to be company-aware
- [x] Write tests for company-related components

### Next Steps 👣
- [ ] Update other providers to be company-aware:
  - [ ] StaffMemberProvider
  - [ ] AccountProvider
  - [ ] TransactionProvider
  - [ ] ReportProvider
- [ ] Implement company filtering in UI components:
  - [ ] ProjectsScreen
  - [ ] StaffScreen
  - [ ] AccountsScreen
  - [ ] ReportsScreen
- [ ] Add company selection to app drawer/navigation
- [ ] Create company dashboard with summary information
- [ ] Add company logo/image upload functionality
- [ ] Implement company settings page

## 📋 Backlog

### UI Improvements 🎨
- [ ] Implement dark mode support
- [ ] Create responsive layouts for different screen sizes
- [ ] Add animations for smoother transitions
- [ ] Improve accessibility features

### Performance Optimization ⚡
- [ ] Profile app performance
- [ ] Optimize database queries
- [ ] Implement caching for frequently accessed data
- [ ] Add pagination for large data sets

### Testing 🧪
- [ ] Increase test coverage to >80%
- [x] Support both online and offline testing modes
- [x] Implement fallback mechanisms for platform-dependent services
- [ ] Add integration tests for critical workflows
- [ ] Implement screenshot tests for UI components
- [ ] Set up CI/CD pipeline for automated testing

## 🐛 Bugs
- [ ] Fix timer issue in company selector test
- [x] Address linter warnings in generated mock files
- [ ] Fix navigation issue when returning from company screen
- [x] Fix handling of .env.test file when not found at runtime
- [x] Fix online testing with SharedPreferences MissingPluginException

## 💡 Ideas for Future Sprints
- [ ] Multi-currency support
- [ ] Export reports to PDF/Excel
- [ ] User roles and permissions
- [ ] Data visualization dashboards
- [ ] Mobile app notifications

## 📝 Notes
- Remember to update documentation when implementing new features
- Follow the established architecture patterns
- Keep the code DRY and modular
- Use the standard TODO format in code comments: `// TODO(username): Description`

---

## How to Use This TODO List

1. **Daily Planning**: Update the "Today's Focus" section at the start of each day
2. **Task Completion**: Mark tasks as completed using `- [x]` syntax
3. **Sprint Planning**: Review and update the "Current Sprint Tasks" section weekly
4. **Bug Tracking**: Add new bugs to the "Bugs" section as they're discovered
5. **Ideas**: Capture new ideas in the "Ideas for Future Sprints" section

You can search for this file in Cursor using:
- Command Palette: `Cmd+P` then type `TODO.md`
- Terminal: `find . -name "TODO.md"` 