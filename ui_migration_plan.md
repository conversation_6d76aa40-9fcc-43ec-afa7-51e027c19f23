# UI Directory Structure Migration Plan

## Overview

This document outlines the plan for consolidating the UI directory structure in the WeLikeMoney application. The goal is to create a more consistent and organized structure for UI components.

## New Directory Structure

```
lib/ui/
  ├── common/                  # Shared UI components
  │   ├── widgets/             # Reusable widgets
  │   ├── dialogs/             # Reusable dialogs
  │   └── layouts/             # Reusable layouts
  ├── forms/                   # Form components (existing)
  │   ├── fields/              # Form fields
  │   └── sections/            # Form sections
  └── features/                # Feature-specific UI components
      ├── accounts/            # Account-related screens and components
      ├── expenses/            # Expense-related screens and components
      ├── general_ledger/      # General ledger screens and components
      ├── home/                # Home screen
      ├── settings/            # Settings screen
      ├── vendors/             # Vendor-related screens and components
      └── vendor_invoices/     # Vendor invoice screens and components
```

## File Migration Plan

### Widgets Migration

| Current Location | New Location |
|------------------|--------------|
| lib/widgets/custom_dropdown.dart | lib/ui/common/widgets/custom_dropdown.dart |
| lib/widgets/date_picker_field.dart | lib/ui/common/widgets/date_picker_field.dart |
| lib/widgets/error_display.dart | lib/ui/common/widgets/error_display.dart |
| lib/widgets/loading_display.dart | lib/ui/common/widgets/loading_display.dart |
| lib/ui/widgets/company_selector.dart | lib/ui/common/widgets/company_selector.dart |
| lib/ui/widgets/connection_status.dart | lib/ui/common/widgets/connection_status.dart |
| lib/ui/widgets/currency_display.dart | lib/ui/common/widgets/currency_display.dart |
| lib/ui/widgets/form_fields.dart | lib/ui/common/widgets/form_fields.dart |

### Account Feature Migration

| Current Location | New Location |
|------------------|--------------|
| lib/screens/account/account_list_screen.dart | lib/ui/features/accounts/account_list_screen.dart |
| lib/screens/account/account_list_screen_refactored.dart | lib/ui/features/accounts/account_list_screen_refactored.dart |
| lib/screens/account/account_register_screen.dart | lib/ui/features/accounts/account_register_screen.dart |
| lib/screens/account/components/account_list_item.dart | lib/ui/features/accounts/components/account_list_item.dart |
| lib/screens/account/components/account_search_bar.dart | lib/ui/features/accounts/components/account_search_bar.dart |
| lib/screens/account/components/empty_accounts_view.dart | lib/ui/features/accounts/components/empty_accounts_view.dart |
| lib/screens/account/components/error_view.dart | lib/ui/features/accounts/components/error_view.dart |
| lib/screens/account/components/loading_view.dart | lib/ui/features/accounts/components/loading_view.dart |
| lib/ui/screens/account_details_screen.dart | lib/ui/features/accounts/account_details_screen.dart |
| lib/ui/screens/accounts_screen.dart | lib/ui/features/accounts/accounts_screen.dart |

### General Ledger Feature Migration

| Current Location | New Location |
|------------------|--------------|
| lib/screens/general_ledger/general_ledger_screen_refactored.dart | lib/ui/features/general_ledger/general_ledger_screen.dart |
| lib/screens/general_ledger/components/empty_ledger_view.dart | lib/ui/features/general_ledger/components/empty_ledger_view.dart |
| lib/screens/general_ledger/components/error_view.dart | lib/ui/features/general_ledger/components/error_view.dart |
| lib/screens/general_ledger/components/filter_bar.dart | lib/ui/features/general_ledger/components/filter_bar.dart |
| lib/screens/general_ledger/components/ledger_table.dart | lib/ui/features/general_ledger/components/ledger_table.dart |
| lib/screens/general_ledger/components/loading_view.dart | lib/ui/features/general_ledger/components/loading_view.dart |
| lib/screens/general_ledger/components/summary_card.dart | lib/ui/features/general_ledger/components/summary_card.dart |

### Vendor Feature Migration

| Current Location | New Location |
|------------------|--------------|
| lib/screens/vendor/vendor_detail_screen.dart | lib/ui/features/vendors/vendor_detail_screen.dart |
| lib/screens/vendor/vendor_detail_screen_refactored.dart | lib/ui/features/vendors/vendor_detail_screen_refactored.dart |
| lib/screens/vendor/vendor_list_screen.dart | lib/ui/features/vendors/vendor_list_screen.dart |
| lib/screens/vendor/vendor_quick_create_dialog.dart | lib/ui/features/vendors/vendor_quick_create_dialog.dart |
| lib/screens/vendor/components/address_section.dart | lib/ui/features/vendors/components/address_section.dart |
| lib/screens/vendor/components/banking_info_section.dart | lib/ui/features/vendors/components/banking_info_section.dart |
| lib/screens/vendor/components/basic_info_section.dart | lib/ui/features/vendors/components/basic_info_section.dart |
| lib/screens/vendor/components/contact_info_section.dart | lib/ui/features/vendors/components/contact_info_section.dart |
| lib/screens/vendor/components/save_button_section.dart | lib/ui/features/vendors/components/save_button_section.dart |

### Vendor Invoice Feature Migration

| Current Location | New Location |
|------------------|--------------|
| lib/screens/vendor_invoice/vendor_invoice_detail_screen.dart | lib/ui/features/vendor_invoices/vendor_invoice_detail_screen.dart |
| lib/screens/vendor_invoice/vendor_invoice_entry_screen.dart | lib/ui/features/vendor_invoices/vendor_invoice_entry_screen.dart |
| lib/screens/vendor_invoice/vendor_invoice_form_state.dart | lib/ui/features/vendor_invoices/vendor_invoice_form_state.dart |
| lib/screens/vendor_invoice/vendor_invoice_list_screen.dart | lib/ui/features/vendor_invoices/vendor_invoice_list_screen.dart |
| lib/screens/vendor_invoice/vendor_invoice_view_model.dart | lib/ui/features/vendor_invoices/vendor_invoice_view_model.dart |
| lib/screens/vendor_invoice/components/accounting_section.dart | lib/ui/features/vendor_invoices/components/accounting_section.dart |
| lib/screens/vendor_invoice/components/invoice_details_section.dart | lib/ui/features/vendor_invoices/components/invoice_details_section.dart |
| lib/screens/vendor_invoice/components/invoice_filter_dialog.dart | lib/ui/features/vendor_invoices/components/invoice_filter_dialog.dart |
| lib/screens/vendor_invoice/components/invoice_list_item.dart | lib/ui/features/vendor_invoices/components/invoice_list_item.dart |
| lib/screens/vendor_invoice/components/invoice_search_bar.dart | lib/ui/features/vendor_invoices/components/invoice_search_bar.dart |
| lib/screens/vendor_invoice/components/optional_fields_section.dart | lib/ui/features/vendor_invoices/components/optional_fields_section.dart |
| lib/screens/vendor_invoice/components/save_button.dart | lib/ui/features/vendor_invoices/components/save_button.dart |
| lib/screens/vendor_invoice/components/vendor_selection_section.dart | lib/ui/features/vendor_invoices/components/vendor_selection_section.dart |

### Expense Feature Migration

| Current Location | New Location |
|------------------|--------------|
| lib/ui/screens/expense/expense_entry_screen.dart | lib/ui/features/expenses/expense_entry_screen.dart |
| lib/ui/screens/expense/expense_entry_screen_refactored.dart | lib/ui/features/expenses/expense_entry_screen_refactored.dart |
| lib/ui/screens/expense/expense_form_state.dart | lib/ui/features/expenses/expense_form_state.dart |
| lib/ui/screens/expense/expense_view_model.dart | lib/ui/features/expenses/expense_view_model.dart |
| lib/ui/screens/expense/components/expense_details_section.dart | lib/ui/features/expenses/components/expense_details_section.dart |
| lib/ui/screens/expense/components/form_section_header.dart | lib/ui/features/expenses/components/form_section_header.dart |
| lib/ui/screens/expense/components/payment_details_section.dart | lib/ui/features/expenses/components/payment_details_section.dart |
| lib/ui/screens/expense/components/project_staff_section.dart | lib/ui/features/expenses/components/project_staff_section.dart |
| lib/ui/screens/expense/components/save_button.dart | lib/ui/features/expenses/components/save_button.dart |
| lib/ui/screens/expense/components/vendor_selection_section.dart | lib/ui/features/expenses/components/vendor_selection_section.dart |
| lib/ui/screens/expense/components/refactored/expense_details_section.dart | lib/ui/features/expenses/components/refactored/expense_details_section.dart |
| lib/ui/screens/expense/components/refactored/payment_details_section.dart | lib/ui/features/expenses/components/refactored/payment_details_section.dart |
| lib/ui/screens/expense/components/refactored/project_staff_section.dart | lib/ui/features/expenses/components/refactored/project_staff_section.dart |
| lib/ui/screens/expense/components/refactored/vendor_section.dart | lib/ui/features/expenses/components/refactored/vendor_section.dart |
| lib/ui/screens/expenses_screen.dart | lib/ui/features/expenses/expenses_screen.dart |

### Other Screens Migration

| Current Location | New Location |
|------------------|--------------|
| lib/ui/screens/home_screen.dart | lib/ui/features/home/<USER>
| lib/ui/screens/settings_screen.dart | lib/ui/features/settings/settings_screen.dart |
| lib/ui/screens/companies_screen.dart | lib/ui/features/companies/companies_screen.dart |
| lib/ui/screens/projects_screen.dart | lib/ui/features/projects/projects_screen.dart |

## Form Sections Migration

| Current Location | New Location |
|------------------|--------------|
| lib/ui/forms/form_section.dart | lib/ui/forms/sections/form_section.dart |
| lib/ui/forms/form_section_header.dart | lib/ui/forms/sections/form_section_header.dart |

## Implementation Steps

1. Create the new directory structure
2. Move files to their new locations
3. Update imports in all files
4. Test the application to ensure everything works
5. Remove empty directories

## Testing Strategy

After migration, we should test:

1. Navigation between screens
2. Form submission
3. Data display
4. Component rendering

## Rollback Plan

If issues are encountered:

1. Revert all changes
2. Address issues
3. Try again with a more incremental approach
