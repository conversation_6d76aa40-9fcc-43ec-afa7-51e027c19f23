import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:platform/platform.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:we_like_money/config/supabase_config.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/config/app_config.dart';
import 'package:get_it/get_it.dart';
import 'package:we_like_money/models/company.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/services/logger.dart';
import 'package:we_like_money/services/database_service.dart';

// Create a test-specific SupabaseClientProvider that always returns our initialized client
class TestSupabaseClientProvider implements SupabaseClientProvider {
  final SupabaseClient _client;

  TestSupabaseClientProvider(this._client);

  @override
  SupabaseClient? getClient() => _client;
}

/// A secure environment loader that never stores sensitive credentials in source code
class EnvironmentLoader {
  final Map<String, String> _variables = {};
  final Logger _logger;

  EnvironmentLoader({Logger? logger})
    : _logger = logger ?? Logger(print: debugPrint, debugMode: true);

  /// Get a specific environment variable
  String? get(String key) => _variables[key];

  /// Get an environment variable with a default value
  String getEnvironmentVariable(String key, {String defaultValue = ''}) {
    return _variables[key] ?? defaultValue;
  }

  /// Load environment variables from a file or multiple possible locations
  Future<bool> loadFromFile({List<String>? searchPaths}) async {
    const platform = LocalPlatform();
    final homeDir =
        platform.environment['HOME'] ?? platform.environment['USERPROFILE'];
    final projectDir = '$homeDir/projects/WeLikeMoney/we_like_money';
    final searchLocations =
        searchPaths ??
        [
          '.env.test',
          '../.env.test',
          '../../.env.test',
          // Add project-independent paths
          '${Directory.current.path}/.env.test',
          '${Directory.current.parent.path}/.env.test',
          // Try specific platform-independent locations
          '$projectDir/.env.test',
        ];

    for (final location in searchLocations) {
      try {
        if (File(location).existsSync()) {
          _logger.info('🔄 Loading environment variables from $location');
          await dotenv.load(fileName: location);
          _logger.info('✅ Loaded environment variables from $location');

          // Copy variables to our internal map
          dotenv.env.forEach((key, value) {
            _variables[key] = value;
          });

          return true;
        }
      } catch (e) {
        // Continue to next location
        _logger.warning('⚠️ Failed to load from $location: $e');
      }
    }

    _logger.warning(
      '⚠️ Could not load environment variables from any location',
    );
    return false;
  }

  /// Check if testing can proceed with available variables
  bool canProceedWithTesting() {
    final requiredVariables = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];

    final missing =
        requiredVariables.where((variable) => get(variable) == null).toList();

    if (missing.isNotEmpty) {
      _logger.error(
        '❌ Missing required environment variables: ${missing.join(', ')}',
      );
      _logger.warning('⚠️ Please set these variables in a .env.test file');
      return false;
    }

    return true;
  }
}

Future<void> main() async {
  // Initialize the integration test framework
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  // Initialize logger
  final logger = Logger(print: debugPrint, debugMode: true);

  // Create our environment loader
  final env = EnvironmentLoader(logger: logger);

  // Load environment variables securely
  final environmentLoaded = await env.loadFromFile();
  if (!environmentLoaded || !env.canProceedWithTesting()) {
    logger.error('❌ Required environment variables are missing');
    logger.info(
      'ℹ️ Please create a .env.test file with SUPABASE_URL and SUPABASE_ANON_KEY',
    );
    return;
  }

  final supabaseUrl = env.get('SUPABASE_URL') ?? '';
  final supabaseAnonKey = env.get('SUPABASE_ANON_KEY') ?? '';

  // Force remote database mode for integration tests
  final appConfig = AppConfig();
  await appConfig.setDatabaseMode(DatabaseMode.remoteOnly);
  logger.info('✅ Forced remote database mode for integration test');

  // Initialize Supabase
  SupabaseClient? supabaseClient;
  try {
    // First check if Supabase is already initialized to avoid issues
    try {
      supabaseClient = Supabase.instance.client;
      logger.info('✅ Supabase is already initialized');
    } catch (e) {
      logger.info('🔄 Need to initialize Supabase: $e');
      // Initialize Supabase if not already initialized
      await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
      supabaseClient = Supabase.instance.client;
    }
    logger.info('✅ Supabase initialized successfully');
  } catch (e) {
    logger.error('❌ Failed to initialize Supabase: $e');
    return;
  }

  // Create our test provider
  final testProvider = TestSupabaseClientProvider(supabaseClient);

  // Initialize dependency injection with our test provider and AppConfig
  await configureDependencies(
    testProvider: testProvider,
    testProviders: <Type, Object>{AppConfig: appConfig},
    isTest: true,
  );

  group('Vendor Invoice Integration Tests', () {
    // Setup state for tests
    String? vendorId;
    int? companyId;
    int? vendorInvoiceId;
    final testVendorName =
        'Test Vendor ${DateTime.now().millisecondsSinceEpoch}';

    setUpAll(() async {
      // Create a test company first
      try {
        const company = Company(
          companyId: 1,
          companyName: 'Test Company',
          organizationNumber: 'ORG-123',
          phone: '+**********',
          email: '<EMAIL>',
          address: 'Test Address',
          zipCode: '12345',
          city: 'Test City',
          country: 'Test Country',
        );
        companyId = company.companyId;
        logger.info('✅ Created test company with ID: $companyId');

        // Create test accounts
        final testAccounts = [
          Account(
            accountName: 'Test Account 1',
            accountNumber: '1000',
            accountType: 'asset',
            companyId: companyId!,
          ),
          Account(
            accountName: 'Test Account 2',
            accountNumber: '2000',
            accountType: 'liability',
            companyId: companyId!,
          ),
          Account(
            accountName: 'Test Account 3',
            accountNumber: '3000',
            accountType: 'asset',
            companyId: companyId!,
          ),
        ];

        final databaseService = GetIt.instance<DatabaseService>();
        for (final account in testAccounts) {
          // Check if account exists
          logger.info('Checking if account ${account.accountNumber} exists...');
          final existingAccount = await databaseService.getAccountByNumber(
            account.accountNumber,
          );
          if (existingAccount == null) {
            logger.info('Creating account ${account.accountNumber}...');
            final createdAccount = await databaseService.createAccount(account);
            logger.info(
              'Created account: ${createdAccount.accountNumber} - ${createdAccount.accountName}',
            );
          }
        }
      } catch (e) {
        logger.error('❌ Error in setUpAll: $e');
        rethrow;
      }
    });

    testWidgets('Create and manage vendor invoices', (tester) async {
      try {
        final databaseService = GetIt.instance<DatabaseService>();

        // Create a test vendor
        final vendor = Vendor(
          vendorId: 'V${DateTime.now().millisecondsSinceEpoch}',
          vendorName: testVendorName,
          organizationNumber: 'VENDOR-123',
          phone: '+**********',
          email: '<EMAIL>',
          address: 'Vendor Address',
          zipCode: '54321',
          city: 'Vendor City',
          country: 'Vendor Country',
          companyId: companyId!,
        );

        logger.info('Creating test vendor: ${vendor.vendorName}');
        final createdVendor = await databaseService.createVendor(vendor);
        vendorId = createdVendor.vendorId;
        logger.info('Created vendor with ID: $vendorId');

        // Verify vendor was created
        expect(createdVendor.vendorName, equals(vendor.vendorName));
        expect(
          createdVendor.organizationNumber,
          equals(vendor.organizationNumber),
        );

        // Create a vendor invoice
        final vendorInvoice = VendorInvoice(
          invoiceId: 1,
          vendorId: vendorId!,
          invoiceNumber: 'INV-001',
          invoiceDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          amount: 1000.0,
          currencyCode: 'USD',
          expenseAccountNumber: '1000',
          companyId: companyId!,
        );

        logger.info('Creating vendor invoice: ${vendorInvoice.invoiceNumber}');
        final createdInvoice = await databaseService.createVendorInvoice(
          vendorInvoice,
        );
        vendorInvoiceId = createdInvoice.invoiceId;
        logger.info('Created vendor invoice with ID: $vendorInvoiceId');

        // Verify invoice was created
        expect(
          createdInvoice.invoiceNumber,
          equals(vendorInvoice.invoiceNumber),
        );
        expect(createdInvoice.amount, equals(vendorInvoice.amount));

        // Update the invoice
        final updatedInvoice = VendorInvoice(
          invoiceId: vendorInvoiceId!,
          vendorId: vendorId!,
          invoiceNumber: 'INV-001-UPDATED',
          invoiceDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 45)),
          amount: 1500.0,
          currencyCode: 'USD',
          expenseAccountNumber: '1000',
          companyId: companyId!,
        );

        logger.info('Updating vendor invoice: ${updatedInvoice.invoiceNumber}');
        final updated = await databaseService.updateVendorInvoice(
          updatedInvoice,
        );
        logger.info('Updated vendor invoice with ID: ${updated.invoiceId}');

        // Verify invoice was updated
        expect(updated.invoiceNumber, equals(updatedInvoice.invoiceNumber));
        expect(updated.amount, equals(updatedInvoice.amount));
        expect(updated.isPaid, equals(updatedInvoice.isPaid));

        // Delete the invoice
        logger.info('Deleting vendor invoice with ID: $vendorInvoiceId');
        await databaseService.deleteVendorInvoice(vendorInvoiceId!);
        logger.info('Deleted vendor invoice successfully');

        // Verify invoice was deleted
        final deletedInvoice = await databaseService.getVendorInvoiceById(
          vendorInvoiceId!,
        );
        expect(deletedInvoice, isNull);

        // Clean up vendor
        logger.info('Deleting vendor with ID: $vendorId');
        await databaseService.deleteVendor(vendorId!);
        logger.info('Deleted vendor successfully');

        // Verify vendor was deleted
        final deletedVendor = await databaseService.getVendorById(vendorId!);
        expect(deletedVendor, isNull);
      } catch (e) {
        logger.error('❌ Error in test: $e');
        rethrow;
      }
    });
  });
}
