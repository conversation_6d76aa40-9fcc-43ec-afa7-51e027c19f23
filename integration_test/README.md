# Integration Testing Guide

## Overview
This directory contains integration tests for the WeLikeMoney application. These tests validate that the application interacts correctly with external services like Supabase.

## Environment Setup

### Environment Variables
Integration tests require a `.env.test` file with your test environment credentials.

1. Copy the `.env.test.example` file in the project root to create a `.env.test` file
2. Update the variables with your actual test credentials:

```
# Supabase configuration
SUPABASE_URL=your-test-supabase-url
SUPABASE_ANON_KEY=your-test-anon-key

# Test configuration
TEST_MODE=online
ENABLE_MOCK_FALLBACK=true
```

**IMPORTANT: Never commit actual credentials to version control!**

The `.env.test` file should be listed in your `.gitignore` to prevent accidentally committing sensitive information.

### Running Tests

To run the integration tests:

```bash
flutter test integration_test/supabase_online_test.dart -d macos
```

This will:
1. Load environment variables from the `.env.test` file
2. Initialize a Supabase client with your test credentials
3. Run the integration tests against your test environment

### Mock Mode

The tests include a "mock mode" fallback that activates when:
- Network connections are restricted
- Supabase credentials are unavailable
- Environment variables can't be loaded

In mock mode, the tests will still run, but will use predefined test data instead of making actual network requests.

## Test Structure

Each integration test follows this pattern:

1. **Setup**: Initialize services with test credentials
2. **Test**: Perform operations against external services
3. **Verification**: Verify that operations produced expected results
4. **Cleanup**: Remove any test data created during testing

## Troubleshooting

If tests fail with "Missing environment variables":
1. Verify your `.env.test` file exists and contains the required variables
2. Check that the path to the file is accessible from the test environment
3. Ensure credentials have appropriate permissions

If you see network connection errors:
1. Verify your network connection
2. Check that your test credentials are valid
3. The test will automatically fall back to mock mode if network requests are restricted

## Maintaining Tests

When adding new integration tests:
1. Follow the existing pattern of gracefully handling connection issues
2. Always check for the presence of required environment variables
3. Include fallback behavior for when external services are unavailable
4. Clean up any test data created during testing

## Adding New Environment Variables

If you need to add new environment variables for tests:
1. Add them to `.env.test.example` with placeholder values
2. Document the variables in this README
3. Add them to your personal `.env.test` file
4. Do NOT modify `.env.test` in version control 