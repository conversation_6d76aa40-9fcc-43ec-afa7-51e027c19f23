import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:we_like_money/app.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:platform/platform.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    // Print directory for debugging
    final currentDir = Directory.current.path;
    debugPrint('📂 Current directory: $currentDir');

    // Try to load environment variables from multiple potential locations
    bool envLoaded = false;
    final searchLocations = [
      // Try specific paths first
      '.env.test',
      '../.env.test',
      '../../.env.test',
      '$currentDir/.env.test',
      '${Directory.current.parent.path}/.env.test',
      // Try to locate the file in platform-specific common locations
      if (const LocalPlatform().environment.containsKey('HOME'))
        '${const LocalPlatform().environment['HOME']}/projects/WeLikeMoney/we_like_money/.env.test',
    ];

    for (final location in searchLocations) {
      try {
        debugPrint('🔄 Loading environment variables from $location');
        await dotenv.load(fileName: location);
        debugPrint('✅ Loaded environment variables from $location');
        envLoaded = true;
        break;
      } catch (e) {
        // Continue to the next location
        continue;
      }
    }

    if (!envLoaded) {
      debugPrint('⚠️ Could not load .env.test file from any location');
      debugPrint(
        '⚠️ App may not function correctly without environment variables',
      );
    }
  });

  group('App Integration Tests', () {
    testWidgets('App can start and basic UI elements are visible', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(const App());

      // Wait for app to fully load
      await tester.pumpAndSettle();

      // Verify the app starts without crashing and basic UI elements are visible
      expect(find.byType(App), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // Find and tap on the Projects navigation item if it exists
      // If it's in a drawer, we need to open the drawer first
      final drawerButton = find.byIcon(Icons.menu);
      if (drawerButton.evaluate().isNotEmpty) {
        await tester.tap(drawerButton);
        await tester.pumpAndSettle();
      }

      // Look for Projects navigation item or any other navigation elements
      // This is a more flexible approach that doesn't assume a specific navigation structure
      final projectsNavItem = find.text('Projects');
      if (projectsNavItem.evaluate().isNotEmpty) {
        await tester.tap(projectsNavItem);
        await tester.pumpAndSettle();

        // Verify we're on the Projects screen or at least some UI elements are visible
        expect(find.byType(Scaffold), findsOneWidget);

        // Test UI elements that should be present regardless of data
        expect(find.byType(AppBar), findsOneWidget);
      } else {
        // If we can't find the Projects navigation item, just verify the app is running
        debugPrint(
          'Projects navigation item not found. Verifying app is running.',
        );
        expect(find.byType(Scaffold), findsOneWidget);
      }
    });

    testWidgets('App shows appropriate UI for empty or error states', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(const App());

      // Wait for app to fully load
      await tester.pumpAndSettle();

      // This test verifies that the app shows appropriate UI for empty or error states
      // Since we can't easily simulate these states in an integration test,
      // we'll just verify that the app doesn't crash

      expect(find.byType(App), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);

      // This is a placeholder for more sophisticated error state testing
      expect(true, isTrue, reason: 'App loaded without crashing');
    });
  });
}
