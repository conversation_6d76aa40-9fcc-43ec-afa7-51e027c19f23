#!/bin/bash

# Clean up old directories after migration

echo "This script will remove the old directories after migration."
echo "Make sure the application is working correctly before running this script."
echo "Press Enter to continue or Ctrl+C to cancel..."
read

# Remove old directories
echo "Removing old directories..."

# Remove old screens directory
rm -rf lib/screens

# Remove old UI screens directory
rm -rf lib/ui/screens

# Remove old widgets directory
rm -rf lib/widgets

# Remove old UI widgets directory
rm -rf lib/ui/widgets

# Remove old form section files
rm -f lib/ui/forms/form_section.dart
rm -f lib/ui/forms/form_section_header.dart

echo "Cleanup complete."
